{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "فواتير المشتريات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<style>
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
    
    .invoice-row {
        transition: all 0.2s;
    }
    
    .invoice-row:hover {
        background-color: #f8f9fa;
    }
    
    .invoice-card {
        transition: all 0.3s;
        height: 100%;
    }
    
    .invoice-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .invoice-card .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }
    
    .invoice-card .card-footer {
        background-color: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "فواتير المشتريات" %}</h1>
    <div>
        <a href="{% url 'purchases:purchase_orders' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى طلبات الشراء" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية النتائج" %}</h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases:invoices' %}" method="get" id="filterForm">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="status" class="form-label">{% trans "الحالة" %}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{% trans "الكل" %}</option>
                        {% for status_code, status_name in status_choices %}
                        <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>{{ status_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">{% trans "الكل" %}</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier_filter == supplier.id|stringformat:"i" %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                    </button>
                    <a href="{% url 'purchases:invoices' %}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> {% trans "إعادة ضبط" %}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Invoices -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة الفواتير" %}</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">{% trans "خيارات العرض" %}:</div>
                <a class="dropdown-item view-mode" href="#" data-mode="table">
                    <i class="fas fa-table me-1"></i> {% trans "عرض كجدول" %}
                </a>
                <a class="dropdown-item view-mode" href="#" data-mode="cards">
                    <i class="fas fa-th-large me-1"></i> {% trans "عرض كبطاقات" %}
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{% url 'purchases:reports' %}">
                    <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Table View (Default) -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="invoicesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <th>{% trans "المورد" %}</th>
                            <th>{% trans "رقم الطلب" %}</th>
                            <th>{% trans "تاريخ الفاتورة" %}</th>
                            <th>{% trans "تاريخ الاستحقاق" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr class="invoice-row">
                            <td>
                                <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}">
                                    {{ invoice.invoice_number }}
                                </a>
                            </td>
                            <td>
                                <a href="{% url 'purchases:view_supplier' supplier_id=invoice.purchase.supplier.id %}">
                                    {{ invoice.purchase.supplier.name }}
                                </a>
                            </td>
                            <td>
                                <a href="{% url 'purchases:view_purchase' purchase_id=invoice.purchase.id %}">
                                    {{ invoice.purchase.reference_number }}
                                </a>
                            </td>
                            <td>{{ invoice.invoice_date|date:"Y-m-d" }}</td>
                            <td>
                                {% if invoice.due_date %}
                                    {{ invoice.due_date|date:"Y-m-d" }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ invoice.amount|floatformat:2 }} ر.س</td>
                            <td>
                                {% if invoice.status == 'pending' %}
                                <span class="badge bg-warning text-dark status-badge">{% trans "معلقة" %}</span>
                                {% elif invoice.status == 'verified' %}
                                <span class="badge bg-info status-badge">{% trans "تم التحقق" %}</span>
                                {% elif invoice.status == 'paid' %}
                                <span class="badge bg-success status-badge">{% trans "مدفوعة" %}</span>
                                {% elif invoice.status == 'cancelled' %}
                                <span class="badge bg-danger status-badge">{% trans "ملغية" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'purchases:edit_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if invoice.status != 'paid' %}
                                    <a href="{% url 'purchases:add_payment' purchase_id=invoice.purchase.id %}" class="btn btn-sm btn-success" title="{% trans 'دفع' %}">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{% url 'purchases:delete_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">{% trans "لا توجد فواتير" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Cards View (Hidden by default) -->
        <div id="cardsView" class="d-none">
            <div class="row">
                {% for invoice in invoices %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card invoice-card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{{ invoice.invoice_number }}</h5>
                                <span class="badge {% if invoice.status == 'pending' %}bg-warning text-dark{% elif invoice.status == 'verified' %}bg-info{% elif invoice.status == 'paid' %}bg-success{% elif invoice.status == 'cancelled' %}bg-danger{% endif %}">
                                    {% if invoice.status == 'pending' %}{% trans "معلقة" %}
                                    {% elif invoice.status == 'verified' %}{% trans "تم التحقق" %}
                                    {% elif invoice.status == 'paid' %}{% trans "مدفوعة" %}
                                    {% elif invoice.status == 'cancelled' %}{% trans "ملغية" %}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                <strong>{% trans "المورد" %}:</strong> {{ invoice.purchase.supplier.name }}<br>
                                <strong>{% trans "رقم الطلب" %}:</strong> {{ invoice.purchase.reference_number }}<br>
                                <strong>{% trans "تاريخ الفاتورة" %}:</strong> {{ invoice.invoice_date|date:"Y-m-d" }}<br>
                                {% if invoice.due_date %}
                                <strong>{% trans "تاريخ الاستحقاق" %}:</strong> {{ invoice.due_date|date:"Y-m-d" }}<br>
                                {% endif %}
                                <strong>{% trans "المبلغ" %}:</strong> {{ invoice.amount|floatformat:2 }} ر.س
                            </p>
                            {% if invoice.invoice_file %}
                            <a href="{{ invoice.invoice_file.url }}" target="_blank" class="btn btn-sm btn-info">
                                <i class="fas fa-file-download me-1"></i> {% trans "تحميل الفاتورة" %}
                            </a>
                            {% endif %}
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100">
                                <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i> {% trans "عرض" %}
                                </a>
                                <a href="{% url 'purchases:edit_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                                </a>
                                {% if invoice.status != 'paid' %}
                                <a href="{% url 'purchases:add_payment' purchase_id=invoice.purchase.id %}" class="btn btn-sm btn-success">
                                    <i class="fas fa-money-bill-wave me-1"></i> {% trans "دفع" %}
                                </a>
                                {% endif %}
                                <a href="{% url 'purchases:delete_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info">
                        {% trans "لا توجد فواتير. قم بإضافة فاتورة جديدة." %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // استدعاء دالة تهيئة القوائم المنسدلة من navbar-fix.js
        if (typeof initializeNavbarDropdowns === 'function') {
            initializeNavbarDropdowns();
        }
    });

    $(document).ready(function() {
        // Initialize DataTable
        var table = $('#invoicesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "order": [[3, "desc"]],
            "pageLength": 25
        });
        
        // Toggle view mode
        $('.view-mode').click(function(e) {
            e.preventDefault();
            var mode = $(this).data('mode');
            
            if (mode === 'table') {
                $('#tableView').removeClass('d-none');
                $('#cardsView').addClass('d-none');
            } else if (mode === 'cards') {
                $('#tableView').addClass('d-none');
                $('#cardsView').removeClass('d-none');
            }
        });
        
        // Auto-submit form on select change
        $('#status, #supplier').change(function() {
            $('#filterForm').submit();
        });
    });
</script>
{% endblock %}
