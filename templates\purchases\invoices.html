{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "فواتير المشتريات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
    
    .invoice-row {
        transition: all 0.2s;
    }
    
    .invoice-row:hover {
        background-color: #f8f9fa;
    }
    
    .invoice-card {
        transition: all 0.3s;
        height: 100%;
    }
    
    .invoice-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .invoice-card .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }
    
    .invoice-card .card-footer {
        background-color: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-file-invoice me-3"></i>
                        {% trans "فواتير المشتريات" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "إدارة شاملة لفواتير المشتريات مع تتبع حالات الدفع والاستحقاق وإدارة المدفوعات" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <a href="{% url 'purchases:purchase_orders' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-clipboard-list me-1"></i>
                                {% trans "طلبات الشراء" %}
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "أدوات" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'purchases:suppliers' %}">
                                        <i class="fas fa-truck text-primary me-2"></i>{% trans "إدارة الموردين" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:payments' %}">
                                        <i class="fas fa-credit-card text-success me-2"></i>{% trans "المدفوعات" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportInvoices()">
                                        <i class="fas fa-file-export text-success me-2"></i>{% trans "تصدير الفواتير" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="printInvoicesList()">
                                        <i class="fas fa-print text-secondary me-2"></i>{% trans "طباعة القائمة" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="overdueInvoicesReport()">
                                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>{% trans "الفواتير المتأخرة" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="paymentStatusReport()">
                                        <i class="fas fa-chart-pie text-info me-2"></i>{% trans "تقرير حالات الدفع" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="bulkInvoiceActions()">
                                        <i class="fas fa-tasks text-secondary me-2"></i>{% trans "إجراءات مجمعة" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendPaymentReminders()">
                                        <i class="fas fa-envelope text-danger me-2"></i>{% trans "تذكير بالدفع" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'purchases:index' %}">
                                            <i class="fas fa-shopping-bag me-1"></i>{% trans "إدارة المشتريات" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "فواتير المشتريات" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية النتائج" %}</h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases:invoices' %}" method="get" id="filterForm">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="status" class="form-label">{% trans "الحالة" %}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{% trans "الكل" %}</option>
                        {% for status_code, status_name in status_choices %}
                        <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>{{ status_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">{% trans "الكل" %}</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier_filter == supplier.id|stringformat:"i" %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                    </button>
                    <a href="{% url 'purchases:invoices' %}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> {% trans "إعادة ضبط" %}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Invoices -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة الفواتير" %}</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">{% trans "خيارات العرض" %}:</div>
                <a class="dropdown-item view-mode" href="#" data-mode="table">
                    <i class="fas fa-table me-1"></i> {% trans "عرض كجدول" %}
                </a>
                <a class="dropdown-item view-mode" href="#" data-mode="cards">
                    <i class="fas fa-th-large me-1"></i> {% trans "عرض كبطاقات" %}
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{% url 'purchases:reports' %}">
                    <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Table View (Default) -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="invoicesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <th>{% trans "المورد" %}</th>
                            <th>{% trans "رقم الطلب" %}</th>
                            <th>{% trans "تاريخ الفاتورة" %}</th>
                            <th>{% trans "تاريخ الاستحقاق" %}</th>
                            <th>{% trans "المبلغ" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr class="invoice-row">
                            <td>
                                <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}">
                                    {{ invoice.invoice_number }}
                                </a>
                            </td>
                            <td>
                                <a href="{% url 'purchases:view_supplier' supplier_id=invoice.purchase.supplier.id %}">
                                    {{ invoice.purchase.supplier.name }}
                                </a>
                            </td>
                            <td>
                                <a href="{% url 'purchases:view_purchase' purchase_id=invoice.purchase.id %}">
                                    {{ invoice.purchase.reference_number }}
                                </a>
                            </td>
                            <td>{{ invoice.invoice_date|date:"Y-m-d" }}</td>
                            <td>
                                {% if invoice.due_date %}
                                    {{ invoice.due_date|date:"Y-m-d" }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ invoice.amount|floatformat:2 }} ر.س</td>
                            <td>
                                {% if invoice.status == 'pending' %}
                                <span class="badge bg-warning text-dark status-badge">{% trans "معلقة" %}</span>
                                {% elif invoice.status == 'verified' %}
                                <span class="badge bg-info status-badge">{% trans "تم التحقق" %}</span>
                                {% elif invoice.status == 'paid' %}
                                <span class="badge bg-success status-badge">{% trans "مدفوعة" %}</span>
                                {% elif invoice.status == 'cancelled' %}
                                <span class="badge bg-danger status-badge">{% trans "ملغية" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'purchases:edit_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if invoice.status != 'paid' %}
                                    <a href="{% url 'purchases:add_payment' purchase_id=invoice.purchase.id %}" class="btn btn-sm btn-success" title="{% trans 'دفع' %}">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{% url 'purchases:delete_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">{% trans "لا توجد فواتير" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Cards View (Hidden by default) -->
        <div id="cardsView" class="d-none">
            <div class="row">
                {% for invoice in invoices %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card invoice-card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{{ invoice.invoice_number }}</h5>
                                <span class="badge {% if invoice.status == 'pending' %}bg-warning text-dark{% elif invoice.status == 'verified' %}bg-info{% elif invoice.status == 'paid' %}bg-success{% elif invoice.status == 'cancelled' %}bg-danger{% endif %}">
                                    {% if invoice.status == 'pending' %}{% trans "معلقة" %}
                                    {% elif invoice.status == 'verified' %}{% trans "تم التحقق" %}
                                    {% elif invoice.status == 'paid' %}{% trans "مدفوعة" %}
                                    {% elif invoice.status == 'cancelled' %}{% trans "ملغية" %}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                <strong>{% trans "المورد" %}:</strong> {{ invoice.purchase.supplier.name }}<br>
                                <strong>{% trans "رقم الطلب" %}:</strong> {{ invoice.purchase.reference_number }}<br>
                                <strong>{% trans "تاريخ الفاتورة" %}:</strong> {{ invoice.invoice_date|date:"Y-m-d" }}<br>
                                {% if invoice.due_date %}
                                <strong>{% trans "تاريخ الاستحقاق" %}:</strong> {{ invoice.due_date|date:"Y-m-d" }}<br>
                                {% endif %}
                                <strong>{% trans "المبلغ" %}:</strong> {{ invoice.amount|floatformat:2 }} ر.س
                            </p>
                            {% if invoice.invoice_file %}
                            <a href="{{ invoice.invoice_file.url }}" target="_blank" class="btn btn-sm btn-info">
                                <i class="fas fa-file-download me-1"></i> {% trans "تحميل الفاتورة" %}
                            </a>
                            {% endif %}
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100">
                                <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i> {% trans "عرض" %}
                                </a>
                                <a href="{% url 'purchases:edit_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                                </a>
                                {% if invoice.status != 'paid' %}
                                <a href="{% url 'purchases:add_payment' purchase_id=invoice.purchase.id %}" class="btn btn-sm btn-success">
                                    <i class="fas fa-money-bill-wave me-1"></i> {% trans "دفع" %}
                                </a>
                                {% endif %}
                                <a href="{% url 'purchases:delete_invoice' invoice_id=invoice.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info">
                        {% trans "لا توجد فواتير. قم بإضافة فاتورة جديدة." %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportInvoices() {
        // تصدير فواتير المشتريات
        const invoices = [];
        $('#invoicesTable tbody tr').each(function() {
            const row = $(this);
            const invoice = {
                number: row.find('td:eq(0)').text().trim(),
                supplier: row.find('td:eq(1)').text().trim(),
                purchase_order: row.find('td:eq(2)').text().trim(),
                invoice_date: row.find('td:eq(3)').text().trim(),
                due_date: row.find('td:eq(4)').text().trim(),
                amount: row.find('td:eq(5)').text().trim(),
                status: row.find('td:eq(6)').text().trim()
            };
            invoices.push(invoice);
        });

        // إنشاء محتوى CSV
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "رقم الفاتورة,المورد,رقم طلب الشراء,تاريخ الفاتورة,تاريخ الاستحقاق,المبلغ,الحالة\n";

        invoices.forEach(function(invoice) {
            csvContent += '"' + invoice.number + '","' + invoice.supplier + '","' + invoice.purchase_order + '","' + invoice.invoice_date + '","' + invoice.due_date + '","' + invoice.amount + '","' + invoice.status + '"\n';
        });

        // تحميل الملف
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "purchase_invoices_" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        if (typeof toastr !== 'undefined') {
            toastr.success("{% trans 'تم تصدير فواتير المشتريات بنجاح' %}");
        }
    }

    function printInvoicesList() {
        // طباعة قائمة الفواتير
        window.print();
    }

    function overdueInvoicesReport() {
        // تقرير الفواتير المتأخرة
        if (typeof toastr !== 'undefined') {
            toastr.info("{% trans 'جاري إنشاء تقرير الفواتير المتأخرة...' %}");
        }

        // محاكاة إنشاء التقرير
        setTimeout(function() {
            const overdueCount = $('#invoicesTable tbody tr:contains("متأخر")').length;
            const totalOverdue = "{% trans 'إجمالي المبلغ المتأخر' %}";

            // إنشاء تقرير
            const reportWindow = window.open('', '_blank', 'width=800,height=600');
            reportWindow.document.write(`
                <html dir="rtl">
                <head><title>تقرير الفواتير المتأخرة</title></head>
                <body style="font-family: Arial; padding: 20px;">
                    <h1>تقرير الفواتير المتأخرة</h1>
                    <p>عدد الفواتير المتأخرة: ' + overdueCount + '</p>
                    <p>تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA') + '</p>
                    <hr>
                    <h3>الفواتير المتأخرة:</h3>
                    <div id="overdueList"></div>
                    <script>
                        // إضافة قائمة الفواتير المتأخرة
                        const overdueInvoices = [];
                        window.opener.document.querySelectorAll('#invoicesTable tbody tr').forEach(row => {
                            if (row.textContent.includes('متأخر')) {
                                const cells = row.querySelectorAll('td');
                                if (cells.length > 0) {
                                    overdueInvoices.push({
                                        number: cells[0].textContent.trim(),
                                        supplier: cells[1].textContent.trim(),
                                        amount: cells[5].textContent.trim(),
                                        dueDate: cells[4].textContent.trim()
                                    });
                                }
                            }
                        });

                        let listHtml = '<ul>';
                        overdueInvoices.forEach(function(invoice) {
                            listHtml += '<li>' + invoice.number + ' - ' + invoice.supplier + ' - ' + invoice.amount + ' (استحقاق: ' + invoice.dueDate + ')</li>';
                        });
                        listHtml += '</ul>';
                        document.getElementById('overdueList').innerHTML = listHtml;
                    </script>
                </body>
                </html>
            `);

            if (typeof toastr !== 'undefined') {
                toastr.success("{% trans 'تم إنشاء تقرير الفواتير المتأخرة بنجاح' %}");
            }
        }, 1500);
    }

    function paymentStatusReport() {
        // تقرير حالات الدفع
        if (typeof toastr !== 'undefined') {
            toastr.info("{% trans 'جاري إنشاء تقرير حالات الدفع...' %}");
        }

        setTimeout(function() {
            const paidCount = $('#invoicesTable tbody tr:contains("مدفوع")').length;
            const unpaidCount = $('#invoicesTable tbody tr:contains("غير مدفوع")').length;
            const partialCount = $('#invoicesTable tbody tr:contains("مدفوع جزئياً")').length;
            const totalInvoices = $('#invoicesTable tbody tr').length;

            // إنشاء تقرير دائري
            const reportWindow = window.open('', '_blank', 'width=600,height=500');
            reportWindow.document.write(`
                <html dir="rtl">
                <head><title>تقرير حالات الدفع</title></head>
                <body style="font-family: Arial; padding: 20px;">
                    <h1>تقرير حالات الدفع</h1>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <h3>إحصائيات الدفع:</h3>
                        <p>إجمالي الفواتير: ' + totalInvoices + '</p>
                        <p>مدفوع: ' + paidCount + ' (' + ((paidCount/totalInvoices)*100).toFixed(1) + '%)</p>
                        <p>غير مدفوع: ' + unpaidCount + ' (' + ((unpaidCount/totalInvoices)*100).toFixed(1) + '%)</p>
                        <p>مدفوع جزئياً: ' + partialCount + ' (' + ((partialCount/totalInvoices)*100).toFixed(1) + '%)</p>
                    </div>
                    <p>تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA') + '</p>
                </body>
                </html>
            `);

            if (typeof toastr !== 'undefined') {
                toastr.success("{% trans 'تم إنشاء تقرير حالات الدفع بنجاح' %}");
            }
        }, 1500);
    }

    function bulkInvoiceActions() {
        // إجراءات مجمعة على الفواتير
        const selectedInvoices = $('#invoicesTable tbody input[type="checkbox"]:checked');

        if (selectedInvoices.length === 0) {
            if (typeof toastr !== 'undefined') {
                toastr.warning("{% trans 'يرجى تحديد فاتورة واحدة على الأقل' %}");
            }
            return;
        }

        const actions = [
            "{% trans 'تحديث حالة الدفع' %}",
            "{% trans 'إرسال تذكير بالدفع' %}",
            "{% trans 'تصدير الفواتير المحددة' %}",
            "{% trans 'طباعة الفواتير المحددة' %}",
            "{% trans 'تحديد تاريخ استحقاق جديد' %}"
        ];

        let actionChoice = prompt("{% trans 'اختر الإجراء المطلوب:' %}\n" +
            actions.map(function(action, index) { return (index + 1) + '. ' + action; }).join('\n'));

        if (actionChoice) {
            const actionIndex = parseInt(actionChoice) - 1;
            if (actionIndex >= 0 && actionIndex < actions.length) {
                if (typeof toastr !== 'undefined') {
                    toastr.info("{% trans 'جاري تنفيذ:' %} " + actions[actionIndex]);
                }

                setTimeout(function() {
                    if (typeof toastr !== 'undefined') {
                        toastr.success("{% trans 'تم تنفيذ:' %} " + actions[actionIndex] + " {% trans 'على' %} " + selectedInvoices.length + " {% trans 'فاتورة' %}");
                    }
                }, 2000);
            }
        }
    }

    function sendPaymentReminders() {
        // إرسال تذكير بالدفع
        const unpaidInvoices = $('#invoicesTable tbody tr:contains("غير مدفوع")').length;
        const overdueInvoices = $('#invoicesTable tbody tr:contains("متأخر")').length;

        if (unpaidInvoices === 0 && overdueInvoices === 0) {
            if (typeof toastr !== 'undefined') {
                toastr.info("{% trans 'لا توجد فواتير تحتاج لتذكير بالدفع' %}");
            }
            return;
        }

        const reminderTypes = [
            "{% trans 'تذكير ودي للفواتير المستحقة' %}",
            "{% trans 'تذكير عاجل للفواتير المتأخرة' %}",
            "{% trans 'إشعار نهائي قبل اتخاذ إجراء قانوني' %}",
            "{% trans 'تذكير بخصومات الدفع المبكر' %}"
        ];

        let reminderType = prompt("{% trans 'اختر نوع التذكير:' %}\n" +
            reminderTypes.map(function(type, index) { return (index + 1) + '. ' + type; }).join('\n'));

        if (reminderType) {
            const typeIndex = parseInt(reminderType) - 1;
            if (typeIndex >= 0 && typeIndex < reminderTypes.length) {
                if (typeof toastr !== 'undefined') {
                    toastr.info("{% trans 'جاري إرسال:' %} " + reminderTypes[typeIndex]);
                }

                setTimeout(function() {
                    if (typeof toastr !== 'undefined') {
                        toastr.success("{% trans 'تم إرسال التذكير إلى' %} " + (unpaidInvoices + overdueInvoices) + " {% trans 'مورد' %}");
                    }
                }, 2000);
            }
        }
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'purchases:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // Initialize DataTable
        var table = $('#invoicesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "order": [[3, "desc"]],
            "pageLength": 25
        });
        
        // Toggle view mode
        $('.view-mode').click(function(e) {
            e.preventDefault();
            var mode = $(this).data('mode');
            
            if (mode === 'table') {
                $('#tableView').removeClass('d-none');
                $('#cardsView').addClass('d-none');
            } else if (mode === 'cards') {
                $('#tableView').addClass('d-none');
                $('#cardsView').removeClass('d-none');
            }
        });
        
        // Auto-submit form on select change
        $('#status, #supplier').change(function() {
            $('#filterForm').submit();
        });
    });
</script>
{% endblock %}
