#!/usr/bin/env python
"""
Script لإصلاح تواريخ المدفوعات التي لا تحتوي على تاريخ صحيح
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'auto_parts_pos.settings')
django.setup()

from sales.models import Payment
from django.utils import timezone

def fix_payment_dates():
    """إصلاح تواريخ المدفوعات"""
    
    # البحث عن المدفوعات التي لا تحتوي على payment_date صحيح
    payments_without_date = Payment.objects.filter(payment_date__isnull=True)
    
    print(f"عدد المدفوعات بدون تاريخ: {payments_without_date.count()}")
    
    # تحديث المدفوعات لتستخدم created_at كـ payment_date
    for payment in payments_without_date:
        payment.payment_date = payment.created_at
        payment.save(update_fields=['payment_date'])
        print(f"تم تحديث الدفعة {payment.id} - التاريخ: {payment.payment_date}")
    
    # البحث عن المدفوعات التي payment_date = created_at (المدفوعات القديمة)
    payments_to_update = Payment.objects.filter(payment_date=timezone.F('created_at'))
    
    print(f"عدد المدفوعات التي تحتاج تحديث: {payments_to_update.count()}")
    
    for payment in payments_to_update:
        # استخدام تاريخ البيع كتاريخ افتراضي للدفع
        payment.payment_date = payment.sale.date
        payment.save(update_fields=['payment_date'])
        print(f"تم تحديث الدفعة {payment.id} - التاريخ الجديد: {payment.payment_date}")

if __name__ == '__main__':
    fix_payment_dates()
    print("تم الانتهاء من إصلاح تواريخ المدفوعات")
