/* Main Styles for Auto Parts POS System */

/* Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

body {
    font-family: 'Tajawal', sans-serif;
    background-color: #f8f9fa;
    margin-bottom: 60px; /* لإضافة مساحة أسفل الصفحة للشريط السفلي */
}

/* RTL Specific Adjustments */
.me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

/* Navbar Adjustments */
.navbar {
    padding: 0.5rem 0;
}

.navbar-nav {
    flex-wrap: wrap;
    white-space: normal;
}

.navbar-nav .nav-item {
    display: inline-block;
}

.navbar .container-fluid {
    max-width: 100%;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.navbar-brand {
    font-size: 1.1rem;
    white-space: nowrap;
    margin-right: 0.5rem;
}

.nav-link {
    padding: 0.5rem 0.3rem;
    font-size: 0.85rem;
    white-space: nowrap;
}

/* Ensure settings link is visible */
.nav-item:nth-child(9) .nav-link {
    color: #fff;
    font-weight: bold;
}

/* Dropdown menus - navbar dropdowns show fully, others with scrolling */
.dropdown-menu {
    min-width: 10rem;
}

/* Only apply scrolling to non-navbar dropdowns */
.dropdown-menu:not(.navbar .dropdown-menu) {
    max-height: 300px;
    overflow-y: auto;
}

/* Navbar dropdown menus show fully without scrolling */
.navbar .dropdown-menu {
    max-height: none;
    overflow: visible;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
    z-index: 1050;
}

/* Ensure navbar dropdowns have proper spacing */
.navbar .dropdown-item {
    padding: 0.5rem 1rem;
    white-space: nowrap;
}

/* Force navbar dropdowns to show fully - highest specificity */
.navbar-nav .dropdown-menu,
.navbar .nav-item .dropdown-menu,
#inventoryDropdown + .dropdown-menu,
#purchasesDropdown + .dropdown-menu,
#userDropdown + .dropdown-menu {
    max-height: none !important;
    overflow: visible !important;
    height: auto !important;
    min-width: 220px !important; /* زيادة العرض لعدم قطع النص */
}

/* تحسين تصميم عناصر القائمة المنسدلة */
.navbar .dropdown-item {
    padding: 0.75rem 1rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: flex-start; /* البداية من اليمين */
    direction: ltr; /* اتجاه من اليسار لليمين */
}

/* وضع الأيقونات في أقصى اليمين */
.navbar .dropdown-item i {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
    order: 1; /* وضع الأيقونة في البداية */
    flex-shrink: 0;
}

/* النص في الجهة اليسرى */
.navbar .dropdown-item span,
.navbar .dropdown-item .dropdown-text {
    order: 2;
    flex-grow: 1;
    text-align: right;
}

/* تحسينات خاصة لقوائم المخزون والمشتريات */
#inventoryDropdown + .dropdown-menu .dropdown-item,
#purchasesDropdown + .dropdown-menu .dropdown-item {
    min-width: 200px;
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
}

/* ضمان ظهور النص كاملاً */
#inventoryDropdown + .dropdown-menu,
#purchasesDropdown + .dropdown-menu {
    min-width: 250px !important;
    width: auto !important;
}

/* تحسين hover effect */
.navbar .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #495057;
}

/* تحسين الفواصل */
.navbar .dropdown-divider {
    margin: 0.5rem 0;
    border-top: 1px solid #dee2e6;
}

/* منع التمرير الأفقي في الصفحة */
body {
    overflow-x: hidden;
}

/* تحسينات خاصة لقائمة المستخدم (admin) */
.dropdown-menu-end,
#userDropdown + .dropdown-menu {
    right: 0 !important;
    left: auto !important;
    min-width: 160px !important;
    max-width: 180px !important;
    transform: none !important;
}

/* ضمان عدم تجاوز القوائم المنسدلة لحدود الشاشة */
.navbar .dropdown-menu {
    max-width: calc(100vw - 40px);
    box-sizing: border-box;
    position: absolute;
}

/* إصلاح موضع القوائم المنسدلة في الجانب الأيمن */
.navbar-nav .nav-item:last-child .dropdown-menu {
    right: 0;
    left: auto;
    transform: translateX(0);
}

/* Select wrapper for custom styling */
.select-wrapper select {
    max-height: 300px;
}

/* تعطيل التمرير لحقل مكان التخزين */
#storage_location {
    overflow: visible !important;
}

/* تنسيق القائمة المنسدلة العادية */
.normal-select {
    overflow: visible !important;
    max-height: none !important;
}

/* تنسيق القوائم المنسدلة المخصصة */
.custom-dropdown {
    max-height: 300px;
    overflow-y: auto;
    position: absolute;
    width: 100%;
    z-index: 1000;
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    display: none;
    top: 100%;
    right: 0;
}

.custom-dropdown .option-item {
    padding: 8px 12px;
    cursor: pointer;
}

.custom-dropdown .option-item:hover {
    background-color: #f8f9fa;
}

.custom-dropdown .option-item.selected {
    background-color: #e9ecef;
}

/* إضافة الشريط الأزرق عند تحديد حقل منسدل */
.select-wrapper.focused select {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* للتوافق مع الكود القديم */
.storage-location-dropdown {
    max-height: 300px;
    overflow-y: auto;
    position: absolute;
    width: 100%;
    z-index: 1000;
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    display: none;
    top: 100%;
    right: 0;
}

/* Scrollable select for dropdowns with many options */
.select-scrollable {
    max-height: 300px;
    overflow-y: auto;
}

/* تنسيق القائمة المنسدلة عند فتحها */
select.form-select[size]:not([size="1"]) {
    height: auto;
    padding-right: 0.75rem;
    border: 1px solid #ced4da;
    box-shadow: none;
}

/* Navbar adjustments for small screens */
@media (max-width: 1200px) {
    .navbar-brand {
        font-size: 0.9rem;
        max-width: 180px;
    }

    .nav-link {
        font-size: 0.8rem;
        padding: 0.4rem 0.2rem;
    }

    /* للتأكد من أن القائمة المنسدلة تظهر بشكل صحيح */
    .dropdown-menu {
        position: absolute;
    }
}

/* Dashboard Cards */
.dashboard-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-icon {
    font-size: 2.5rem;
    color: #0d6efd;
}

/* Tables */
.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* DataTables Pagination Controls */
.dataTables_length {
    margin-bottom: 15px;
    margin-right: 15px;
}

.dataTables_length select {
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.dataTables_info {
    padding-top: 0.85em;
    white-space: nowrap;
    margin-right: 15px;
}

/* Forms */
.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تعديل مظهر القائمة المنسدلة عند التركيز وتفعيل التمرير */
.form-select[size]:focus {
    border-color: #ced4da;
    box-shadow: none;
}

/* Buttons */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Sidebar (for mobile) */
@media (max-width: 992px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: 0;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        background-color: #343a40;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }
}

/* Login Page */
.login-container {
    max-width: 400px;
    margin: 100px auto;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    background-color: #fff;
}

/* Invoice Styles */
.invoice-header {
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.invoice-footer {
    border-top: 2px solid #dee2e6;
    padding-top: 20px;
    margin-top: 20px;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    body {
        background-color: #fff;
    }

    .container {
        width: 100%;
        max-width: 100%;
    }
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    padding: 3px 6px;
    border-radius: 50%;
    background-color: #dc3545;
    color: white;
    font-size: 0.7rem;
}

/* Product Cards */
.product-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-img {
    height: 200px;
    object-fit: contain;
    background-color: #f8f9fa;
}

/* Customer Profile */
.customer-profile-img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid #0d6efd;
}

/* Status Badges */
.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.status-completed {
    background-color: #198754;
    color: white;
}

.status-cancelled {
    background-color: #dc3545;
    color: white;
}

/* Low Stock Warning */
.low-stock {
    color: #dc3545;
    font-weight: bold;
}

/* Search Box */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 40px;
}

.search-box .search-icon {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #0d6efd;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0b5ed7;
}

/* تنسيقات الشريط السفلي */
html {
    position: relative;
    min-height: 100%;
}

body {
    margin: 0;
    padding: 0;
    margin-bottom: 70px; /* مسافة أكبر للشريط السفلي لمنع التداخل مع المحتوى */
}

#main-footer {
    height: 50px;
    background-color: #212529;
    color: white;
    position: absolute; /* تغيير من fixed إلى absolute لتحسين السلوك */
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
}

/* إضافة مسافة أسفل المحتوى في جميع الصفحات */
.container, .container-fluid {
    padding-bottom: 30px;
}

/* إضافة مسافة أسفل البطاقات والنماذج */
.card, form {
    margin-bottom: 20px;
}
