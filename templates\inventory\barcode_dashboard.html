{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "لوحة تحكم الباركود" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes - مطابق لصفحة لوحة التحكم */
    body .navbar .dropdown-menu {
        max-height: none !important;
        overflow: visible !important;
        border: 1px solid rgba(0,0,0,.15) !important;
        border-radius: 0.375rem !important;
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15) !important;
        background-color: white !important;
        min-width: 10rem !important;
        margin-top: 0.5rem !important;
    }

    body .navbar .dropdown-item {
        padding: 0.5rem 1rem !important;
        white-space: nowrap !important;
        color: #374151 !important;
        background-color: transparent !important;
        font-weight: normal !important;
    }

    body .navbar .dropdown-item:hover,
    body .navbar .dropdown-item:focus {
        background-color: #f8f9fa !important;
        color: #0d6efd !important;
        text-decoration: none !important;
    }

    body .navbar .dropdown-item i {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-qrcode me-3"></i>
                        {% trans "لوحة تحكم الباركود" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "إدارة شاملة لأنظمة الباركود مع إنشاء ومسح وطباعة وتتبع جميع العمليات" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <a href="{% url 'inventory:barcode:generate_barcode' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "إنشاء باركود" %}
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "أدوات" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'inventory:barcode:scan_barcode' %}">
                                        <i class="fas fa-camera text-primary me-2"></i>{% trans "مسح باركود" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:select_products_for_barcode' %}">
                                        <i class="fas fa-print text-success me-2"></i>{% trans "طباعة دفعة" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:barcode:barcode_types' %}">
                                        <i class="fas fa-tags text-warning me-2"></i>{% trans "أنواع الباركود" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:barcode:barcode_settings' %}">
                                        <i class="fas fa-sliders-h text-info me-2"></i>{% trans "إعدادات الباركود" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:barcode:barcode_logs' %}">
                                        <i class="fas fa-history text-secondary me-2"></i>{% trans "سجلات الباركود" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportBarcodeReport()">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>{% trans "تقرير الباركود" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'inventory:index' %}">
                                            <i class="fas fa-warehouse me-1"></i>{% trans "المخزون" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "لوحة تحكم الباركود" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <!-- إحصائيات الباركود -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>{{ total_barcodes }}</h3>
                                    <p class="mb-0">{% trans "إجمالي الباركودات" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcode_types_count }}</h3>
                                    <p class="mb-0">{% trans "أنواع الباركود" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcodes_printed }}</h3>
                                    <p class="mb-0">{% trans "الباركودات المطبوعة" %}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3>{{ barcodes_scanned }}</h3>
                                    <p class="mb-0">{% trans "الباركودات الممسوحة" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- وظائف الباركود الرئيسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2">{% trans "وظائف الباركود" %}</h5>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-cog me-2"></i>{% trans "إدارة الباركود" %}</h5>
                                    <p class="card-text">{% trans "إدارة أنواع الباركود وإعدادات النظام" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:barcode_types' %}" class="btn btn-outline-primary"><i class="fas fa-tags me-2"></i>{% trans "أنواع الباركود" %}</a>
                                        <a href="{% url 'inventory:barcode:barcode_settings' %}" class="btn btn-outline-secondary"><i class="fas fa-sliders-h me-2"></i>{% trans "إعدادات الباركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-print me-2"></i>{% trans "إنشاء وطباعة الباركود" %}</h5>
                                    <p class="card-text">{% trans "إنشاء باركود جديد أو طباعة باركود لمنتجات متعددة" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:generate_barcode' %}" class="btn btn-outline-success"><i class="fas fa-plus-circle me-2"></i>{% trans "إنشاء باركود" %}</a>
                                        <a href="{% url 'inventory:select_products_for_barcode' %}" class="btn btn-outline-info"><i class="fas fa-print me-2"></i>{% trans "طباعة دفعة باركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-qrcode me-2"></i>{% trans "مسح وتتبع الباركود" %}</h5>
                                    <p class="card-text">{% trans "مسح الباركود باستخدام الكاميرا وعرض سجلات الاستخدام" %}</p>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'inventory:barcode:scan_barcode' %}" class="btn btn-outline-danger"><i class="fas fa-camera me-2"></i>{% trans "مسح الباركود" %}</a>
                                        <a href="{% url 'inventory:barcode:barcode_logs' %}" class="btn btn-outline-dark"><i class="fas fa-history me-2"></i>{% trans "سجلات الباركود" %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ميزات متقدمة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2">{% trans "ميزات متقدمة" %}</h5>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-file-export me-2"></i>{% trans "تصدير واستيراد" %}</h5>
                                    <p class="card-text">{% trans "تصدير قائمة الباركود إلى ملفات Excel/CSV أو استيراد الباركود من ملفات خارجية" %}</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-primary" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-file-export me-2"></i>{% trans "تصدير الباركود" %}</button>
                                        <button class="btn btn-outline-secondary" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-file-import me-2"></i>{% trans "استيراد الباركود" %}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-cogs me-2"></i>{% trans "تكامل مع نظام نقطة البيع" %}</h5>
                                    <p class="card-text">{% trans "ربط مباشر بين مسح الباركود وإضافة المنتجات إلى سلة المبيعات" %}</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-success" onclick="openPOSIntegration()"><i class="fas fa-link me-2"></i>{% trans "ربط مع نقطة البيع" %}</button>
                                        <button class="btn btn-outline-info" onclick="alert('سيتم تطوير هذه الميزة قريباً')"><i class="fas fa-sync me-2"></i>{% trans "تحديث المخزون تلقائياً" %}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأنشطة الأخيرة -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>{% trans "الأنشطة الأخيرة" %}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "التاريخ" %}</th>
                                                    <th>{% trans "الباركود" %}</th>
                                                    <th>{% trans "العملية" %}</th>
                                                    <th>{% trans "المستخدم" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for log in recent_logs %}
                                                <tr>
                                                    <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                                                    <td>{{ log.barcode.barcode_number }}</td>
                                                    <td>
                                                        {% if log.action == 'print' %}
                                                            <span class="badge bg-warning">{% trans "طباعة" %}</span>
                                                        {% elif log.action == 'scan' %}
                                                            <span class="badge bg-success">{% trans "مسح" %}</span>
                                                        {% elif log.action == 'create' %}
                                                            <span class="badge bg-primary">{% trans "إنشاء" %}</span>
                                                        {% elif log.action == 'delete' %}
                                                            <span class="badge bg-danger">{% trans "حذف" %}</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{{ log.action }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ log.user.username }}</td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="4" class="text-center">{% trans "لا توجد أنشطة حديثة" %}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportBarcodeReport() {
        // إنشاء تقرير شامل للباركود
        const totalBarcodes = {{ total_barcodes|default:0 }};
        const barcodeTypes = {{ barcode_types_count|default:0 }};
        const printed = {{ barcodes_printed|default:0 }};
        const scanned = {{ barcodes_scanned|default:0 }};

        // إنشاء تقرير PDF
        const reportData = {
            title: 'تقرير الباركود الشامل',
            date: new Date().toLocaleDateString('ar-SA'),
            statistics: {
                total: totalBarcodes,
                types: barcodeTypes,
                printed: printed,
                scanned: scanned
            }
        };

        // طباعة التقرير
        window.print();
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'inventory:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // يمكن إضافة أي سلوك JavaScript إضافي هنا
    });

    // وظيفة ربط نقطة البيع
    function openPOSIntegration() {
        // إنشاء نافذة حوار لربط نقطة البيع
        const posModal = $(`
            <div class="modal fade" id="posIntegrationModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title"><i class="fas fa-link me-2"></i>ربط مع نقطة البيع</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0"><i class="fas fa-qrcode me-2"></i>مسح باركود للبيع</h6>
                                        </div>
                                        <div class="card-body text-center">
                                            <p class="text-muted">امسح باركود المنتج لإضافته إلى سلة المبيعات</p>
                                            <button class="btn btn-primary btn-lg" onclick="startPOSScanner()">
                                                <i class="fas fa-camera me-2"></i>بدء المسح
                                            </button>
                                            <div id="pos-scanner-result" class="mt-3"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>سلة المبيعات</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="pos-cart-items">
                                                <p class="text-muted text-center">لا توجد منتجات في السلة</p>
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between">
                                                <strong>الإجمالي:</strong>
                                                <strong id="pos-total">0.00 د.م</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-success" onclick="completePOSSale()">
                                <i class="fas fa-check me-2"></i>إتمام البيع
                            </button>
                            <button type="button" class="btn btn-info" onclick="openFullPOS()">
                                <i class="fas fa-external-link-alt me-2"></i>فتح نقطة البيع الكاملة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);

        // إضافة النافذة إلى الصفحة
        $('body').append(posModal);

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('posIntegrationModal'));
        modal.show();

        // إزالة النافذة عند إغلاقها
        $('#posIntegrationModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    }

    // متغيرات عامة لسلة المبيعات
    let posCart = [];
    let posTotal = 0;

    // بدء ماسح الباركود لنقطة البيع
    function startPOSScanner() {
        $('#pos-scanner-result').html(`
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                سيتم فتح ماسح الباركود في نافذة جديدة
            </div>
        `);

        // فتح صفحة مسح الباركود في نافذة جديدة
        const scannerWindow = window.open('/inventory/barcode/scan/', '_blank', 'width=800,height=600');

        // استماع لرسائل من نافذة الماسح
        window.addEventListener('message', function(event) {
            if (event.data.type === 'barcode_scanned') {
                addProductToPOSCart(event.data.product);
                scannerWindow.close();
            }
        });
    }

    // إضافة منتج إلى سلة نقطة البيع
    function addProductToPOSCart(product) {
        // التحقق من وجود المنتج في السلة
        const existingItem = posCart.find(item => item.id === product.id);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            posCart.push({
                id: product.id,
                name: product.name,
                price: parseFloat(product.price),
                quantity: 1
            });
        }

        updatePOSCartDisplay();

        // عرض رسالة نجاح
        $('#pos-scanner-result').html(`
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                تم إضافة "${product.name}" إلى السلة
            </div>
        `);
    }

    // تحديث عرض سلة المبيعات
    function updatePOSCartDisplay() {
        const cartContainer = $('#pos-cart-items');

        if (posCart.length === 0) {
            cartContainer.html('<p class="text-muted text-center">لا توجد منتجات في السلة</p>');
            posTotal = 0;
        } else {
            let cartHTML = '';
            posTotal = 0;

            posCart.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                posTotal += itemTotal;

                cartHTML += `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>${item.name}</strong><br>
                            <small class="text-muted">${item.price.toFixed(2)} د.م × ${item.quantity}</small>
                        </div>
                        <div class="text-end">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" onclick="decreaseQuantity(${index})">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="increaseQuantity(${index})">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="removeFromCart(${index})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <div class="mt-1">
                                <strong>${itemTotal.toFixed(2)} د.م</strong>
                            </div>
                        </div>
                    </div>
                `;
            });

            cartContainer.html(cartHTML);
        }

        $('#pos-total').text(posTotal.toFixed(2) + ' د.م');
    }

    // زيادة كمية المنتج
    function increaseQuantity(index) {
        posCart[index].quantity += 1;
        updatePOSCartDisplay();
    }

    // تقليل كمية المنتج
    function decreaseQuantity(index) {
        if (posCart[index].quantity > 1) {
            posCart[index].quantity -= 1;
            updatePOSCartDisplay();
        }
    }

    // إزالة منتج من السلة
    function removeFromCart(index) {
        posCart.splice(index, 1);
        updatePOSCartDisplay();
    }

    // إتمام عملية البيع
    function completePOSSale() {
        if (posCart.length === 0) {
            alert('لا توجد منتجات في السلة');
            return;
        }

        // هنا يمكن إرسال بيانات البيع إلى الخادم
        alert(`تم إتمام عملية البيع بقيمة ${posTotal.toFixed(2)} د.م`);

        // إعادة تعيين السلة
        posCart = [];
        posTotal = 0;
        updatePOSCartDisplay();

        // إغلاق النافذة
        $('#posIntegrationModal').modal('hide');
    }

    // فتح نقطة البيع الكاملة
    function openFullPOS() {
        // فتح صفحة نقطة البيع الرئيسية
        window.open('/sales/new/', '_blank');
    }

    // تهيئة القوائم المنسدلة في الشريط العلوي
    initializeNavbarDropdowns();
</script>

<script>
    // تهيئة القوائم المنسدلة في الشريط العلوي
    function initializeNavbarDropdowns() {
        // Re-initialize all Bootstrap dropdowns
        const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
        const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });

        // Ensure dropdowns work properly
        $('.dropdown-toggle').off('click.bs.dropdown').on('click', function(e) {
            e.preventDefault();
            const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
            dropdown.toggle();
        });

        // Fix navbar dropdown items layout
        fixNavbarDropdownLayout();

        // Fix admin dropdown position
        fixAdminDropdownPosition();
    }

    // إصلاح تخطيط عناصر القائمة المنسدلة
    function fixNavbarDropdownLayout() {
        $('.navbar .dropdown-item').each(function() {
            const $item = $(this);
            const $icon = $item.find('i');
            const text = $item.text().trim();

            if ($icon.length > 0 && text) {
                // إعادة ترتيب المحتوى - الأيقونة أولاً ثم النص
                $item.html(`
                    <i class="${$icon.attr('class')}"></i>
                    <span class="dropdown-text">${text}</span>
                `);
            }
        });
    }

    // إصلاح موضع قائمة admin لمنع التمرير الأفقي
    function fixAdminDropdownPosition() {
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) {
            const dropdownMenu = userDropdown.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                // إضافة class خاص لقائمة المستخدم
                dropdownMenu.classList.add('dropdown-menu-end');

                // ضبط الموضع
                dropdownMenu.style.right = '0';
                dropdownMenu.style.left = 'auto';
                dropdownMenu.style.transform = 'none';
                dropdownMenu.style.minWidth = '160px';
                dropdownMenu.style.maxWidth = '180px';
            }
        }

        // منع التمرير الأفقي في الصفحة
        document.body.style.overflowX = 'hidden';
    }
</script>
{% endblock %}