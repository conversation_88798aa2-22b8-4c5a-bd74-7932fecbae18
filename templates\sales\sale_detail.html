{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تفاصيل البيع" %} #{{ sale.invoice_number }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .sale-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .info-card {
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        border-radius: 10px;
        margin-bottom: 1.5rem;
    }
    
    .info-card .card-header {
        background: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        border-radius: 10px 10px 0 0;
    }
    
    .status-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    
    .payment-status {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .amount-display {
        font-size: 1.2rem;
        font-weight: bold;
    }
    
    .table-items {
        background: white;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .table-items th {
        background: #cfe2ff
        color: white;
        border: none;
        padding: 1rem;
    }
    
    .table-items td {
        padding: 1rem;
        border-color: #e3e6f0;
    }
    
    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 5px;
    }
    
    .action-buttons .btn {
        margin: 0.25rem;
    }
    
    @media print {
        @page {
            size: A5;
            margin: 0.2in;
        }

        body {
            font-size: 8px !important;
            line-height: 1.1 !important;
        }

        .no-print {
            display: none !important;
        }

        .container-fluid {
            max-width: 100% !important;
            padding: 0 !important;
            width: 100% !important;
        }

        /* ضمان استخدام العرض الكامل */
        .card {
            width: 100% !important;
        }

        .table-borderless td {
            border: none !important;
            padding: 0.2rem 0.5rem !important;
        }

        .sale-header {
            background: #5a5c69 !important;
            -webkit-print-color-adjust: exact;
            padding: 0.5rem !important;
            margin-bottom: 0.5rem !important;
            page-break-inside: avoid;
        }

        .card {
            margin-bottom: 0.5rem !important;
            page-break-inside: avoid;
        }

        .card-body {
            padding: 0.25rem !important;
        }

        .table {
            font-size: 7px !important;
        }

        .table td, .table th {
            padding: 0.3rem !important;
        }

        .row {
            margin-bottom: 0.5rem !important;
        }

        /* تقليل حجم الصور */
        .product-image {
            width: 30px !important;
            height: 30px !important;
        }

        /* تقليل المسافات */
        .info-card {
            margin-bottom: 0.5rem !important;
        }

        /* ضمان عرض المعلومات جنباً إلى جنب */
        .row {
            display: flex !important;
            flex-wrap: nowrap !important;
        }

        .col-lg-6 {
            width: 50% !important;
            flex: 0 0 50% !important;
            max-width: 50% !important;
            padding-right: 0.25rem !important;
            padding-left: 0.25rem !important;
        }

        /* منع تقسيم الجداول */
        .table-responsive {
            page-break-inside: avoid;
        }

        /* ضغط المحتوى */
        h1, h2, h3, h4, h5, h6 {
            margin-bottom: 0.5rem !important;
        }

        /* إخفاء معلومات التشخيص عند الطباعة */
        .alert {
            display: none !important;
        }

        /* تحسينات إضافية لضمان صفحة واحدة */
        .card-header {
            padding: 0.25rem 0.5rem !important;
            font-size: 8px !important;
        }

        .card-header h6 {
            margin: 0 !important;
            font-size: 8px !important;
        }

        .table-borderless td {
            padding: 0.1rem 0.3rem !important;
            font-size: 7px !important;
        }

        .badge {
            font-size: 6px !important;
            padding: 0.1rem 0.3rem !important;
        }

        /* ضغط جدول المنتجات */
        .table thead th {
            padding: 0.2rem !important;
            font-size: 7px !important;
        }

        .table tbody td {
            padding: 0.15rem !important;
            font-size: 6px !important;
        }

        /* ضغط ملخص الفاتورة */
        .invoice-summary {
            font-size: 7px !important;
        }

        .invoice-summary td {
            padding: 0.1rem !important;
        }

        /* تقليل ارتفاع الصور */
        .product-image {
            width: 20px !important;
            height: 20px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}
    <!-- Sale Header -->
    <div class="sale-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h3 mb-2">
                    <i class="fas fa-file-invoice me-2"></i>
                    {% trans "فاتورة رقم" %}: {{ sale.invoice_number }}
                </h1>
                <p class="mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    {{ sale.date|date:"Y-m-d H:i" }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="payment-status">
                    {% if sale.is_paid %}
                        <span class="badge bg-success status-badge">
                            <i class="fas fa-check-circle me-1"></i>مدفوع
                        </span>
                    {% else %}
                        <span class="badge bg-danger status-badge">
                            <i class="fas fa-times-circle me-1"></i>غير مدفوع
                        </span>
                    {% endif %}
                </div>
                <div class="amount-display mt-2">
                    {{ sale.total_amount|floatformat:2 }} د.م
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{% url 'reports:sales_report' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>{% trans "العودة للتقرير" %}
                    </a>
                </div>
                <div class="action-buttons">
                    <!-- Export Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-download me-1"></i>{% trans "تصدير" %}
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{% url 'sales:export_sale_pdf' sale.id %}">
                                    <i class="fas fa-file-pdf text-danger me-2"></i>{% trans "تصدير PDF" %}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'sales:export_sale_excel' sale.id %}">
                                    <i class="fas fa-file-excel text-success me-2"></i>{% trans "تصدير Excel" %}
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" id="shareWhatsApp" data-sale-id="{{ sale.id }}">
                                    <i class="fab fa-whatsapp text-success me-2"></i>{% trans "مشاركة عبر واتساب" %}
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- Action Buttons -->
                    <a href="{% url 'sales:print_invoice' sale.id %}" class="btn btn-success me-2" target="_blank">
                        <i class="fas fa-print me-1"></i>{% trans "طباعة الفاتورة" %}
                    </a>
                    <a href="{% url 'sales:edit_sale' sale.id %}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i>{% trans "تعديل" %}
                    </a>
                    {% if not sale.is_paid %}
                        <button class="btn btn-warning mark-paid-btn me-2" data-sale-id="{{ sale.id }}">
                            <i class="fas fa-dollar-sign me-1"></i>{% trans "تسجيل كمدفوع" %}
                        </button>
                    {% endif %}
                    <button class="btn btn-info" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>{% trans "طباعة هذه الصفحة" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Customer Information -->
        <div class="col-lg-6">
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user me-2"></i>{% trans "معلومات العميل" %}
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>{% trans "الاسم" %}:</strong></td>
                            <td>{{ sale.customer.name }}</td>
                        </tr>
                        {% if sale.customer.phone %}
                        <tr>
                            <td><strong>{% trans "الهاتف" %}:</strong></td>
                            <td>{{ sale.customer.phone }}</td>
                        </tr>
                        {% endif %}
                        {% if sale.customer.email %}
                        <tr>
                            <td><strong>{% trans "البريد الإلكتروني" %}:</strong></td>
                            <td>{{ sale.customer.email }}</td>
                        </tr>
                        {% endif %}
                        {% if sale.customer.address %}
                        <tr>
                            <td><strong>{% trans "العنوان" %}:</strong></td>
                            <td>{{ sale.customer.address }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>

        <!-- Sale Information -->
        <div class="col-lg-6">
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>{% trans "معلومات البيع" %}
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>{% trans "الموظف" %}:</strong></td>
                            <td>{{ sale.employee.get_full_name|default:sale.employee.username }}</td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "طريقة الدفع" %}:</strong></td>
                            <td>
                                {% if sale.payment_method == 'cash' %}
                                    <i class="fas fa-money-bill text-success"></i> نقدي
                                {% elif sale.payment_method == 'card' %}
                                    <i class="fas fa-credit-card text-info"></i> بطاقة ائتمان
                                {% elif sale.payment_method == 'transfer' %}
                                    <i class="fas fa-university text-warning"></i> تحويل بنكي
                                {% elif sale.payment_method == 'check' %}
                                    <i class="fas fa-file-invoice text-secondary"></i> شيك
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "الحالة" %}:</strong></td>
                            <td>
                                {% if sale.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                {% elif sale.status == 'pending' %}
                                    <span class="badge bg-warning text-dark">معلق</span>
                                {% else %}
                                    <span class="badge bg-danger">ملغي</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>{% trans "المبلغ المدفوع" %}:</strong></td>
                            <td class="text-success">{{ sale.paid_amount|floatformat:2 }} د.م</td>
                        </tr>
                        {% if not sale.is_paid %}
                        <tr>
                            <td><strong>{% trans "المبلغ المتبقي" %}:</strong></td>
                            <td class="text-danger">{{ sale.remaining_amount|floatformat:2 }} د.م</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Sale Items -->
    <div class="card info-card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-shopping-cart me-2"></i>{% trans "المنتجات المباعة" %}
            </h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-items mb-0">
                    <thead class="table-primary">
                        <tr>
                            <th class="text-black">{% trans "المنتج" %}</th>
                            <th class="text-black">{% trans "الكمية" %}</th>
                            <th class="text-black">{% trans "سعر الوحدة" %}</th>
                            <th class="text-black">{% trans "المجموع" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in sale.items.all %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if item.product.image %}
                                        <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image me-3">
                                    {% else %}
                                        <div class="product-image me-3 bg-light d-flex align-items-center justify-content-center">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ item.product.name }}</strong>
                                        <br><small class="text-muted">{{ item.product.code }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.unit_price|floatformat:2 }} د.م</td>
                            <td><strong>{{ item.subtotal|floatformat:2 }} د.م</strong></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Sale Summary -->
    <div class="row">
        <div class="col-lg-8"></div>
        <div class="col-lg-4">
            <div class="card info-card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calculator me-2"></i>{% trans "ملخص الفاتورة" %}
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td>{% trans "المجموع الفرعي" %}:</td>
                            <td class="text-end">{{ sale.subtotal|floatformat:2 }} د.م</td>
                        </tr>
                        {% if sale.discount > 0 %}
                        <tr>
                            <td>{% trans "الخصم" %}:</td>
                            <td class="text-end text-success">-{{ sale.discount|floatformat:2 }} د.م</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td>{% trans "الضريبة" %} ({{ sale.tax_rate }}%):</td>
                            <td class="text-end">{{ sale.tax_amount|floatformat:2 }} د.م</td>
                        </tr>
                        <tr class="border-top">
                            <td><strong>{% trans "المجموع الكلي" %}:</strong></td>
                            <td class="text-end"><strong>{{ sale.total_amount|floatformat:2 }} د.م</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Payments Section -->
    <!-- Payments Section - Always Show -->
    <div class="card info-card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-money-bill me-2"></i>{% trans "المدفوعات" %} ({{ payments.count }})
            </h6>
        </div>
        <div class="card-body p-0">
            <!-- Debug Info -->
            <div class="alert alert-info m-3">
                <strong>معلومات التشخيص:</strong><br>
                عدد المدفوعات: {{ payments.count }}<br>
                إجمالي المدفوع: {{ total_paid|floatformat:2 }} د.م<br>
                المبلغ المتبقي: {{ remaining_amount|floatformat:2 }} د.م
            </div>

            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead class="table-primary">
                        <tr>
                            <th class="text-blue">{% trans "رقم الدفعة" %}</th>
                            <th class="text-blue">{% trans "التاريخ" %}</th>
                            <th class="text-blue">{% trans "المبلغ" %}</th>
                            <th class="text-blue">{% trans "طريقة الدفع" %}</th>
                            <th class="text-blue">{% trans "ملاحظات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr style="background-color: {% cycle '#f8f9fa' '#ffffff' %};">
                            <td><strong>#{{ payment.id }}</strong></td>
                            <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                            <td><strong style="color: #28a745;">{{ payment.amount|floatformat:2 }} د.م</strong></td>
                            <td>
                                {% if payment.payment_method == 'cash' %}
                                    <i class="fas fa-money-bill text-success"></i> نقدي
                                {% elif payment.payment_method == 'card' %}
                                    <i class="fas fa-credit-card text-info"></i> بطاقة ائتمان
                                {% elif payment.payment_method == 'transfer' %}
                                    <i class="fas fa-exchange-alt text-primary"></i> تحويل بنكي
                                {% elif payment.payment_method == 'check' %}
                                    <i class="fas fa-file-invoice text-secondary"></i> شيك
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>{{ payment.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center text-muted">
                                <i class="fas fa-info-circle me-2"></i>لا توجد مدفوعات مسجلة
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-light">
            <div class="row">
                <div class="col-md-6">
                    <strong>{% trans "إجمالي المدفوع" %}:</strong>
                    <span class="text-success">{{ total_paid|floatformat:2 }} د.م</span>
                </div>
                <div class="col-md-6 text-end">
                    <strong>{% trans "المبلغ المتبقي" %}:</strong>
                    <span class="text-danger">{{ remaining_amount|floatformat:2 }} د.م</span>
                </div>
            </div>
            {% if not sale.is_paid %}
            <div class="row mt-2">
                <div class="col-12 text-center">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                        <i class="fas fa-plus me-1"></i>{% trans "إضافة دفعة جديدة" %}
                    </button>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    {% if sale.notes %}
    <!-- Notes -->
    <div class="card info-card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-sticky-note me-2"></i>{% trans "ملاحظات" %}
            </h6>
        </div>
        <div class="card-body">
            <p class="mb-0">{{ sale.notes }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Add Payment Modal -->
    <div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPaymentModalLabel">
                        <i class="fas fa-money-bill me-2"></i>{% trans "إضافة دفعة جديدة" %}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addPaymentForm" method="post" action="{% url 'sales:add_payment' sale.id %}">
                    <div class="modal-body">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="payment_amount" class="form-label">{% trans "المبلغ" %}</label>
                            <input type="number" class="form-control" id="payment_amount" name="amount"
                                   step="0.01" min="0.01" max="{{ remaining_amount }}"
                                   value="{{ remaining_amount }}" required>
                            <div class="form-text">
                                {% trans "المبلغ المتبقي" %}: {{ remaining_amount|floatformat:2 }} د.م
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">{% trans "طريقة الدفع" %}</label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="">{% trans "اختر طريقة الدفع" %}</option>
                                <option value="cash">{% trans "نقدي" %}</option>
                                <option value="card">{% trans "بطاقة ائتمان" %}</option>
                                <option value="transfer">{% trans "تحويل بنكي" %}</option>
                                <option value="check">{% trans "شيك" %}</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="payment_date" class="form-label">{% trans "تاريخ الدفع" %}</label>
                            <input type="datetime-local" class="form-control" id="payment_date" name="date"
                                   value="{{ today|date:'Y-m-d' }}T{{ today|date:'H:i' }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="payment_notes" class="form-label">{% trans "ملاحظات" %}</label>
                            <textarea class="form-control" id="payment_notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            {% trans "إلغاء" %}
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>{% trans "حفظ الدفعة" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تسجيل الفاتورة كمدفوعة
    $('.mark-paid-btn').on('click', function() {
        const saleId = $(this).data('sale-id');
        const button = $(this);
        
        if (confirm('هل أنت متأكد من تسجيل هذه الفاتورة كمدفوعة؟')) {
            $.ajax({
                url: '{% url "sales:mark_as_paid" 0 %}'.replace('0', saleId),
                method: 'POST',
                headers: {
                    'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || 'حدث خطأ أثناء تحديث حالة الدفع');
                    }
                },
                error: function() {
                    alert('حدث خطأ في الاتصال بالخادم');
                }
            });
        }
    });

    // مشاركة عبر واتساب
    $('#shareWhatsApp').on('click', function(e) {
        e.preventDefault();
        const saleId = $(this).data('sale-id');

        // إنشاء نص الرسالة
        const message = `فاتورة مبيعات رقم: {{ sale.invoice_number }}
العميل: {{ sale.customer.name }}
التاريخ: {{ sale.date|date:"Y-m-d" }}
المبلغ الإجمالي: {{ sale.total_amount }} درهم
{% if sale.is_paid %}الحالة: مدفوع{% else %}الحالة: غير مدفوع{% endif %}

رابط تحميل الفاتورة: ${window.location.origin}{% url 'sales:export_sale_pdf' sale.id %}`;

        // فتح واتساب
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
    });

    // معالجة إضافة دفعة جديدة
    $('#addPaymentForm').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();

        // تعطيل الزر وإظهار مؤشر التحميل
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...');

        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    // إغلاق النافذة المنبثقة وإعادة تحميل الصفحة
                    $('#addPaymentModal').modal('hide');
                    location.reload();
                } else {
                    alert(response.message || 'حدث خطأ أثناء حفظ الدفعة');
                    submitBtn.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr) {
                let errorMessage = 'حدث خطأ في الاتصال بالخادم';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert(errorMessage);
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
{% endblock %}
