/* أنماط محسنة خصيصاً لـ PDF مع دعم أفضل للعربية */

/* استيراد الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

/* إعدادات أساسية */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: '<PERSON><PERSON>wal', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Arial Unicode MS', sans-serif !important;
    font-size: 12pt;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
    background: white;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* حاوي الفاتورة */
.invoice-container {
    width: 100%;
    max-width: 210mm;
    margin: 0 auto;
    padding: 15mm;
    background: white;
    position: relative;
}

/* إخفاء العناصر غير المطلوبة */
.no-print, .action-buttons, .btn, .dropdown, .modal, .navbar, .sidebar {
    display: none !important;
}

/* تنسيق العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Tajawal', 'Tahoma', sans-serif;
    font-weight: 700;
    margin-bottom: 12px;
    line-height: 1.3;
    page-break-after: avoid;
    color: #333;
}

h1 { font-size: 20pt; margin-bottom: 15px; }
h2 { font-size: 18pt; margin-bottom: 12px; }
h3 { font-size: 16pt; margin-bottom: 10px; }
h4 { font-size: 14pt; margin-bottom: 8px; }
h5 { font-size: 13pt; margin-bottom: 8px; }
h6 { font-size: 12pt; margin-bottom: 6px; }

/* تنسيق الفقرات */
p {
    margin-bottom: 10px;
    line-height: 1.5;
    font-size: 12pt;
}

/* تنسيق الجداول */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    page-break-inside: avoid;
    font-size: 11pt;
}

th, td {
    padding: 10px 8px;
    border: 1px solid #ddd;
    text-align: center;
    vertical-align: middle;
    line-height: 1.4;
    word-wrap: break-word;
}

th {
    background-color: #f8f9fa;
    font-weight: 700;
    color: #333;
    font-size: 12pt;
}

tbody tr:nth-child(even) {
    background-color: #fafafa;
}

/* تنسيق الصفوف والأعمدة */
.row {
    display: table;
    width: 100%;
    margin-bottom: 15px;
    page-break-inside: avoid;
}

.col-md-6 {
    display: table-cell;
    width: 50%;
    vertical-align: top;
    padding: 0 10px;
}

.col-md-4 {
    display: table-cell;
    width: 33.333333%;
    vertical-align: top;
    padding: 0 8px;
}

.col-md-12 {
    display: table-cell;
    width: 100%;
    padding: 0 5px;
}

/* تنسيق رأس الفاتورة */
.invoice-header {
    border-bottom: 3px solid #007bff;
    padding-bottom: 20px;
    margin-bottom: 25px;
    page-break-after: avoid;
}

.invoice-title {
    font-size: 22pt;
    font-weight: 700;
    margin-bottom: 8px;
    color: #007bff;
}

/* تنسيق تفاصيل الفاتورة */
.invoice-details {
    margin-bottom: 25px;
    page-break-inside: avoid;
}

.invoice-details h5 {
    background-color: #f8f9fa;
    padding: 10px;
    margin-bottom: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #007bff;
}

/* تنسيق قوائم التعريف */
dl {
    margin-bottom: 0;
}

dt {
    font-weight: 700;
    margin-bottom: 5px;
    display: inline-block;
    width: 40%;
    vertical-align: top;
    color: #555;
}

dd {
    margin-bottom: 10px;
    display: inline-block;
    width: 58%;
    vertical-align: top;
    padding-right: 10px;
}

/* تنسيق عناصر الفاتورة */
.invoice-items {
    margin-bottom: 25px;
    page-break-inside: avoid;
}

.table-invoice th {
    background-color: #007bff;
    color: white;
    font-weight: 700;
    text-align: center;
}

.table-invoice td {
    border: 1px solid #ddd;
    text-align: center;
}

.table-invoice td:first-child {
    text-align: center;
    font-weight: 700;
}

.table-invoice td:nth-child(3) {
    text-align: right;
    font-weight: 600;
}

/* تنسيق ملخص الفاتورة */
.invoice-summary {
    background-color: #f8f9fa;
    padding: 20px;
    border: 2px solid #007bff;
    border-radius: 8px;
    margin-bottom: 25px;
    page-break-inside: avoid;
}

.invoice-summary .d-flex {
    display: table;
    width: 100%;
    margin-bottom: 10px;
    padding: 5px 0;
}

.invoice-summary .d-flex span:first-child {
    display: table-cell;
    width: 70%;
    font-weight: 600;
    text-align: right;
}

.invoice-summary .d-flex span:last-child {
    display: table-cell;
    width: 30%;
    text-align: left;
    font-weight: 700;
}

.total-amount {
    font-size: 16pt !important;
    font-weight: 700 !important;
    color: #007bff !important;
}

/* تنسيق معلومات الدفع */
.payment-info {
    background-color: #f8f9fa;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 25px;
    page-break-inside: avoid;
}

.payment-status {
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 4px;
}

.payment-status.paid { 
    color: white; 
    background-color: #28a745; 
}

.payment-status.partial { 
    color: #333; 
    background-color: #ffc107; 
}

.payment-status.unpaid { 
    color: white; 
    background-color: #dc3545; 
}

/* تنسيق منطقة التوقيع */
.signature-area {
    display: table;
    width: 100%;
    margin-top: 40px;
    page-break-inside: avoid;
}

.signature-box {
    display: table-cell;
    width: 33.333%;
    text-align: center;
    padding: 0 15px;
    vertical-align: top;
}

.signature-box p {
    font-weight: 700;
    margin-bottom: 60px;
    color: #555;
}

.signature-line {
    border-top: 2px solid #333;
    margin-bottom: 8px;
}

/* تنسيق الشروط والأحكام */
.terms-conditions {
    font-size: 10pt;
    margin-top: 30px;
    page-break-inside: avoid;
    background-color: #fafafa;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
}

.terms-conditions h6 {
    color: #007bff;
    margin-bottom: 10px;
    font-size: 12pt;
}

.terms-conditions ol {
    padding-right: 25px;
    margin: 0;
}

.terms-conditions li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* تنسيق ذيل الفاتورة */
.invoice-footer {
    border-top: 2px solid #007bff;
    padding-top: 20px;
    margin-top: 30px;
    text-align: center;
    font-size: 10pt;
    color: #666;
    page-break-inside: avoid;
}

/* تنسيق الصور */
.invoice-logo {
    max-height: 80px;
    max-width: 200px;
    margin-bottom: 15px;
    display: block;
}

.qr-code {
    text-align: center;
    margin: 20px 0;
}

.qr-code img {
    max-width: 120px;
    max-height: 120px;
    border: 1px solid #ddd;
    padding: 5px;
}

/* تنسيق الشارات */
.badge {
    display: inline-block;
    padding: 6px 12px;
    font-size: 10pt;
    font-weight: 700;
    background-color: #007bff;
    color: white;
    border-radius: 4px;
    margin: 2px;
}

/* تنسيق الخطوط والألوان */
.text-center { text-align: center !important; }
.text-start { text-align: right !important; }
.text-end { text-align: left !important; }
.text-muted { color: #6c757d !important; }
.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }

.fw-bold, strong { font-weight: 700 !important; }
.small { font-size: 10pt !important; }

/* تنسيق الهوامش والمسافات */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 6px !important; }
.mb-2 { margin-bottom: 10px !important; }
.mb-3 { margin-bottom: 15px !important; }
.mb-4 { margin-bottom: 20px !important; }
.mt-2 { margin-top: 10px !important; }
.mt-3 { margin-top: 15px !important; }
.mt-4 { margin-top: 20px !important; }

/* تنسيق الحدود */
hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 15px 0;
}

/* تنسيق خاص للطباعة */
@page {
    size: A4;
    margin: 15mm;
}

@media print {
    body {
        font-size: 11pt;
        line-height: 1.4;
    }
    
    .invoice-container {
        padding: 0;
        margin: 0;
        box-shadow: none;
    }
    
    .page-break {
        page-break-before: always;
    }
    
    .no-page-break {
        page-break-inside: avoid;
    }
    
    table {
        font-size: 10pt;
    }
    
    th, td {
        padding: 6px 4px;
    }
}
