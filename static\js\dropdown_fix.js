// إصلاح شامل للنوافذ المنسدلة في جميع الصفحات
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        // تأكد من وجود data-bs-toggle في جميع أزرار النوافذ المنسدلة
        document.querySelectorAll('.dropdown-toggle').forEach(function(element) {
            if (!element.hasAttribute('data-bs-toggle')) {
                element.setAttribute('data-bs-toggle', 'dropdown');
            }
        });

        // أعد تهيئة جميع النوافذ المنسدلة
        document.querySelectorAll('.dropdown-toggle').forEach(function(element) {
            const dropdown = bootstrap.Dropdown.getInstance(element);
            if (dropdown) {
                dropdown.dispose();
            }
            new bootstrap.Dropdown(element);
        });

        // أضف معالج النقر المخصص
        document.querySelectorAll('.dropdown-toggle').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // أولاً، أغلق جميع القوائم المنسدلة المفتوحة الأخرى
                document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                    if (menu.parentElement.querySelector('.dropdown-toggle') !== element) {
                        menu.classList.remove('show');
                    }
                });

                // ثم افتح أو أغلق القائمة المنسدلة الحالية
                const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
                dropdown.toggle();
            });
        });

        // تأكد من إغلاق القوائم المنسدلة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                    menu.classList.remove('show');
                });
            }
        });
    }, 2000);
});
