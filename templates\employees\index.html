{% extends 'modern_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "إدارة الموظفين" %}{% endblock %}

{% block page_title %}{% trans "إدارة الموظفين" %}{% endblock %}
{% block page_subtitle %}{% trans "إدارة شاملة للموظفين والأقسام مع تتبع الحضور والصلاحيات" %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active text-white">{% trans "الموظفين" %}</li>
{% endblock %}

{% block header_actions %}
<a href="{% url 'employees:add_employee' %}" class="btn btn-light me-2">
    <i class="fas fa-plus"></i> {% trans "إضافة موظف" %}
</a>
<a href="{% url 'employees:roles' %}" class="btn btn-outline-light me-2">
    <i class="fas fa-sitemap"></i> {% trans "الأقسام" %}
</a>
<a href="{% url 'employees:roles_permissions' %}" class="btn btn-outline-light me-2">
    <i class="fas fa-user-shield"></i> {% trans "الصلاحيات" %}
</a>
<button class="btn btn-success me-2" id="exportEmployees">
    <i class="fas fa-file-export"></i> {% trans "تصدير" %}
</button>
<button class="btn btn-secondary" data-toggle="modal" data-target="#importEmployeesModal">
    <i class="fas fa-file-import"></i> {% trans "استيراد" %}
</button>
{% endblock %}

{% block content %}
<div class="container-fluid">

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">{% trans "إجمالي الموظفين" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ employees.count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">{% trans "الموظفين النشطين" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_employees_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">{% trans "الموظفين الجدد (هذا الشهر)" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ new_employees_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">{% trans "الإجازات الحالية" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_leaves_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-minus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة الموظفين" %}</h6>
            <div class="d-flex">
                <div class="input-group mr-2" style="width: 250px;">
                    <input type="text" class="form-control" id="searchEmployees" placeholder="{% trans 'بحث...' %}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="dropdown mr-2">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="departmentFilter" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-building"></i> {% trans "القسم" %}
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="departmentFilter">
                        <a class="dropdown-item active" href="#" data-filter="all">{% trans "الكل" %}</a>
                        {% for department in departments %}
                        <a class="dropdown-item" href="#" data-filter="department-{{ department.id }}">{{ department.name }}</a>
                        {% endfor %}
                    </div>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-success dropdown-toggle" type="button" id="statusFilter" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-user-check"></i> {% trans "الحالة" %}
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="statusFilter">
                        <a class="dropdown-item active" href="#" data-filter="all">{% trans "الكل" %}</a>
                        <a class="dropdown-item" href="#" data-filter="status-active">{% trans "نشط" %}</a>
                        <a class="dropdown-item" href="#" data-filter="status-inactive">{% trans "غير نشط" %}</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="employeesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="5%">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="selectAllEmployees">
                                    <label class="custom-control-label" for="selectAllEmployees"></label>
                                </div>
                            </th>
                            <th>{% trans "رقم الموظف" %}</th>
                            <th>{% trans "الاسم" %}</th>
                            <th>{% trans "القسم" %}</th>
                            <th>{% trans "المنصب" %}</th>
                            <th>{% trans "رقم الهاتف" %}</th>
                            <th>{% trans "تاريخ التوظيف" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                        <tr class="employee-row {% if employee.department %}department-{{ employee.department.id }}{% endif %} status-{% if employee.is_active %}active{% else %}inactive{% endif %}">
                            <td>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input employee-checkbox" id="employee-{{ employee.id }}" value="{{ employee.id }}">
                                    <label class="custom-control-label" for="employee-{{ employee.id }}"></label>
                                </div>
                            </td>
                            <td>{{ employee.employee_id }}</td>
                            <td>{{ employee.full_name }}</td>
                            <td>{{ employee.department.name|default:"-" }}</td>
                            <td>{{ employee.position.name|default:"-" }}</td>
                            <td>{{ employee.phone }}</td>
                            <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                            <td>
                                {% if employee.is_active %}
                                <span class="badge badge-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge badge-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'employees:view_employee' employee.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'employees:edit_employee' employee.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'employees:delete_employee' employee.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-warning employee-actions-btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-right">
                                        <a class="dropdown-item" href="#" onclick="resetPassword({{ employee.id }}); return false;">
                                            <i class="fas fa-key mr-2"></i> {% trans "إعادة تعيين كلمة المرور" %}
                                        </a>
                                        <a class="dropdown-item" href="#" onclick="toggleStatus({{ employee.id }}, {% if employee.is_active %}false{% else %}true{% endif %}); return false;">
                                            {% if employee.is_active %}
                                            <i class="fas fa-user-slash mr-2"></i> {% trans "تعطيل" %}
                                            {% else %}
                                            <i class="fas fa-user-check mr-2"></i> {% trans "تفعيل" %}
                                            {% endif %}
                                        </a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="#" onclick="viewAttendance({{ employee.id }}); return false;">
                                            <i class="fas fa-calendar-check mr-2"></i> {% trans "سجل الحضور" %}
                                        </a>
                                        <a class="dropdown-item" href="#" onclick="viewLeaves({{ employee.id }}); return false;">
                                            <i class="fas fa-calendar-minus mr-2"></i> {% trans "الإجازات" %}
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center">{% trans "لا يوجد موظفين" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- أزرار الإجراءات الجماعية -->
            <div class="bulk-actions mt-3" style="display: none;">
                <div class="alert alert-info">
                    <span id="selected-count">0</span> {% trans "موظف محدد" %}
                    <div class="float-right">
                        <button class="btn btn-sm btn-success" id="bulkActivate">
                            <i class="fas fa-user-check"></i> {% trans "تفعيل" %}
                        </button>
                        <button class="btn btn-sm btn-warning" id="bulkDeactivate">
                            <i class="fas fa-user-slash"></i> {% trans "تعطيل" %}
                        </button>
                        <button class="btn btn-sm btn-danger" id="bulkDelete">
                            <i class="fas fa-trash"></i> {% trans "حذف" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Employees Modal -->
<div class="modal fade" id="importEmployeesModal" tabindex="-1" role="dialog" aria-labelledby="importEmployeesModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importEmployeesModalLabel">{% trans "استيراد بيانات الموظفين" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="importEmployeesForm" method="post" enctype="multipart/form-data" action="{% url 'employees:import_employees' %}">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="importFile">{% trans "ملف CSV/Excel" %}</label>
                        <input type="file" class="form-control-file" id="importFile" name="import_file" accept=".csv,.xlsx" required>
                    </div>
                    <div class="alert alert-info">
                        <h6>{% trans "تعليمات:" %}</h6>
                        <ul>
                            <li>{% trans "يجب أن يحتوي الملف على الأعمدة التالية: الاسم الأول, الاسم الأخير, البريد الإلكتروني, رقم الموظف, رقم الهاتف, القسم, المنصب, تاريخ التوظيف, الراتب" %}</li>
                            <li>{% trans "يمكنك تحميل نموذج للملف من" %} <a href="#" id="downloadTemplate">{% trans "هنا" %}</a></li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="submitImport">{% trans "استيراد" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" role="dialog" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">{% trans "إعادة تعيين كلمة المرور" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="resetPasswordForm">
                    {% csrf_token %}
                    <input type="hidden" id="resetEmployeeId" name="employee_id">
                    <div class="form-group">
                        <label for="newPassword">{% trans "كلمة المرور الجديدة" %}</label>
                        <input type="password" class="form-control" id="newPassword" name="new_password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">{% trans "تأكيد كلمة المرور" %}</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="submitResetPassword">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" role="dialog" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkDeleteModalLabel">{% trans "تأكيد الحذف الجماعي" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من حذف" %} <span id="deleteCount"></span> {% trans "موظفين؟" %}</p>
                <p class="text-danger">{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
                <form id="bulkDeleteForm">
                    {% csrf_token %}
                    <input type="hidden" id="employeeIds" name="employee_ids">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-danger" id="confirmBulkDelete">{% trans "حذف" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تهيئة جدول البيانات
        var table = $('#employeesTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[2, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": [0, 8] }
            ],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // البحث المخصص
        $('#searchEmployees').keyup(function() {
            table.search($(this).val()).draw();
        });

        // تصفية حسب القسم
        $('#departmentFilter .dropdown-item').click(function(e) {
            e.preventDefault();
            $('#departmentFilter .dropdown-item').removeClass('active');
            $(this).addClass('active');

            var filter = $(this).data('filter');
            if (filter === 'all') {
                $('.employee-row').show();
            } else {
                $('.employee-row').hide();
                $('.' + filter).show();
            }
        });

        // تصفية حسب الحالة
        $('#statusFilter .dropdown-item').click(function(e) {
            e.preventDefault();
            $('#statusFilter .dropdown-item').removeClass('active');
            $(this).addClass('active');

            var filter = $(this).data('filter');
            if (filter === 'all') {
                $('.employee-row').show();
            } else {
                $('.employee-row').hide();
                $('.' + filter).show();
            }
        });

        // تحديد الكل
        $('#selectAllEmployees').change(function() {
            $('.employee-checkbox').prop('checked', $(this).prop('checked'));
            updateBulkActions();
        });

        // تحديث عدد العناصر المحددة
        $('.employee-checkbox').change(function() {
            updateBulkActions();
        });

        // تحديث شريط الإجراءات الجماعية
        function updateBulkActions() {
            var count = $('.employee-checkbox:checked').length;
            $('#selected-count').text(count);

            if (count > 0) {
                $('.bulk-actions').show();
            } else {
                $('.bulk-actions').hide();
            }
        }

        // تفعيل جماعي
        $('#bulkActivate').click(function() {
            var ids = [];
            $('.employee-checkbox:checked').each(function() {
                ids.push($(this).val());
            });

            if (ids.length > 0) {
                // إرسال طلب AJAX لتفعيل الموظفين
                alert('سيتم تفعيل ' + ids.length + ' موظف');
            }
        });

        // تعطيل جماعي
        $('#bulkDeactivate').click(function() {
            var ids = [];
            $('.employee-checkbox:checked').each(function() {
                ids.push($(this).val());
            });

            if (ids.length > 0) {
                // إرسال طلب AJAX لتعطيل الموظفين
                alert('سيتم تعطيل ' + ids.length + ' موظف');
            }
        });

        // حذف جماعي
        $('#bulkDelete').click(function() {
            var ids = [];
            $('.employee-checkbox:checked').each(function() {
                ids.push($(this).val());
            });

            if (ids.length > 0) {
                $('#deleteCount').text(ids.length);
                $('#employeeIds').val(JSON.stringify(ids));
                $('#bulkDeleteModal').modal('show');
            }
        });

        // تأكيد الحذف الجماعي
        $('#confirmBulkDelete').click(function() {
            // إرسال طلب AJAX لحذف الموظفين
            alert('سيتم حذف ' + $('#deleteCount').text() + ' موظف');
            $('#bulkDeleteModal').modal('hide');
        });

        // تصدير البيانات
        $('#exportEmployees').click(function() {
            // إرسال طلب لتصدير البيانات
            window.location.href = "{% url 'employees:export_employees' %}";
        });

        // تقديم نموذج الاستيراد
        $('#submitImport').click(function() {
            if ($('#importFile').val()) {
                $('#importEmployeesForm').submit();
            } else {
                alert('{% trans "يرجى اختيار ملف" %}');
            }
        });

        // تنزيل قالب الاستيراد
        $('#downloadTemplate').click(function(e) {
            e.preventDefault();
            window.location.href = "{% url 'employees:download_template' %}";
        });

        // إعادة تعيين كلمة المرور
        function resetPassword(employeeId) {
            $('#resetEmployeeId').val(employeeId);
            $('#resetPasswordModal').modal('show');
        }

        // تقديم نموذج إعادة تعيين كلمة المرور
        $('#submitResetPassword').click(function() {
            var newPassword = $('#newPassword').val();
            var confirmPassword = $('#confirmPassword').val();

            if (newPassword !== confirmPassword) {
                alert('{% trans "كلمة المرور وتأكيدها غير متطابقين" %}');
                return;
            }

            // إرسال طلب AJAX لإعادة تعيين كلمة المرور
            alert('{% trans "سيتم إعادة تعيين كلمة المرور" %}');
            $('#resetPasswordModal').modal('hide');
        });

        // تبديل حالة الموظف
        window.toggleStatus = function(employeeId, status) {
            // إرسال طلب AJAX لتغيير حالة الموظف
            var statusText = status ? '{% trans "تفعيل" %}' : '{% trans "تعطيل" %}';
            alert('سيتم ' + statusText + ' الموظف');
        };

        // عرض سجل الحضور
        window.viewAttendance = function(employeeId) {
            window.location.href = "{% url 'employees:attendance' %}?employee=" + employeeId;
        };

        // عرض الإجازات
        window.viewLeaves = function(employeeId) {
            alert('{% trans "سيتم عرض إجازات الموظف" %}');
        };

        // تعريف الدالة للاستخدام من الخارج
        window.resetPassword = resetPassword;
    });
</script>
{% endblock %}
