{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "إدارة الموظفين" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-user-tie me-3"></i>
                        {% trans "إدارة الموظفين" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "إدارة شاملة للموظفين والأقسام مع تتبع الأدوار والصلاحيات والحضور" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <a href="{% url 'employees:add_employee' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "موظف جديد" %}
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "إدارة" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'employees:roles' %}">
                                        <i class="fas fa-sitemap text-info me-2"></i>{% trans "الأقسام والمناصب" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'employees:roles_permissions' %}">
                                        <i class="fas fa-user-shield text-warning me-2"></i>{% trans "الأدوار والصلاحيات" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel text-success me-2"></i>{% trans "تصدير Excel" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>{% trans "تصدير PDF" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importEmployeesModal">
                                        <i class="fas fa-file-import text-primary me-2"></i>{% trans "استيراد" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "الموظفين" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid">

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">{% trans "إجمالي الموظفين" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ employees.count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">{% trans "الموظفين النشطين" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_employees_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">{% trans "الموظفين الجدد (هذا الشهر)" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ new_employees_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">{% trans "الإجازات الحالية" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ current_leaves_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-minus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة الموظفين" %}</h6>
            <div class="d-flex">
                <div class="input-group mr-2" style="width: 250px;">
                    <input type="text" class="form-control" id="searchEmployees" placeholder="{% trans 'بحث...' %}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="dropdown mr-2">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="departmentFilter" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-building"></i> {% trans "القسم" %}
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="departmentFilter">
                        <a class="dropdown-item active" href="#" data-filter="all">{% trans "الكل" %}</a>
                        {% for department in departments %}
                        <a class="dropdown-item" href="#" data-filter="department-{{ department.id }}">{{ department.name }}</a>
                        {% endfor %}
                    </div>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-success dropdown-toggle" type="button" id="statusFilter" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-user-check"></i> {% trans "الحالة" %}
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="statusFilter">
                        <a class="dropdown-item active" href="#" data-filter="all">{% trans "الكل" %}</a>
                        <a class="dropdown-item" href="#" data-filter="status-active">{% trans "نشط" %}</a>
                        <a class="dropdown-item" href="#" data-filter="status-inactive">{% trans "غير نشط" %}</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="employeesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="5%">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="selectAllEmployees">
                                    <label class="custom-control-label" for="selectAllEmployees"></label>
                                </div>
                            </th>
                            <th>{% trans "رقم الموظف" %}</th>
                            <th>{% trans "الاسم" %}</th>
                            <th>{% trans "القسم" %}</th>
                            <th>{% trans "المنصب" %}</th>
                            <th>{% trans "رقم الهاتف" %}</th>
                            <th>{% trans "تاريخ التوظيف" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employees %}
                        <tr class="employee-row {% if employee.department %}department-{{ employee.department.id }}{% endif %} status-{% if employee.is_active %}active{% else %}inactive{% endif %}">
                            <td>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input employee-checkbox" id="employee-{{ employee.id }}" value="{{ employee.id }}">
                                    <label class="custom-control-label" for="employee-{{ employee.id }}"></label>
                                </div>
                            </td>
                            <td>{{ employee.employee_id }}</td>
                            <td>{{ employee.full_name }}</td>
                            <td>{{ employee.department.name|default:"-" }}</td>
                            <td>{{ employee.position.name|default:"-" }}</td>
                            <td>{{ employee.phone }}</td>
                            <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                            <td>
                                {% if employee.is_active %}
                                <span class="badge badge-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge badge-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'employees:view_employee' employee.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'employees:edit_employee' employee.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'employees:delete_employee' employee.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-warning employee-actions-btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-right">
                                        <a class="dropdown-item" href="#" onclick="resetPassword({{ employee.id }}); return false;">
                                            <i class="fas fa-key mr-2"></i> {% trans "إعادة تعيين كلمة المرور" %}
                                        </a>
                                        <a class="dropdown-item" href="#" onclick="toggleStatus({{ employee.id }}, {% if employee.is_active %}false{% else %}true{% endif %}); return false;">
                                            {% if employee.is_active %}
                                            <i class="fas fa-user-slash mr-2"></i> {% trans "تعطيل" %}
                                            {% else %}
                                            <i class="fas fa-user-check mr-2"></i> {% trans "تفعيل" %}
                                            {% endif %}
                                        </a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="#" onclick="viewAttendance({{ employee.id }}); return false;">
                                            <i class="fas fa-calendar-check mr-2"></i> {% trans "سجل الحضور" %}
                                        </a>
                                        <a class="dropdown-item" href="#" onclick="viewLeaves({{ employee.id }}); return false;">
                                            <i class="fas fa-calendar-minus mr-2"></i> {% trans "الإجازات" %}
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center">{% trans "لا يوجد موظفين" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- أزرار الإجراءات الجماعية -->
            <div class="bulk-actions mt-3" style="display: none;">
                <div class="alert alert-info">
                    <span id="selected-count">0</span> {% trans "موظف محدد" %}
                    <div class="float-right">
                        <button class="btn btn-sm btn-success" id="bulkActivate">
                            <i class="fas fa-user-check"></i> {% trans "تفعيل" %}
                        </button>
                        <button class="btn btn-sm btn-warning" id="bulkDeactivate">
                            <i class="fas fa-user-slash"></i> {% trans "تعطيل" %}
                        </button>
                        <button class="btn btn-sm btn-danger" id="bulkDelete">
                            <i class="fas fa-trash"></i> {% trans "حذف" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Employees Modal -->
<div class="modal fade" id="importEmployeesModal" tabindex="-1" role="dialog" aria-labelledby="importEmployeesModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importEmployeesModalLabel">{% trans "استيراد بيانات الموظفين" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="importEmployeesForm" method="post" enctype="multipart/form-data" action="{% url 'employees:import_employees' %}">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="importFile">{% trans "ملف CSV/Excel" %}</label>
                        <input type="file" class="form-control-file" id="importFile" name="import_file" accept=".csv,.xlsx" required>
                    </div>
                    <div class="alert alert-info">
                        <h6>{% trans "تعليمات:" %}</h6>
                        <ul>
                            <li>{% trans "يجب أن يحتوي الملف على الأعمدة التالية: الاسم الأول, الاسم الأخير, البريد الإلكتروني, رقم الموظف, رقم الهاتف, القسم, المنصب, تاريخ التوظيف, الراتب" %}</li>
                            <li>{% trans "يمكنك تحميل نموذج للملف من" %} <a href="#" id="downloadTemplate">{% trans "هنا" %}</a></li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="submitImport">{% trans "استيراد" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" role="dialog" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">{% trans "إعادة تعيين كلمة المرور" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="resetPasswordForm">
                    {% csrf_token %}
                    <input type="hidden" id="resetEmployeeId" name="employee_id">
                    <div class="form-group">
                        <label for="newPassword">{% trans "كلمة المرور الجديدة" %}</label>
                        <input type="password" class="form-control" id="newPassword" name="new_password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">{% trans "تأكيد كلمة المرور" %}</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-primary" id="submitResetPassword">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" role="dialog" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkDeleteModalLabel">{% trans "تأكيد الحذف الجماعي" %}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من حذف" %} <span id="deleteCount"></span> {% trans "موظفين؟" %}</p>
                <p class="text-danger">{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
                <form id="bulkDeleteForm">
                    {% csrf_token %}
                    <input type="hidden" id="employeeIds" name="employee_ids">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{% trans "إلغاء" %}</button>
                <button type="button" class="btn btn-danger" id="confirmBulkDelete">{% trans "حذف" %}</button>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportToExcel() {
        // تصدير البيانات إلى Excel
        const table = document.querySelector('#employeesTable');
        if (table) {
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.table_to_sheet(table);
            XLSX.utils.book_append_sheet(wb, ws, 'الموظفين');
            XLSX.writeFile(wb, 'employees_data.xlsx');
        }
    }

    function exportToPDF() {
        window.print();
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'dashboard:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // تهيئة جدول البيانات
        var table = $('#employeesTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[2, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": [0, 8] }
            ],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // البحث المخصص
        $('#searchEmployees').keyup(function() {
            table.search($(this).val()).draw();
        });

        // تصفية حسب القسم
        $('#departmentFilter .dropdown-item').click(function(e) {
            e.preventDefault();
            $('#departmentFilter .dropdown-item').removeClass('active');
            $(this).addClass('active');

            var filter = $(this).data('filter');
            if (filter === 'all') {
                $('.employee-row').show();
            } else {
                $('.employee-row').hide();
                $('.' + filter).show();
            }
        });

        // تصفية حسب الحالة
        $('#statusFilter .dropdown-item').click(function(e) {
            e.preventDefault();
            $('#statusFilter .dropdown-item').removeClass('active');
            $(this).addClass('active');

            var filter = $(this).data('filter');
            if (filter === 'all') {
                $('.employee-row').show();
            } else {
                $('.employee-row').hide();
                $('.' + filter).show();
            }
        });

        // تحديد الكل
        $('#selectAllEmployees').change(function() {
            $('.employee-checkbox').prop('checked', $(this).prop('checked'));
            updateBulkActions();
        });

        // تحديث عدد العناصر المحددة
        $('.employee-checkbox').change(function() {
            updateBulkActions();
        });

        // تحديث شريط الإجراءات الجماعية
        function updateBulkActions() {
            var count = $('.employee-checkbox:checked').length;
            $('#selected-count').text(count);

            if (count > 0) {
                $('.bulk-actions').show();
            } else {
                $('.bulk-actions').hide();
            }
        }

        // تفعيل جماعي
        $('#bulkActivate').click(function() {
            var ids = [];
            $('.employee-checkbox:checked').each(function() {
                ids.push($(this).val());
            });

            if (ids.length > 0) {
                // إرسال طلب AJAX لتفعيل الموظفين
                alert('سيتم تفعيل ' + ids.length + ' موظف');
            }
        });

        // تعطيل جماعي
        $('#bulkDeactivate').click(function() {
            var ids = [];
            $('.employee-checkbox:checked').each(function() {
                ids.push($(this).val());
            });

            if (ids.length > 0) {
                // إرسال طلب AJAX لتعطيل الموظفين
                alert('سيتم تعطيل ' + ids.length + ' موظف');
            }
        });

        // حذف جماعي
        $('#bulkDelete').click(function() {
            var ids = [];
            $('.employee-checkbox:checked').each(function() {
                ids.push($(this).val());
            });

            if (ids.length > 0) {
                $('#deleteCount').text(ids.length);
                $('#employeeIds').val(JSON.stringify(ids));
                $('#bulkDeleteModal').modal('show');
            }
        });

        // تأكيد الحذف الجماعي
        $('#confirmBulkDelete').click(function() {
            // إرسال طلب AJAX لحذف الموظفين
            alert('سيتم حذف ' + $('#deleteCount').text() + ' موظف');
            $('#bulkDeleteModal').modal('hide');
        });

        // تصدير البيانات
        $('#exportEmployees').click(function() {
            // إرسال طلب لتصدير البيانات
            window.location.href = "{% url 'employees:export_employees' %}";
        });

        // تقديم نموذج الاستيراد
        $('#submitImport').click(function() {
            if ($('#importFile').val()) {
                $('#importEmployeesForm').submit();
            } else {
                alert('{% trans "يرجى اختيار ملف" %}');
            }
        });

        // تنزيل قالب الاستيراد
        $('#downloadTemplate').click(function(e) {
            e.preventDefault();
            window.location.href = "{% url 'employees:download_template' %}";
        });

        // إعادة تعيين كلمة المرور
        function resetPassword(employeeId) {
            $('#resetEmployeeId').val(employeeId);
            $('#resetPasswordModal').modal('show');
        }

        // تقديم نموذج إعادة تعيين كلمة المرور
        $('#submitResetPassword').click(function() {
            var newPassword = $('#newPassword').val();
            var confirmPassword = $('#confirmPassword').val();

            if (newPassword !== confirmPassword) {
                alert('{% trans "كلمة المرور وتأكيدها غير متطابقين" %}');
                return;
            }

            // إرسال طلب AJAX لإعادة تعيين كلمة المرور
            alert('{% trans "سيتم إعادة تعيين كلمة المرور" %}');
            $('#resetPasswordModal').modal('hide');
        });

        // تبديل حالة الموظف
        window.toggleStatus = function(employeeId, status) {
            // إرسال طلب AJAX لتغيير حالة الموظف
            var statusText = status ? '{% trans "تفعيل" %}' : '{% trans "تعطيل" %}';
            alert('سيتم ' + statusText + ' الموظف');
        };

        // عرض سجل الحضور
        window.viewAttendance = function(employeeId) {
            window.location.href = "{% url 'employees:attendance' %}?employee=" + employeeId;
        };

        // عرض الإجازات
        window.viewLeaves = function(employeeId) {
            alert('{% trans "سيتم عرض إجازات الموظف" %}');
        };

        // تعريف الدالة للاستخدام من الخارج
        window.resetPassword = resetPassword;
    });
</script>
{% endblock %}
