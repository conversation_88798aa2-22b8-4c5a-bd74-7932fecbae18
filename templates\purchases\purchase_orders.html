{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "طلبات الشراء" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .payment-badge {
        font-size: 0.75rem;
    }

    .purchase-row {
        transition: all 0.2s;
    }

    .purchase-row:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-clipboard-list me-3"></i>
                        {% trans "طلبات الشراء" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "عرض وإدارة جميع طلبات الشراء مع إمكانية التصفية والبحث وتتبع حالة الطلبات" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <a href="{% url 'purchases:new_purchase' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "طلب شراء جديد" %}
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "أدوات" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'purchases:suppliers' %}">
                                        <i class="fas fa-truck text-success me-2"></i>{% trans "إدارة الموردين" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:invoices' %}">
                                        <i class="fas fa-file-invoice text-warning me-2"></i>{% trans "الفواتير" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:payments' %}">
                                        <i class="fas fa-credit-card text-info me-2"></i>{% trans "المدفوعات" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportPurchaseOrders()">
                                        <i class="fas fa-file-export text-primary me-2"></i>{% trans "تصدير الطلبات" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="printPurchaseOrders()">
                                        <i class="fas fa-print text-secondary me-2"></i>{% trans "طباعة القائمة" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="bulkActions()">
                                        <i class="fas fa-tasks text-warning me-2"></i>{% trans "إجراءات مجمعة" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:reports' %}">
                                        <i class="fas fa-chart-bar text-danger me-2"></i>{% trans "التقارير" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'purchases:index' %}">
                                            <i class="fas fa-shopping-bag me-1"></i>{% trans "إدارة المشتريات" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "طلبات الشراء" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية النتائج" %}</h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases:filter_purchases' %}" method="get" id="filterForm">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="status" class="form-label">{% trans "الحالة" %}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{% trans "الكل" %}</option>
                        {% for status_code, status_name in status_choices %}
                        <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>{{ status_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">{% trans "الكل" %}</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier_filter == supplier.id|stringformat:"i" %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                    </button>
                    <a href="{% url 'purchases:purchase_orders' %}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> {% trans "إعادة ضبط" %}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Purchases Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة طلبات الشراء" %}</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">{% trans "خيارات" %}:</div>
                <a class="dropdown-item" href="{% url 'purchases:reports' %}">
                    <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
                </a>
                <a class="dropdown-item" href="#" id="printBtn">
                    <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="purchasesTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "رقم المرجع" %}</th>
                        <th>{% trans "المورد" %}</th>
                        <th>{% trans "التاريخ" %}</th>
                        <th>{% trans "تاريخ التسليم المتوقع" %}</th>
                        <th>{% trans "المبلغ الإجمالي" %}</th>
                        <th>{% trans "حالة الطلب" %}</th>
                        <th>{% trans "حالة الدفع" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for purchase in purchases %}
                    <tr class="purchase-row">
                        <td>
                            <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}">
                                {{ purchase.reference_number }}
                            </a>
                        </td>
                        <td>
                            <a href="{% url 'purchases:view_supplier' supplier_id=purchase.supplier.id %}">
                                {{ purchase.supplier.name }}
                            </a>
                        </td>
                        <td>{{ purchase.date|date:"Y-m-d" }}</td>
                        <td>
                            {% if purchase.expected_delivery_date %}
                                {{ purchase.expected_delivery_date|date:"Y-m-d" }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>{{ purchase.total_amount|floatformat:2 }} ر.س</td>
                        <td>
                            {% if purchase.status == 'pending' %}
                            <span class="badge bg-warning text-dark status-badge">{% trans "معلق" %}</span>
                            {% elif purchase.status == 'received' %}
                            <span class="badge bg-success status-badge">{% trans "تم الاستلام" %}</span>
                            {% elif purchase.status == 'cancelled' %}
                            <span class="badge bg-danger status-badge">{% trans "ملغي" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.payment_status == 'unpaid' %}
                            <span class="badge bg-danger payment-badge">{% trans "غير مدفوع" %}</span>
                            {% elif purchase.payment_status == 'partial' %}
                            <span class="badge bg-warning text-dark payment-badge">{% trans "مدفوع جزئياً" %}</span>
                            {% elif purchase.payment_status == 'paid' %}
                            <span class="badge bg-success payment-badge">{% trans "مدفوع بالكامل" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if purchase.status == 'pending' %}
                                <a href="{% url 'purchases:edit_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'purchases:receive_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-success" title="{% trans 'استلام' %}">
                                    <i class="fas fa-check"></i>
                                </a>
                                {% endif %}
                                {% if purchase.payment_status != 'paid' %}
                                <a href="{% url 'purchases:add_payment' purchase_id=purchase.id %}" class="btn btn-sm btn-warning" title="{% trans 'إضافة دفعة' %}">
                                    <i class="fas fa-money-bill-wave"></i>
                                </a>
                                {% endif %}
                                {% if purchase.status == 'pending' %}
                                <a href="{% url 'purchases:delete_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-danger" title="{% trans 'حذف' %}">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">{% trans "لا توجد طلبات شراء" %}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportPurchaseOrders() {
        // تصدير طلبات الشراء
        const table = $('#purchasesTable').DataTable();
        const data = table.rows().data();

        // إنشاء محتوى CSV
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "رقم المرجع,المورد,التاريخ,تاريخ التسليم المتوقع,المبلغ الإجمالي,حالة الطلب,حالة الدفع\n";

        data.each(function(row, index) {
            // استخراج البيانات من كل صف
            const cells = $(row).find('td');
            if (cells.length > 0) {
                const rowData = [
                    $(cells[0]).text().trim(),
                    $(cells[1]).text().trim(),
                    $(cells[2]).text().trim(),
                    $(cells[3]).text().trim(),
                    $(cells[4]).text().trim(),
                    $(cells[5]).text().trim(),
                    $(cells[6]).text().trim()
                ];
                csvContent += rowData.join(",") + "\n";
            }
        });

        // تحميل الملف
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "purchase_orders_" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        if (typeof toastr !== 'undefined') {
            toastr.success("{% trans 'تم تصدير طلبات الشراء بنجاح' %}");
        }
    }

    function printPurchaseOrders() {
        // طباعة قائمة طلبات الشراء
        window.print();
    }

    function bulkActions() {
        // إجراءات مجمعة على طلبات الشراء المحددة
        const selectedRows = $('#purchasesTable tbody input[type="checkbox"]:checked');

        if (selectedRows.length === 0) {
            if (typeof toastr !== 'undefined') {
                toastr.warning("{% trans 'يرجى تحديد طلب واحد على الأقل' %}");
            }
            return;
        }

        // إظهار خيارات الإجراءات المجمعة
        const actions = [
            "{% trans 'تحديث حالة الطلبات' %}",
            "{% trans 'إرسال تذكير للموردين' %}",
            "{% trans 'تصدير الطلبات المحددة' %}",
            "{% trans 'طباعة الطلبات المحددة' %}"
        ];

        let actionChoice = prompt("{% trans 'اختر الإجراء المطلوب:' %}\n" +
            actions.map((action, index) => `${index + 1}. ${action}`).join('\n'));

        if (actionChoice) {
            const actionIndex = parseInt(actionChoice) - 1;
            if (actionIndex >= 0 && actionIndex < actions.length) {
                if (typeof toastr !== 'undefined') {
                    toastr.info(`{% trans 'جاري تنفيذ:' %} ${actions[actionIndex]}`);
                }

                // محاكاة تنفيذ الإجراء
                setTimeout(() => {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(`{% trans 'تم تنفيذ:' %} ${actions[actionIndex]} {% trans 'بنجاح' %}`);
                    }
                }, 2000);
            }
        }
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'purchases:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // Initialize DataTable
        var table = $('#purchasesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[2, "desc"]],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // Print functionality
        $('#printBtn').click(function(e) {
            e.preventDefault();
            window.print();
        });

        // Auto-submit form on select change
        $('#status, #supplier').change(function() {
            $('#filterForm').submit();
        });
    });
</script>
{% endblock %}
