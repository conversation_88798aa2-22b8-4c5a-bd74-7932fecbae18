/* Modern Header Design - مطابق لتصميم صفحة المبيعات */

/* CSS Variables */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1d4ed8;
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* Modern Page Header */
.modern-page-header {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.modern-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.modern-page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    z-index: 1;
}

.modern-page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
    font-weight: 400;
    position: relative;
    z-index: 1;
}

.modern-header-actions {
    position: relative;
    z-index: 1;
}

.modern-header-actions .btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: none;
}

.modern-header-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.modern-breadcrumb-nav {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modern-breadcrumb-nav a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
}

.modern-breadcrumb-nav a:hover {
    color: white;
}

/* زر العودة للصفحة السابقة */
.modern-btn-back {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--transition);
    cursor: pointer;
    backdrop-filter: blur(10px);
    margin-right: 10px;
}

.modern-btn-back:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    color: white;
}

.modern-btn-back i {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.modern-btn-back:hover i {
    transform: translateX(-2px);
}

/* تحسين breadcrumb للعمل مع زر العودة */
.modern-breadcrumb-nav nav {
    flex-grow: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-page-header {
        padding: 1.5rem 0;
    }
    
    .modern-page-title {
        font-size: 2rem;
    }
    
    .modern-page-subtitle {
        font-size: 1rem;
    }
    
    .modern-header-actions .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .modern-page-title {
        font-size: 1.75rem;
    }
    
    .modern-page-subtitle {
        font-size: 0.9rem;
    }
    
    .modern-header-actions {
        text-align: center;
        margin-top: 1rem;
    }
    
    .modern-header-actions .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .modern-page-header {
        background: var(--primary-color) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .modern-header-actions,
    .modern-btn-back {
        display: none !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-in-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Button Styles */
.modern-btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.modern-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.modern-btn-secondary {
    background: rgba(255,255,255,0.9);
    border: 1px solid rgba(255,255,255,0.2);
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.modern-btn-secondary:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--primary-color);
}

/* Dropdown Enhancements */
.modern-dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.modern-dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    background: none;
}

.modern-dropdown-item:hover {
    background: var(--gradient-primary);
    color: white;
}

.modern-dropdown-item i {
    width: 20px;
    text-align: center;
    margin-left: 0.5rem;
}

/* Search Highlight */
.search-highlight {
    background: rgba(37, 99, 235, 0.1);
    border-radius: 4px;
    transition: var(--transition);
}

/* Loading States */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Enhanced Cards */
.modern-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: none;
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.modern-card-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 1rem 1.5rem;
    border: none;
}

.modern-card-body {
    padding: 1.5rem;
}

/* Enhanced Tables */
.modern-table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.modern-table thead th {
    background: var(--gradient-primary);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.modern-table tbody tr {
    transition: var(--transition);
}

.modern-table tbody tr:hover {
    background: rgba(37, 99, 235, 0.05);
}

.modern-table tbody td {
    padding: 1rem;
    border-color: rgba(0,0,0,0.05);
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.875rem;
}

.status-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.status-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.status-info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

/* Enhanced Forms */
.modern-form-control {
    border: 2px solid #e5e7eb;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background: white;
}

.modern-form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.modern-form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

/* Notification Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
}
