{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load report_filters %}

{% block title %}{% trans "تقرير المخزون المتقدم" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
<style>
    /* متغيرات CSS للألوان والتدرجات */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);

        --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
        --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
        --shadow-lg: 0 8px 25px rgba(0,0,0,0.2);
        --shadow-xl: 0 20px 40px rgba(0,0,0,0.1);

        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* خلفية الصفحة الرئيسية */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Tajawal', sans-serif;
    }

    /* حاوي الصفحة الرئيسي */
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-xl);
        margin: 2rem auto;
        padding: 2rem;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* عنوان الصفحة */
    .page-header {
        background: var(--primary-gradient);
        color: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        margin-bottom: 2rem;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
        position: relative;
        z-index: 1;
    }

    /* بطاقات الإحصائيات */
    .stats-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stats-card.success::before { background: var(--success-gradient); }
    .stats-card.warning::before { background: var(--warning-gradient); }
    .stats-card.danger::before { background: var(--danger-gradient); }
    .stats-card.info::before { background: var(--info-gradient); }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
        background: var(--primary-gradient);
    }

    .stats-icon.success { background: var(--success-gradient); }
    .stats-icon.warning { background: var(--warning-gradient); }
    .stats-icon.danger { background: var(--danger-gradient); }
    .stats-icon.info { background: var(--info-gradient); }

    .stats-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .stats-change {
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }

    .stats-change.positive { color: #27ae60; }
    .stats-change.negative { color: #e74c3c; }

    /* قسم الفلاتر */
    .filter-section {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .filter-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .filter-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-toggle {
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: var(--transition);
    }

    .filter-toggle:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .filter-toggle i {
        transition: transform 0.3s ease;
    }

    .filter-toggle[aria-expanded="true"] i {
        transform: rotate(180deg);
    }

    #filterCollapse {
        overflow: hidden;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem;
        transition: var(--transition);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* الجداول */
    .table-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background: var(--primary-gradient);
        color: white;
        border: none;
        font-weight: 600;
        padding: 1rem 0.75rem;
        text-align: center;
        vertical-align: middle;
    }

    .table thead th:first-child {
        border-top-right-radius: 8px;
    }

    .table thead th:last-child {
        border-top-left-radius: 8px;
    }

    .table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f8f9fa;
    }

    .table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
    }

    /* حالات المخزون */
    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-available {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .status-low {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }

    .status-out {
        background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        color: white;
    }

    /* صور المنتجات */
    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: var(--shadow-sm);
    }

    /* الأزرار */
    .btn-gradient {
        background: var(--primary-gradient);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        transition: var(--transition);
    }

    .btn-gradient:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        color: white;
    }

    .btn-export {
        background: var(--success-gradient);
    }

    .btn-print {
        background: var(--info-gradient);
    }

    .btn-filter {
        background: var(--warning-gradient);
    }

    /* breadcrumb */
    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: var(--transition);
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: var(--transition);
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* الرسوم البيانية */
    .chart-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
        position: relative;
        height: 400px;
    }

    .chart-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
        text-align: center;
    }

    /* تنبيهات المخزون */
    .alert-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
    }

    .alert-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 8px;
        border-right: 4px solid #f6c23e;
        transition: var(--transition);
    }

    .alert-item:hover {
        transform: translateX(-5px);
        box-shadow: var(--shadow-sm);
    }

    .alert-item.critical {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-right-color: #e74a3b;
    }

    .alert-item.warning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-right-color: #f6c23e;
    }

    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 768px) {
        .main-container {
            margin: 1rem;
            padding: 1rem;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .stats-card {
            margin-bottom: 1rem;
        }

        .filter-section {
            padding: 1rem;
        }

        .table-responsive {
            font-size: 0.85rem;
        }

        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .btn-back {
            margin-right: 0;
            margin-top: 10px;
        }
    }

    /* تحسينات إضافية */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* تحسين navbar dropdowns */
    .navbar .dropdown-menu,
    .navbar-nav .dropdown-menu {
        max-height: none !important;
        overflow: visible !important;
        height: auto !important;
        min-width: 250px !important;
    }

    .navbar .dropdown-item {
        padding: 0.75rem 1rem !important;
        white-space: nowrap !important;
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        direction: ltr !important;
        min-width: 200px !important;
    }

    .navbar .dropdown-item i {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;
        order: 1 !important;
        flex-shrink: 0 !important;
        width: 16px !important;
        text-align: center !important;
    }

    .navbar .dropdown-item .dropdown-text {
        order: 2 !important;
        flex-grow: 1 !important;
        text-align: right !important;
    }
</style>
{% endblock %}
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    {% csrf_token %}

    <!-- عنوان الصفحة مع breadcrumb -->
    <div class="page-header">
        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
            <nav aria-label="breadcrumb" class="flex-grow-1">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{% url 'dashboard:index' %}">
                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'reports:index' %}">{% trans "التقارير" %}</a>
                    </li>
                    <li class="breadcrumb-item active text-white">{% trans "تقرير المخزون المتقدم" %}</li>
                </ol>
            </nav>
            <!-- زر العودة للصفحة السابقة -->
            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                <i class="fas fa-arrow-left"></i>
            </button>
        </div>

        <div class="text-center mt-3">
            <h1 class="page-title">
                <i class="fas fa-warehouse me-3"></i>{% trans "تقرير المخزون المتقدم" %}
            </h1>
            <p class="page-subtitle">{% trans "تحليل شامل ومتقدم لحالة المخزون والمنتجات" %}</p>
        </div>

        <!-- أزرار التصدير والطباعة -->
        <div class="text-center mt-4">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-gradient btn-export" onclick="exportReport('excel')">
                    <i class="fas fa-file-excel me-2"></i>{% trans "تصدير Excel" %}
                </button>
                <button type="button" class="btn btn-gradient btn-print" onclick="exportReport('pdf')">
                    <i class="fas fa-file-pdf me-2"></i>{% trans "تصدير PDF" %}
                </button>
                <button type="button" class="btn btn-gradient btn-filter" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>{% trans "طباعة" %}
                </button>
            </div>
        </div>
    </div>

    <!-- بطاقات الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-value">{{ total_products|default:0 }}</div>
                        <div class="stats-label">{% trans "إجمالي المنتجات" %}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up me-1"></i>+5.2% {% trans "من الشهر الماضي" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card success">
                <div class="d-flex align-items-center">
                    <div class="stats-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-value">{{ available_products|default:0 }}</div>
                        <div class="stats-label">{% trans "منتجات متوفرة" %}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up me-1"></i>+2.1% {% trans "من الأسبوع الماضي" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card warning">
                <div class="d-flex align-items-center">
                    <div class="stats-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-value">{{ low_stock_products|default:0 }}</div>
                        <div class="stats-label">{% trans "مخزون منخفض" %}</div>
                        <div class="stats-change negative">
                            <i class="fas fa-arrow-down me-1"></i>-1.3% {% trans "تحسن طفيف" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card danger">
                <div class="d-flex align-items-center">
                    <div class="stats-icon danger">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-value">{{ out_of_stock_products|default:0 }}</div>
                        <div class="stats-label">{% trans "نفد المخزون" %}</div>
                        <div class="stats-change negative">
                            <i class="fas fa-arrow-up me-1"></i>+0.8% {% trans "يحتاج انتباه" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card info">
                <div class="d-flex align-items-center">
                    <div class="stats-icon info">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-value">{{ total_inventory_value|default:0|floatformat:0 }}</div>
                        <div class="stats-label">{% trans "قيمة المخزون الإجمالية" %}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up me-1"></i>+3.7% {% trans "نمو مستمر" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-value">{{ total_categories|default:0 }}</div>
                        <div class="stats-label">{% trans "فئات المنتجات" %}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-plus me-1"></i>{% trans "فئة جديدة هذا الشهر" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card success">
                <div class="d-flex align-items-center">
                    <div class="stats-icon success">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="stats-value">{{ total_locations|default:0 }}</div>
                        <div class="stats-label">{% trans "مواقع التخزين" %}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-check me-1"></i>{% trans "جميعها نشطة" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم الفلاتر المتقدمة -->
    <div class="filter-section">
        <div class="filter-header">
            <h3 class="filter-title">
                <i class="fas fa-filter"></i>
                {% trans "فلاتر التقرير المتقدمة" %}
            </h3>
            <button type="button" class="filter-toggle" aria-expanded="true" aria-controls="filterCollapse">
                <i class="fas fa-chevron-down"></i>
                {% trans "إظهار/إخفاء الفلاتر" %}
            </button>
        </div>

        <div class="collapse show" id="filterCollapse">
            <form method="get" id="filterForm" action="{% url 'reports:inventory_report' %}">
                <div class="row">
                    <!-- فلتر البحث -->
                    <div class="col-md-4 mb-3">
                        <label for="search" class="form-label">
                            <i class="fas fa-search me-1"></i>{% trans "البحث في المنتجات" %}
                        </label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request.GET.search }}"
                               placeholder="{% trans 'اسم المنتج، الكود، أو الوصف...' %}">
                    </div>

                    <!-- فلتر الفئة -->
                    <div class="col-md-4 mb-3">
                        <label for="category" class="form-label">
                            <i class="fas fa-tags me-1"></i>{% trans "الفئة" %}
                        </label>
                        <select class="form-select" id="category" name="category">
                            <option value="">{% trans "جميع الفئات" %}</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}"
                                        {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- فلتر موقع التخزين -->
                    <div class="col-md-4 mb-3">
                        <label for="location" class="form-label">
                            <i class="fas fa-warehouse me-1"></i>{% trans "موقع التخزين" %}
                        </label>
                        <select class="form-select" id="location" name="location">
                            <option value="">{% trans "جميع المواقع" %}</option>
                            {% for location in storage_locations %}
                                <option value="{{ location.id }}"
                                        {% if request.GET.location == location.id|stringformat:"s" %}selected{% endif %}>
                                    {{ location.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- فلتر حالة المخزون -->
                    <div class="col-md-4 mb-3">
                        <label for="stock_status" class="form-label">
                            <i class="fas fa-chart-bar me-1"></i>{% trans "حالة المخزون" %}
                        </label>
                        <select class="form-select" id="stock_status" name="stock_status">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="available" {% if request.GET.stock_status == 'available' %}selected{% endif %}>
                                {% trans "متوفر" %}
                            </option>
                            <option value="low" {% if request.GET.stock_status == 'low' %}selected{% endif %}>
                                {% trans "مخزون منخفض" %}
                            </option>
                            <option value="out" {% if request.GET.stock_status == 'out' %}selected{% endif %}>
                                {% trans "نفد المخزون" %}
                            </option>
                        </select>
                    </div>

                    <!-- فلتر نطاق الكمية -->
                    <div class="col-md-4 mb-3">
                        <label for="min_quantity" class="form-label">
                            <i class="fas fa-sort-numeric-up me-1"></i>{% trans "الحد الأدنى للكمية" %}
                        </label>
                        <input type="number" class="form-control" id="min_quantity" name="min_quantity"
                               value="{{ request.GET.min_quantity }}" min="0"
                               placeholder="{% trans 'مثال: 10' %}">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="max_quantity" class="form-label">
                            <i class="fas fa-sort-numeric-down me-1"></i>{% trans "الحد الأقصى للكمية" %}
                        </label>
                        <input type="number" class="form-control" id="max_quantity" name="max_quantity"
                               value="{{ request.GET.max_quantity }}" min="0"
                               placeholder="{% trans 'مثال: 1000' %}">
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex gap-2 justify-content-center">
                            <button type="submit" class="btn btn-gradient">
                                <i class="fas fa-search me-2"></i>{% trans "تطبيق الفلاتر" %}
                            </button>
                            <button type="button" class="btn btn-gradient btn-filter" onclick="clearFilters()">
                                <i class="fas fa-eraser me-2"></i>{% trans "مسح الفلاتر" %}
                            </button>
                            <button type="button" class="btn btn-gradient btn-export" onclick="saveFilters()">
                                <i class="fas fa-save me-2"></i>{% trans "حفظ الفلاتر" %}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- جدول المنتجات المتقدم -->
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">
                <i class="fas fa-table me-2"></i>{% trans "تفاصيل المخزون" %}
            </h4>
            <div class="d-flex gap-2">
                <button class="btn btn-gradient btn-sm" onclick="refreshTable()">
                    <i class="fas fa-sync-alt me-1"></i>{% trans "تحديث" %}
                </button>
                <button class="btn btn-gradient btn-export btn-sm" onclick="exportTableData()">
                    <i class="fas fa-download me-1"></i>{% trans "تصدير البيانات" %}
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover" id="inventoryTable">
                <thead>
                    <tr>
                        <th>{% trans "الصورة" %}</th>
                        <th>{% trans "اسم المنتج" %}</th>
                        <th>{% trans "الكود" %}</th>
                        <th>{% trans "الفئة" %}</th>
                        <th>{% trans "الكمية الحالية" %}</th>
                        <th>{% trans "الحد الأدنى" %}</th>
                        <th>{% trans "موقع التخزين" %}</th>
                        <th>{% trans "السعر" %}</th>
                        <th>{% trans "القيمة الإجمالية" %}</th>
                        <th>{% trans "الحالة" %}</th>
                        <th>{% trans "آخر تحديث" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>
                            {% if product.image %}
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
                            {% else %}
                                <div class="product-image d-flex align-items-center justify-content-center bg-light">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ product.name }}</strong>
                            {% if product.description %}
                                <br><small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                            {% endif %}
                        </td>
                        <td><code>{{ product.code }}</code></td>
                        <td>
                            <span class="badge bg-secondary">{{ product.category.name|default:"غير محدد" }}</span>
                        </td>
                        <td>
                            <span class="fw-bold">{{ product.quantity }}</span>
                            {% if product.unit %}
                                <small class="text-muted">{{ product.unit }}</small>
                            {% endif %}
                        </td>
                        <td>{{ product.min_quantity|default:"-" }}</td>
                        <td>
                            {% if product.storage_location %}
                                <i class="fas fa-map-marker-alt me-1"></i>{{ product.storage_location.name }}
                            {% else %}
                                <span class="text-muted">{% trans "غير محدد" %}</span>
                            {% endif %}
                        </td>
                        <td>{{ product.price|floatformat:2 }} {% trans "ريال" %}</td>
                        <td>
                            <strong>{{ product.quantity|mul:product.price|floatformat:2 }} {% trans "ريال" %}</strong>
                        </td>
                        <td>
                            {% if product.quantity <= 0 %}
                                <span class="status-badge status-out">{% trans "نفد المخزون" %}</span>
                            {% elif product.quantity <= product.min_quantity %}
                                <span class="status-badge status-low">{% trans "مخزون منخفض" %}</span>
                            {% else %}
                                <span class="status-badge status-available">{% trans "متوفر" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ product.updated_at|date:"Y-m-d H:i" }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'inventory:product_detail' product.id %}"
                                   class="btn btn-outline-primary btn-sm" title="{% trans 'عرض التفاصيل' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'inventory:edit_product' product.id %}"
                                   class="btn btn-outline-warning btn-sm" title="{% trans 'تعديل' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-info btn-sm"
                                        onclick="showProductHistory({{ product.id }})"
                                        title="{% trans 'تاريخ المنتج' %}">
                                    <i class="fas fa-history"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="12" class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "لا توجد منتجات تطابق المعايير المحددة" %}</h5>
                            <p class="text-muted">{% trans "جرب تغيير الفلاتر أو إضافة منتجات جديدة" %}</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if products.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if products.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in products.paginator.page_range %}
                    {% if products.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > products.number|add:'-3' and num < products.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.paginator.num_pages }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>

</div>
{% endblock %}

{% block extra_js %}
<!-- jQuery and Bootstrap are already loaded in base.html -->
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
// تهيئة الصفحة
$(document).ready(function() {
    // تهيئة DataTable
    initializeDataTable();

    // تهيئة الفلاتر
    initializeFilters();

    // تهيئة زر العودة
    initializeBackButton();

    // تهيئة القوائم المنسدلة في الشريط العلوي
    initializeNavbarDropdowns();
});

// تهيئة DataTable
function initializeDataTable() {
    $('#inventoryTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'asc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel me-1"></i>Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf me-1"></i>PDF',
                className: 'btn btn-danger btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print me-1"></i>طباعة',
                className: 'btn btn-info btn-sm'
            }
        ],
        columnDefs: [
            { orderable: false, targets: [0, 11] }, // الصورة والإجراءات غير قابلة للترتيب
            { className: 'text-center', targets: [0, 4, 5, 9, 11] }
        ]
    });
}

// تهيئة الفلاتر
function initializeFilters() {
    // تهيئة زر إظهار/إخفاء الفلاتر
    $('.filter-toggle').click(function() {
        const $filterCollapse = $('#filterCollapse');
        const $icon = $(this).find('i');
        const isVisible = $filterCollapse.is(':visible');

        if (isVisible) {
            $filterCollapse.slideUp(300);
            $(this).attr('aria-expanded', 'false');
            $icon.css('transform', 'rotate(0deg)');
        } else {
            $filterCollapse.slideDown(300);
            $(this).attr('aria-expanded', 'true');
            $icon.css('transform', 'rotate(180deg)');
        }
    });

    // تهيئة Select2 للقوائم المنسدلة
    $('.form-select').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });
}

// وظائف الفلاتر
function clearFilters() {
    $('#filterForm')[0].reset();
    $('.form-select').val('').trigger('change');
    window.location.href = window.location.pathname;
}

function saveFilters() {
    const formData = $('#filterForm').serialize();
    localStorage.setItem('inventoryFilters', formData);
    showNotification('تم حفظ الفلاتر بنجاح', 'success');
}

function loadSavedFilters() {
    const savedFilters = localStorage.getItem('inventoryFilters');
    if (savedFilters) {
        const params = new URLSearchParams(savedFilters);
        params.forEach((value, key) => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = value;
                if (element.classList.contains('form-select')) {
                    $(element).trigger('change');
                }
            }
        });
    }
}

// وظائف التصدير
function exportReport(format) {
    showLoading();

    const formData = $('#filterForm').serialize();
    const url = `{% url 'reports:inventory_report' %}?export=${format}&${formData}`;

    // إنشاء رابط تحميل مخفي
    const link = document.createElement('a');
    link.href = url;
    link.download = `inventory_report_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => {
        hideLoading();
        showNotification(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`, 'success');
    }, 2000);
}

function exportTableData() {
    const table = $('#inventoryTable').DataTable();
    table.button('.buttons-excel').trigger();
}

function refreshTable() {
    showLoading();
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// وظائف المنتجات
function showProductHistory(productId) {
    // يمكن إضافة modal لعرض تاريخ المنتج
    showNotification('ميزة تاريخ المنتج قيد التطوير', 'info');
}

// وظائف المساعدة
function showLoading() {
    if (!document.getElementById('loadingOverlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="text-center">
                <div class="loading-spinner"></div>
                <h5 class="mt-3 text-primary">جاري التحميل...</h5>
            </div>
        `;
        document.body.appendChild(overlay);
    }
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.remove();
    }
}

function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const icon = type === 'success' ? 'fas fa-check-circle' :
                 type === 'error' ? 'fas fa-exclamation-circle' :
                 type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

    const notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed shadow-lg"
             style="top: 20px; left: 20px; z-index: 9999; min-width: 300px; max-width: 500px;">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(notification);

    setTimeout(() => {
        notification.alert('close');
    }, 5000);
}

// وظيفة العودة للصفحة السابقة
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = "{% url 'reports:index' %}";
    }
}

// تهيئة زر العودة
function initializeBackButton() {
    // لا حاجة لكود إضافي، الزر يعمل مع onclick
}

// تهيئة القوائم المنسدلة في الشريط العلوي
function initializeNavbarDropdowns() {
    const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
    const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    $('.dropdown-toggle').off('click.bs.dropdown').on('click', function(e) {
        e.preventDefault();
        const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
        dropdown.toggle();
    });

    fixNavbarDropdownLayout();
}

// إصلاح تخطيط عناصر القائمة المنسدلة
function fixNavbarDropdownLayout() {
    $('.navbar .dropdown-item').each(function() {
        const $item = $(this);
        const $icon = $item.find('i');
        const text = $item.text().trim();

        if ($icon.length > 0 && text) {
            $item.html(`
                <i class="${$icon.attr('class')}"></i>
                <span class="dropdown-text">${text}</span>
            `);
        }
    });
}

// تحميل الفلاتر المحفوظة عند تحميل الصفحة
$(document).ready(function() {
    loadSavedFilters();
});
</script>
{% endblock %}
