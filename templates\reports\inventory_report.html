{% extends 'base.html' %}
{% load i18n %}
{% load static %}

<!-- CSRF Token for AJAX requests -->
<meta name="csrf-token" content="{{ csrf_token }}">
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

{% block title %}{% trans "تقرير المخزون المتقدم" %} | {% trans "زاكورة" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    * {
        box-sizing: border-box;
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* تحسين responsive للشاشات الصغيرة */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .btn-back {
            margin-right: 0;
            margin-top: 10px;
        }
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
    }

    .stats-card.success::before { background: var(--gradient-success); }
    .stats-card.warning::before { background: var(--gradient-warning); }
    .stats-card.info::before { background: var(--gradient-info); }
    .stats-card.danger::before { background: var(--gradient-danger); }

    .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .stats-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--secondary-color);
        margin: 0;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        background: var(--gradient-primary);
        box-shadow: var(--shadow-md);
    }

    .stats-value {
        font-size: 2.2rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stats-change {
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-weight: 500;
    }

    .stats-change.positive { color: var(--success-color); }
    .stats-change.negative { color: var(--danger-color); }

    .stats-card.success .stats-icon { background: var(--gradient-success); }
    .stats-card.warning .stats-icon { background: var(--gradient-warning); }
    .stats-card.info .stats-icon { background: var(--gradient-info); }
    .stats-card.danger .stats-icon { background: var(--gradient-danger); }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
    }

    .filter-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-toggle {
        background: var(--gradient-primary);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .filter-toggle:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .filter-toggle i {
        transition: transform 0.3s ease;
    }

    .filter-toggle[aria-expanded="true"] i {
        transform: rotate(180deg);
    }

    #filterCollapse {
        overflow: hidden;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        outline: none;
    }

    .btn-primary {
        background: var(--gradient-primary);
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-secondary {
        background: var(--gradient-info);
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-success {
        background: var(--gradient-success);
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    /* Table Section */
    .table-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
    }

    .table-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table {
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .table thead th {
        background: var(--gradient-primary);
        color: white;
        border: none;
        font-weight: 600;
        padding: 1rem 0.75rem;
        text-align: center;
        vertical-align: middle;
        white-space: nowrap;
    }

    .table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid var(--border-color);
        text-align: center;
    }

    .table tbody tr:hover {
        background-color: rgba(37, 99, 235, 0.05);
    }

    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: var(--shadow-sm);
    }

    .product-placeholder {
        width: 50px;
        height: 50px;
        background: var(--light-color);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--secondary-color);
        border: 2px dashed var(--border-color);
    }

    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-available {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .status-low {
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        color: white;
    }

    .status-out {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
    }

    .btn-group .btn {
        border-radius: 6px;
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
        margin: 0 0.1rem;
    }

    /* Pagination */
    .pagination {
        justify-content: center;
        margin-top: 2rem;
    }

    .page-link {
        border-radius: 8px;
        margin: 0 0.2rem;
        border: 2px solid var(--border-color);
        color: var(--primary-color);
        font-weight: 500;
    }

    .page-link:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .page-item.active .page-link {
        background: var(--gradient-primary);
        border-color: var(--primary-color);
    }

    /* تحسين navbar dropdowns */
    .navbar .dropdown-menu,
    .navbar-nav .dropdown-menu {
        max-height: none !important;
        overflow: visible !important;
        height: auto !important;
        min-width: 250px !important;
    }

    .navbar .dropdown-item {
        padding: 0.75rem 1rem !important;
        white-space: nowrap !important;
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        direction: ltr !important;
        min-width: 200px !important;
    }

    .navbar .dropdown-item i {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;
        order: 1 !important;
        flex-shrink: 0 !important;
        width: 16px !important;
        text-align: center !important;
    }

    .navbar .dropdown-item .dropdown-text {
        order: 2 !important;
        flex-grow: 1 !important;
        text-align: right !important;
    }

    .navbar .dropdown-item:hover {
        background-color: #f8f9fa !important;
        color: #495057 !important;
    }

    /* خاص بقائمة المستخدم (admin) - منع التمرير الأفقي */
    #userDropdown + .dropdown-menu {
        min-width: 160px !important;
        max-width: 180px !important;
        right: 0 !important;
        left: auto !important;
        transform: none !important;
    }

    /* منع التمرير الأفقي في الصفحة */
    body {
        overflow-x: hidden !important;
    }

    /* ضمان عدم تجاوز القوائم المنسدلة لحدود الشاشة */
    .navbar .dropdown-menu {
        max-width: calc(100vw - 40px) !important;
        box-sizing: border-box !important;
    }

    /* تحسينات إضافية للشاشات الصغيرة */
    @media (max-width: 768px) {
        .page-title {
            font-size: 1.8rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .filter-section {
            padding: 1rem;
        }

        .table-responsive {
            font-size: 0.8rem;
        }

        .table thead th,
        .table tbody td {
            padding: 0.5rem 0.25rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .stats-card {
            padding: 1rem;
        }

        .stats-value {
            font-size: 1.8rem;
        }
    }
</style>
{% endblock %}

    /* بطاقات الإحصائيات */
    .stats-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stats-card.success::before { background: var(--success-gradient); }
    .stats-card.warning::before { background: var(--warning-gradient); }
    .stats-card.danger::before { background: var(--danger-gradient); }
    .stats-card.info::before { background: var(--info-gradient); }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
        background: var(--primary-gradient);
    }

    .stats-icon.success { background: var(--success-gradient); }
    .stats-icon.warning { background: var(--warning-gradient); }
    .stats-icon.danger { background: var(--danger-gradient); }
    .stats-icon.info { background: var(--info-gradient); }

    .stats-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .stats-change {
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }

    .stats-change.positive { color: #27ae60; }
    .stats-change.negative { color: #e74c3c; }

    /* قسم الفلاتر */
    .filter-section {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .filter-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .filter-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-toggle {
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: var(--transition);
    }

    .filter-toggle:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .filter-toggle i {
        transition: transform 0.3s ease;
    }

    .filter-toggle[aria-expanded="true"] i {
        transform: rotate(180deg);
    }

    #filterCollapse {
        overflow: hidden;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem;
        transition: var(--transition);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* الجداول */
    .table-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background: var(--primary-gradient);
        color: white;
        border: none;
        font-weight: 600;
        padding: 1rem 0.75rem;
        text-align: center;
        vertical-align: middle;
    }

    .table thead th:first-child {
        border-top-right-radius: 8px;
    }

    .table thead th:last-child {
        border-top-left-radius: 8px;
    }

    .table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f8f9fa;
    }

    .table tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
    }

    /* حالات المخزون */
    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-available {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .status-low {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }

    .status-out {
        background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        color: white;
    }

    /* صور المنتجات */
    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: var(--shadow-sm);
    }

    /* الأزرار */
    .btn-gradient {
        background: var(--primary-gradient);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        transition: var(--transition);
    }

    .btn-gradient:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        color: white;
    }

    .btn-export {
        background: var(--success-gradient);
    }

    .btn-print {
        background: var(--info-gradient);
    }

    .btn-filter {
        background: var(--warning-gradient);
    }

    /* breadcrumb */
    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: var(--transition);
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: var(--transition);
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* الرسوم البيانية */
    .chart-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
        position: relative;
        height: 400px;
    }

    .chart-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
        text-align: center;
    }

    /* تنبيهات المخزون */
    .alert-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        margin-bottom: 2rem;
    }

    .alert-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 8px;
        border-right: 4px solid #f6c23e;
        transition: var(--transition);
    }

    .alert-item:hover {
        transform: translateX(-5px);
        box-shadow: var(--shadow-sm);
    }

    .alert-item.critical {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-right-color: #e74a3b;
    }

    .alert-item.warning {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-right-color: #f6c23e;
    }

    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 768px) {
        .main-container {
            margin: 1rem;
            padding: 1rem;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .stats-card {
            margin-bottom: 1rem;
        }

        .filter-section {
            padding: 1rem;
        }

        .table-responsive {
            font-size: 0.85rem;
        }

        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .btn-back {
            margin-right: 0;
            margin-top: 10px;
        }
    }

    /* تحسينات إضافية */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* تحسين navbar dropdowns */
    .navbar .dropdown-menu,
    .navbar-nav .dropdown-menu {
        max-height: none !important;
        overflow: visible !important;
        height: auto !important;
        min-width: 250px !important;
    }

    .navbar .dropdown-item {
        padding: 0.75rem 1rem !important;
        white-space: nowrap !important;
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        direction: ltr !important;
        min-width: 200px !important;
    }

    .navbar .dropdown-item i {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;
        order: 1 !important;
        flex-shrink: 0 !important;
        width: 16px !important;
        text-align: center !important;
    }

    .navbar .dropdown-item .dropdown-text {
        order: 2 !important;
        flex-grow: 1 !important;
        text-align: right !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    {% csrf_token %}

    <!-- Header Section -->
    <div class="page-header">
        <div class="container">
            <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                <nav aria-label="breadcrumb" class="flex-grow-1">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{% url 'dashboard:index' %}">
                                <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{% url 'reports:index' %}">{% trans "التقارير" %}</a>
                        </li>
                        <li class="breadcrumb-item active text-white">{% trans "تقرير المخزون المتقدم" %}</li>
                    </ol>
                </nav>
                <!-- زر العودة للصفحة السابقة -->
                <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>

            <div class="text-center mt-4">
                <h1 class="page-title">
                    <i class="fas fa-warehouse me-3"></i>{% trans "تقرير المخزون المتقدم" %}
                </h1>
                <p class="page-subtitle">{% trans "تحليل شامل ومتقدم لحالة المخزون والمنتجات" %}</p>
            </div>

            <div class="header-actions text-center mt-4">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-light" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>{% trans "تصدير Excel" %}
                    </button>
                    <button type="button" class="btn btn-light" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>{% trans "تصدير PDF" %}
                    </button>
                    <button type="button" class="btn btn-light" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>{% trans "طباعة" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-header">
                    <div>
                        <h3 class="stats-title">{% trans "إجمالي المنتجات" %}</h3>
                        <div class="stats-value">{{ total_products|default:0 }}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +5.2% {% trans "من الشهر الماضي" %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
            </div>

            <div class="stats-card success">
                <div class="stats-header">
                    <div>
                        <h3 class="stats-title">{% trans "منتجات متوفرة" %}</h3>
                        <div class="stats-value">{{ available_products|default:0 }}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +2.1% {% trans "من الأسبوع الماضي" %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>

            <div class="stats-card warning">
                <div class="stats-header">
                    <div>
                        <h3 class="stats-title">{% trans "مخزون منخفض" %}</h3>
                        <div class="stats-value">{{ low_stock_products|default:0 }}</div>
                        <div class="stats-change negative">
                            <i class="fas fa-arrow-down"></i>
                            -1.3% {% trans "تحسن طفيف" %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>

            <div class="stats-card danger">
                <div class="stats-header">
                    <div>
                        <h3 class="stats-title">{% trans "نفد المخزون" %}</h3>
                        <div class="stats-value">{{ out_of_stock_products|default:0 }}</div>
                        <div class="stats-change negative">
                            <i class="fas fa-arrow-up"></i>
                            +0.8% {% trans "يحتاج انتباه" %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
            </div>

            <div class="stats-card info">
                <div class="stats-header">
                    <div>
                        <h3 class="stats-title">{% trans "قيمة المخزون" %}</h3>
                        <div class="stats-value">{{ total_inventory_value|default:0|floatformat:0 }}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +3.7% {% trans "نمو مستمر" %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-header">
                    <div>
                        <h3 class="stats-title">{% trans "فئات المنتجات" %}</h3>
                        <div class="stats-value">{{ total_categories|default:0 }}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-plus"></i>
                            {% trans "فئة جديدة هذا الشهر" %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <div class="filter-header">
                <h3 class="filter-title">
                    <i class="fas fa-filter"></i>
                    {% trans "فلاتر التقرير المتقدمة" %}
                </h3>
                <button type="button" class="filter-toggle" aria-expanded="true" aria-controls="filterCollapse">
                    <i class="fas fa-chevron-down"></i>
                    {% trans "إظهار/إخفاء الفلاتر" %}
                </button>
            </div>

            <div class="collapse show" id="filterCollapse">
                <form method="get" id="filterForm" action="{% url 'reports:inventory_report' %}">
                    <div class="row">
                        <!-- فلتر البحث -->
                        <div class="col-md-4 mb-3">
                            <label for="search" class="form-label">
                                <i class="fas fa-search"></i>{% trans "البحث في المنتجات" %}
                            </label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ request.GET.search }}"
                                   placeholder="{% trans 'اسم المنتج، الكود، أو الوصف...' %}">
                        </div>

                        <!-- فلتر الفئة -->
                        <div class="col-md-4 mb-3">
                            <label for="category" class="form-label">
                                <i class="fas fa-tags"></i>{% trans "الفئة" %}
                            </label>
                            <select class="form-select" id="category" name="category">
                                <option value="">{% trans "جميع الفئات" %}</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}"
                                            {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- فلتر حالة المخزون -->
                        <div class="col-md-4 mb-3">
                            <label for="stock_status" class="form-label">
                                <i class="fas fa-chart-bar"></i>{% trans "حالة المخزون" %}
                            </label>
                            <select class="form-select" id="stock_status" name="stock_status">
                                <option value="">{% trans "جميع الحالات" %}</option>
                                <option value="available" {% if request.GET.stock_status == 'available' %}selected{% endif %}>
                                    {% trans "متوفر" %}
                                </option>
                                <option value="low" {% if request.GET.stock_status == 'low' %}selected{% endif %}>
                                    {% trans "مخزون منخفض" %}
                                </option>
                                <option value="out" {% if request.GET.stock_status == 'out' %}selected{% endif %}>
                                    {% trans "نفد المخزون" %}
                                </option>
                            </select>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex gap-2 justify-content-center">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>{% trans "تطبيق الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-eraser me-2"></i>{% trans "مسح الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-success" onclick="saveFilters()">
                                    <i class="fas fa-save me-2"></i>{% trans "حفظ الفلاتر" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Table Section -->
        <div class="table-section">
            <div class="table-header">
                <h3 class="table-title">
                    <i class="fas fa-table"></i>
                    {% trans "جدول المنتجات التفصيلي" %}
                </h3>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary btn-sm" onclick="refreshTable()">
                        <i class="fas fa-sync-alt me-1"></i>{% trans "تحديث" %}
                    </button>
                    <button type="button" class="btn btn-success btn-sm" onclick="exportTableData()">
                        <i class="fas fa-download me-1"></i>{% trans "تصدير البيانات" %}
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover" id="inventoryTable">
                    <thead>
                        <tr>
                            <th>{% trans "الصورة" %}</th>
                            <th>{% trans "الكود" %}</th>
                            <th>{% trans "اسم المنتج" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "الكمية" %}</th>
                            <th>{% trans "سعر البيع" %}</th>
                            <th>{% trans "القيمة الإجمالية" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td>
                                {% if product.image %}
                                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
                                {% else %}
                                    <div class="product-placeholder">
                                        <i class="fas fa-image"></i>
                                    </div>
                                {% endif %}
                            </td>
                            <td><strong>{{ product.code }}</strong></td>
                            <td>{{ product.name }}</td>
                            <td>
                                {% if product.category %}
                                    <span class="badge bg-primary">{{ product.category.name }}</span>
                                {% else %}
                                    <span class="text-muted">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="fw-bold">{{ product.quantity }}</span>
                                {% if product.display_unit %}
                                    <small class="text-muted">{{ product.display_unit }}</small>
                                {% endif %}
                            </td>
                            <td>{{ product.selling_price|floatformat:2 }} {% trans "ريال" %}</td>
                            <td>
                                {% widthratio product.quantity 1 product.selling_price as total_value %}
                                <strong>{{ total_value|floatformat:2 }} {% trans "ريال" %}</strong>
                            </td>
                            <td>
                                {% if product.quantity == 0 %}
                                    <span class="status-badge status-out">{% trans "نفد المخزون" %}</span>
                                {% elif product.is_low_stock %}
                                    <span class="status-badge status-low">{% trans "مخزون منخفض" %}</span>
                                {% else %}
                                    <span class="status-badge status-available">{% trans "متوفر" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'inventory:edit_product' product.id %}"
                                       class="btn btn-primary btn-sm" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-info btn-sm"
                                            onclick="viewProductDetails({{ product.id }})" title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm"
                                            onclick="updateStock({{ product.id }})" title="{% trans 'تحديث المخزون' %}">
                                        <i class="fas fa-boxes"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <h5>{% trans "لا توجد منتجات" %}</h5>
                                    <p>{% trans "لم يتم العثور على منتجات تطابق معايير البحث المحددة" %}</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
// تهيئة DataTable
$(document).ready(function() {
    $('#inventoryTable').DataTable({
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> طباعة',
                className: 'btn btn-info btn-sm'
            }
        ],
        order: [[1, 'asc']],
        columnDefs: [
            { orderable: false, targets: [0, 8] },
            { className: 'text-center', targets: '_all' }
        ]
    });
});

// تبديل عرض الفلاتر
$('.filter-toggle').click(function() {
    $('#filterCollapse').collapse('toggle');
});

// مسح الفلاتر
function clearFilters() {
    $('#filterForm')[0].reset();
    window.location.href = '{% url "reports:inventory_report" %}';
}

// حفظ الفلاتر
function saveFilters() {
    const filters = {
        search: $('#search').val(),
        category: $('#category').val(),
        stock_status: $('#stock_status').val()
    };
    localStorage.setItem('inventory_filters', JSON.stringify(filters));

    // إظهار رسالة نجاح
    showNotification('تم حفظ الفلاتر بنجاح', 'success');
}

// تحميل الفلاتر المحفوظة
function loadSavedFilters() {
    const savedFilters = localStorage.getItem('inventory_filters');
    if (savedFilters) {
        const filters = JSON.parse(savedFilters);
        $('#search').val(filters.search || '');
        $('#category').val(filters.category || '');
        $('#stock_status').val(filters.stock_status || '');
    }
}

// تصدير التقرير
function exportReport(format) {
    const url = new URL(window.location.href);
    url.searchParams.set('export', format);
    window.open(url.toString(), '_blank');
}

// تحديث الجدول
function refreshTable() {
    location.reload();
}

// تصدير بيانات الجدول
function exportTableData() {
    $('#inventoryTable').DataTable().button('.buttons-excel').trigger();
}

// عرض تفاصيل المنتج
function viewProductDetails(productId) {
    // يمكن إضافة modal أو توجيه لصفحة التفاصيل
    window.open(`/inventory/product/${productId}/`, '_blank');
}

// تحديث المخزون
function updateStock(productId) {
    // يمكن إضافة modal لتحديث المخزون
    window.open(`/inventory/edit/${productId}/`, '_blank');
}

// العودة للصفحة السابقة
function goBack() {
    if (document.referrer) {
        window.history.back();
    } else {
        window.location.href = '{% url "reports:index" %}';
    }
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // يمكن استخدام مكتبة إشعارات مثل toastr
    alert(message);
}

// تحميل الفلاتر المحفوظة عند تحميل الصفحة
$(document).ready(function() {
    loadSavedFilters();
});
</script>

{% endblock %}
