<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص المدفوعات</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .code {
            background: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تشخيص المدفوعات للبيع #{{ sale.invoice_number }}</h1>
        
        <div class="info-box">
            <h3>معلومات البيع الأساسية</h3>
            <p><strong>رقم البيع:</strong> {{ sale.id }}</p>
            <p><strong>رقم الفاتورة:</strong> {{ sale.invoice_number }}</p>
            <p><strong>العميل:</strong> {{ sale.customer.name }}</p>
            <p><strong>التاريخ:</strong> {{ sale.date }}</p>
            <p><strong>المبلغ الإجمالي:</strong> {{ sale.total_amount }} درهم</p>
            <p><strong>الحالة:</strong> {{ sale.status }}</p>
        </div>

        <div class="info-box">
            <h3>إحصائيات المدفوعات</h3>
            <p><strong>عدد المدفوعات:</strong> {{ payments.count }}</p>
            <p><strong>المجموع المدفوع:</strong> {{ total_paid }} درهم</p>
            <p><strong>المبلغ المتبقي:</strong> {{ remaining_amount }} درهم</p>
        </div>

        {% if payments %}
        <div class="success-box">
            <h3>✅ المدفوعات الموجودة</h3>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>تاريخ الدفع</th>
                        <th>المرجع</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td>{{ payment.id }}</td>
                        <td>{{ payment.amount }} درهم</td>
                        <td>{{ payment.get_payment_method_display }}</td>
                        <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                        <td>{{ payment.reference|default:"-" }}</td>
                        <td>{{ payment.notes|default:"-" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="error-box">
            <h3>❌ لا توجد مدفوعات</h3>
            <p>لم يتم العثور على أي مدفوعات لهذا البيع.</p>
            <p><strong>السبب المحتمل:</strong></p>
            <ul>
                <li>لم يتم إضافة أي مدفوعات لهذا البيع</li>
                <li>البيع تم إنشاؤه بدون دفعات</li>
                <li>مشكلة في قاعدة البيانات</li>
            </ul>
        </div>
        {% endif %}

        <div class="info-box">
            <h3>اختبار الاستعلام</h3>
            <p><strong>استعلام المدفوعات:</strong> <span class="code">sale.payments.all()</span></p>
            <p><strong>عدد النتائج:</strong> {{ payments.count }}</p>
            <p><strong>استعلام المجموع:</strong> <span class="code">payments.aggregate(Sum('amount'))['amount__sum']</span></p>
            <p><strong>النتيجة:</strong> {{ total_paid }}</p>
        </div>

        <div class="info-box">
            <h3>إجراءات مقترحة</h3>
            {% if not payments %}
            <p><strong>لإضافة دفعة جديدة:</strong></p>
            <ol>
                <li>انتقل إلى صفحة تفاصيل البيع</li>
                <li>اضغط على زر "إضافة دفعة"</li>
                <li>أدخل مبلغ الدفعة وطريقة الدفع</li>
                <li>احفظ الدفعة</li>
            </ol>
            {% else %}
            <p><strong>المدفوعات موجودة ويجب أن تظهر في الفاتورة.</strong></p>
            <p>إذا لم تظهر في الفاتورة، تحقق من:</p>
            <ul>
                <li>قالب الفاتورة (invoice_content.html)</li>
                <li>إعدادات عرض معلومات الدفع</li>
                <li>JavaScript الخاص بإخفاء/إظهار العناصر</li>
            </ul>
            {% endif %}
        </div>

        <div class="info-box">
            <h3>روابط مفيدة</h3>
            <p><a href="/sales/sale_detail/{{ sale.id }}/" style="color: #2196f3; text-decoration: none;">📋 عرض تفاصيل البيع</a></p>
            <p><a href="/sales/invoice/{{ sale.id }}/" style="color: #2196f3; text-decoration: none;">📄 عرض الفاتورة</a></p>
            <p><a href="/sales/add_payment/{{ sale.id }}/" style="color: #4caf50; text-decoration: none;">💰 إضافة دفعة جديدة</a></p>
            {% if not payments %}
            <p><a href="/sales/create-test-payment/{{ sale.id }}/" style="color: #ff9800; text-decoration: none; background: #fff3e0; padding: 5px 10px; border-radius: 3px; display: inline-block; margin-top: 10px;">🧪 إنشاء دفعة تجريبية (100 درهم)</a></p>
            {% endif %}
        </div>
    </div>
</body>
</html>
