<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #{{ sale.invoice_number }}</title>
    {% load static %}
    
    <style>
        /* CSS مضمن للحجم الكامل */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html {
            width: 210mm;
            height: 297mm;
            font-size: 16px;
        }
        
        body {
            width: 100%;
            height: 100%;
            font-family: '<PERSON><PERSON><PERSON>', '<PERSON>homa', 'Arial Unicode MS', sans-serif;
            font-size: 14pt;
            line-height: 1.6;
            color: #000;
            background: white;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
        }
        
        .invoice-container {
            width: 100%;
            height: 100%;
            padding: 20mm;
            background: white;
            box-sizing: border-box;
        }
        
        /* العناوين */
        h1 { font-size: 28pt; font-weight: bold; margin-bottom: 15px; }
        h2 { font-size: 24pt; font-weight: bold; margin-bottom: 12px; }
        h3 { font-size: 20pt; font-weight: bold; margin-bottom: 10px; }
        h4 { font-size: 18pt; font-weight: bold; margin-bottom: 8px; }
        h5 { font-size: 16pt; font-weight: bold; margin-bottom: 8px; }
        
        /* الفقرات */
        p {
            font-size: 14pt;
            line-height: 1.6;
            margin-bottom: 12px;
        }
        
        /* الجداول */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            font-size: 13pt;
            border: 2px solid #000;
        }
        
        th, td {
            padding: 12px 8px;
            border: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            font-size: 13pt;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        /* الصفوف */
        .row {
            width: 100%;
            margin-bottom: 20px;
            display: table;
            clear: both;
        }
        
        .col-md-6 {
            width: 50%;
            float: right;
            padding: 0 15px;
            display: table-cell;
            vertical-align: top;
        }
        
        /* رأس الفاتورة */
        .invoice-header {
            border-bottom: 3px solid #000;
            padding-bottom: 25px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .invoice-title {
            font-size: 28pt;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        /* قوائم التعريف */
        dt {
            font-weight: bold;
            font-size: 14pt;
            margin-bottom: 8px;
            display: inline-block;
            width: 40%;
            vertical-align: top;
        }
        
        dd {
            font-size: 14pt;
            margin-bottom: 12px;
            display: inline-block;
            width: 58%;
            vertical-align: top;
            padding-right: 10px;
        }
        
        /* الأرقام */
        .arabic-number {
            font-family: 'Tajawal', 'Tahoma', monospace;
            font-weight: 600;
            direction: ltr;
            display: inline-block;
        }
        
        /* الشارات */
        .badge {
            display: inline-block;
            padding: 8px 12px;
            font-size: 12pt;
            font-weight: bold;
            border-radius: 4px;
            color: white;
            background-color: #007bff;
        }
        
        /* معلومات الدفع */
        .payment-info {
            background-color: #f8f9fa;
            border: 2px solid #007bff;
            padding: 20px;
            margin-bottom: 25px;
            border-radius: 8px;
        }
        
        .payment-status.paid {
            background-color: #28a745;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
        }
        
        .payment-status.partial {
            background-color: #ffc107;
            color: #000;
            padding: 6px 12px;
            border-radius: 4px;
        }
        
        .payment-status.unpaid {
            background-color: #dc3545;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
        }
        
        /* ملخص الفاتورة */
        .invoice-summary {
            background-color: #f8f9fa;
            border: 2px solid #007bff;
            padding: 25px;
            margin-bottom: 30px;
            border-radius: 8px;
        }
        
        .invoice-summary .d-flex {
            display: table;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .invoice-summary .d-flex span:first-child {
            display: table-cell;
            width: 70%;
            font-weight: 600;
            text-align: right;
        }
        
        .invoice-summary .d-flex span:last-child {
            display: table-cell;
            width: 30%;
            text-align: left;
            font-weight: 700;
        }
        
        .total-amount {
            font-size: 18pt;
            font-weight: 700;
            color: #007bff;
        }
        
        /* منطقة التوقيع */
        .signature-area {
            margin-top: 40px;
            display: table;
            width: 100%;
        }
        
        .signature-box {
            display: table-cell;
            width: 33.333%;
            text-align: center;
            padding: 0 20px;
            vertical-align: top;
        }
        
        .signature-line {
            border-top: 2px solid #000;
            margin-top: 60px;
            margin-bottom: 10px;
        }
        
        /* QR Code */
        .qr-code {
            text-align: center;
            margin: 25px auto;
        }
        
        .qr-code img {
            max-width: 120px;
            max-height: 120px;
            border: 1px solid #ddd;
            padding: 5px;
        }
        
        /* إعدادات الطباعة */
        @page {
            size: A4;
            margin: 15mm;
        }
        
        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
            }
            
            .invoice-container {
                width: 100%;
                height: 100%;
                padding: 15mm;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            {% if company_info %}
                <h1>{{ company_info.name }}</h1>
                {% if company_info.address %}
                    <p>{{ company_info.address }}</p>
                {% endif %}
                {% if company_info.phone %}
                    <p>هاتف: {{ company_info.phone }}</p>
                {% endif %}
            {% else %}
                <h1>فاتورة مبيعات</h1>
            {% endif %}
            <h2>فاتورة #{{ sale.invoice_number }}</h2>
            <p><strong>التاريخ:</strong> {{ sale.date|date:"Y-m-d H:i" }}</p>
        </div>

        <!-- معلومات البيع والعميل -->
        <div class="row">
            <div class="col-md-6">
                <h5>معلومات العميل</h5>
                <dl>
                    <dt>الاسم:</dt>
                    <dd>{{ sale.customer.name }}</dd>
                    {% if sale.customer.phone %}
                    <dt>الهاتف:</dt>
                    <dd class="arabic-number">{{ sale.customer.phone }}</dd>
                    {% endif %}
                    {% if sale.customer.email %}
                    <dt>البريد:</dt>
                    <dd>{{ sale.customer.email }}</dd>
                    {% endif %}
                </dl>
            </div>
            <div class="col-md-6">
                <h5>معلومات البيع</h5>
                <dl>
                    <dt>الموظف:</dt>
                    <dd>{{ sale.employee.get_full_name }}</dd>
                    <dt>طريقة الدفع:</dt>
                    <dd>{{ sale.get_payment_method_display }}</dd>
                    <dt>الحالة:</dt>
                    <dd>{{ sale.get_status_display }}</dd>
                </dl>
            </div>
        </div>

        <!-- عناصر البيع -->
        <h4>المنتجات</h4>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.product.name }}</td>
                    <td class="arabic-number">{{ item.quantity }}</td>
                    <td class="arabic-number">{{ item.unit_price|floatformat:2 }} د.م</td>
                    <td class="arabic-number">{{ item.subtotal|floatformat:2 }} د.م</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- الملخص المالي -->
        <div class="row">
            <div class="col-md-6">
                <div class="payment-info">
                    <h5>معلومات الدفع</h5>
                    <dl>
                        <dt>المجموع المدفوع:</dt>
                        <dd class="arabic-number">{{ total_paid|floatformat:2 }} د.م</dd>
                        <dt>المبلغ المتبقي:</dt>
                        <dd class="arabic-number">{{ remaining_amount|floatformat:2 }} د.م</dd>
                        <dt>حالة الدفع:</dt>
                        <dd>
                            {% if remaining_amount <= 0 %}
                                <span class="payment-status paid">مدفوع بالكامل</span>
                            {% elif total_paid > 0 %}
                                <span class="payment-status partial">مدفوع جزئياً</span>
                            {% else %}
                                <span class="payment-status unpaid">غير مدفوع</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="col-md-6">
                <div class="invoice-summary">
                    <div class="d-flex">
                        <span>المبلغ الفرعي:</span>
                        <span class="arabic-number">{{ sale.subtotal|floatformat:2 }} د.م</span>
                    </div>
                    {% if sale.tax_amount %}
                    <div class="d-flex">
                        <span>الضريبة:</span>
                        <span class="arabic-number">{{ sale.tax_amount|floatformat:2 }} د.م</span>
                    </div>
                    {% endif %}
                    {% if sale.discount %}
                    <div class="d-flex">
                        <span>الخصم:</span>
                        <span class="arabic-number">{{ sale.discount|floatformat:2 }} د.م</span>
                    </div>
                    {% endif %}
                    <hr>
                    <div class="d-flex">
                        <strong>المجموع الكلي:</strong>
                        <strong class="total-amount arabic-number">{{ sale.total_amount|floatformat:2 }} د.م</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل المدفوعات -->
        {% if payments %}
        <h4>تفاصيل الدفعات</h4>
        <table>
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>المبلغ</th>
                    <th>طريقة الدفع</th>
                    <th>المرجع</th>
                </tr>
            </thead>
            <tbody>
                {% for payment in payments %}
                <tr>
                    <td class="arabic-number">{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                    <td class="arabic-number">{{ payment.amount|floatformat:2 }} د.م</td>
                    <td>{{ payment.get_payment_method_display }}</td>
                    <td>{{ payment.reference|default:"-" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}

        <!-- الملاحظات -->
        {% if sale.notes %}
        <h5>ملاحظات</h5>
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <div class="mixed-text-content">{{ sale.notes|format_mixed_text }}</div>
        </div>
        {% endif %}

        <!-- منطقة التوقيع -->
        <div class="signature-area">
            <div class="signature-box">
                <p><strong>توقيع المستلم</strong></p>
                <div class="signature-line"></div>
                <small>الاسم والتاريخ</small>
            </div>
            <div class="signature-box">
                <p><strong>ختم الشركة</strong></p>
                <div class="signature-line"></div>
                <small>ختم الشركة</small>
            </div>
            <div class="signature-box">
                <p><strong>توقيع البائع</strong></p>
                <div class="signature-line"></div>
                <small>{{ sale.employee.get_full_name }}</small>
            </div>
        </div>

        <!-- QR Code -->
        <div class="qr-code">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=INV-{{ sale.invoice_number }}" alt="QR Code">
            <p><small>امسح الرمز للتحقق من الفاتورة</small></p>
        </div>

        <!-- ذيل الفاتورة -->
        <div style="text-align: center; margin-top: 30px; font-size: 12pt; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
            <p>شكراً لتعاملكم معنا</p>
            <p><small>تم إنشاء هذه الفاتورة في {{ "now"|date:"Y-m-d H:i" }}</small></p>
        </div>
    </div>
</body>
</html>
