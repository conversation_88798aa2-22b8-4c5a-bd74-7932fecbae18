{% extends 'modern_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "الإدارة المالية" %}{% endblock %}

{% block page_title %}{% trans "الإدارة المالية المتقدمة" %}{% endblock %}
{% block page_subtitle %}{% trans "إدارة شاملة للحسابات والمعاملات المالية مع تقارير تفصيلية" %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active text-white">{% trans "المالية" %}</li>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% trans "الحسابات" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for account in accounts %}
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 {% if account.current_balance < 0 %}border-danger{% elif account.current_balance > 0 %}border-success{% endif %}">
                                <div class="card-body">
                                    <h5 class="card-title">{{ account.name }}</h5>
                                    <h6 class="card-subtitle mb-2 text-muted">{{ account.account_type }}</h6>
                                    <p class="card-text">
                                        <span class="{% if account.current_balance < 0 %}text-danger{% elif account.current_balance > 0 %}text-success{% endif %}">
                                            {{ account.current_balance }} {% trans "د.م" %}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <div class="alert alert-info">
                                {% trans "لا توجد حسابات مضافة بعد" %}
                                <a href="{% url 'finance:accounts' %}" class="alert-link">{% trans "إضافة حساب جديد" %}</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'finance:accounts' %}" class="btn btn-primary">{% trans "إدارة الحسابات" %}</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">{% trans "آخر الإيرادات" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "الفئة" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for income in incomes %}
                                <tr>
                                    <td>{{ income.date }}</td>
                                    <td>{{ income.category.name }}</td>
                                    <td class="text-success">{{ income.amount }} {% trans "د.م" %}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد إيرادات مسجلة بعد" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'finance:income' %}" class="btn btn-success">{% trans "عرض جميع الإيرادات" %}</a>
                    <a href="{% url 'finance:add_income' %}" class="btn btn-outline-success">{% trans "إضافة إيراد جديد" %}</a>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">{% trans "آخر المصروفات" %}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "الفئة" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for expense in expenses %}
                                <tr>
                                    <td>{{ expense.date }}</td>
                                    <td>{{ expense.category.name }}</td>
                                    <td class="text-danger">{{ expense.amount }} {% trans "د.م" %}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد مصروفات مسجلة بعد" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'finance:expenses' %}" class="btn btn-danger">{% trans "عرض جميع المصروفات" %}</a>
                    <a href="{% url 'finance:add_expense' %}" class="btn btn-outline-danger">{% trans "إضافة مصروف جديد" %}</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
