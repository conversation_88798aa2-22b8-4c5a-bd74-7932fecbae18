/* ملف الخطوط العربية المحسنة لـ PDF */

/* استيراد خطوط Google العربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');

/* تعريف الخطوط المخصصة */
@font-face {
    font-family: 'Arabic-PDF';
    src: local('Tajawal'), local('Tahoma'), local('Arial Unicode MS');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Arabic-PDF-Bold';
    src: local('Tajawal Bold'), local('Tahoma Bold'), local('Arial Unicode MS Bold');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* فئات الخطوط للاستخدام */
.arabic-text {
    font-family: 'Tajawal', 'Cairo', 'Amiri', 'Almarai', 'Tahoma', 'Arial Unicode MS', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    word-spacing: 0.1em;
    letter-spacing: 0.02em;
}

.arabic-title {
    font-family: 'Cairo', 'Tajawal', 'Amiri', 'Tahoma', sans-serif;
    font-weight: 700;
    direction: rtl;
    text-align: right;
    line-height: 1.4;
}

.arabic-number {
    font-family: 'Tajawal', 'Cairo', 'Tahoma', monospace;
    font-weight: 600;
    direction: ltr;
    text-align: center;
}

/* تحسينات للعرض */
.smooth-text {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "liga" 1, "kern" 1;
}

/* تحسينات خاصة بـ PDF */
.pdf-optimized {
    font-variant-ligatures: common-ligatures;
    font-kerning: normal;
    text-rendering: geometricPrecision;
}

/* أحجام الخطوط المحسنة */
.text-xs { font-size: 9pt; }
.text-sm { font-size: 10pt; }
.text-base { font-size: 12pt; }
.text-lg { font-size: 14pt; }
.text-xl { font-size: 16pt; }
.text-2xl { font-size: 18pt; }
.text-3xl { font-size: 20pt; }

/* أوزان الخطوط */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

/* تباعد الأسطر */
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

/* فئات للنصوص المختلطة (عربي + لاتيني) */
.mixed-text-content {
    font-family: 'Tajawal', 'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.8;
    word-spacing: 0.15em;
    letter-spacing: 0.03em;
    unicode-bidi: bidi-override;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    -webkit-hyphens: auto;
    -moz-hyphens: auto;
    -ms-hyphens: auto;
}

/* تحسين عرض النصوص اللاتينية داخل النص العربي */
.mixed-text-content {
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum" 1, "kern" 1, "liga" 1;
}

/* تحسين المسافات للنصوص المختلطة */
.notes-container {
    padding: 20px !important;
    margin: 15px 0 !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.notes-container .mixed-text-content {
    font-size: 12pt;
    line-height: 1.8;
    margin: 0;
    padding: 5px 0;
    min-height: 20px;
}

/* تحسينات خاصة للنصوص المختلطة في PDF */
.mixed-text-content p {
    margin-bottom: 10px;
    line-height: 1.8;
    text-align: justify;
    text-justify: inter-word;
}

.mixed-text-content br {
    line-height: 1.8;
}

/* تحسين عرض الأرقام والرموز في النصوص المختلطة */
.mixed-text-content .number,
.mixed-text-content .symbol {
    font-family: 'Tajawal', 'Tahoma', monospace;
    direction: ltr;
    unicode-bidi: embed;
}

/* تحسينات للطباعة */
@media print {
    .arabic-text {
        font-size: 11pt;
        line-height: 1.4;
    }

    .arabic-title {
        font-size: 13pt;
        line-height: 1.3;
    }

    .arabic-number {
        font-size: 10pt;
    }

    .mixed-text-content {
        font-size: 11pt;
        line-height: 1.7;
        word-spacing: 0.1em;
        letter-spacing: 0.02em;
    }

    .notes-container {
        page-break-inside: avoid;
        margin: 10px 0 !important;
        padding: 15px !important;
    }
}
