/* ملف الخطوط العربية المحسنة لـ PDF */

/* استيراد خطوط Google العربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');

/* تعريف الخطوط المخصصة */
@font-face {
    font-family: 'Arabic-PDF';
    src: local('Tajawal'), local('Tahoma'), local('Arial Unicode MS');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Arabic-PDF-Bold';
    src: local('Tajawal Bold'), local('Tahoma Bold'), local('Arial Unicode MS Bold');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* فئات الخطوط للاستخدام */
.arabic-text {
    font-family: 'Tajawal', 'Cairo', 'Amiri', 'Almarai', 'Tahoma', 'Arial Unicode MS', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    word-spacing: 0.1em;
    letter-spacing: 0.02em;
}

.arabic-title {
    font-family: 'Cairo', 'Tajawal', 'Amiri', 'Tahoma', sans-serif;
    font-weight: 700;
    direction: rtl;
    text-align: right;
    line-height: 1.4;
}

.arabic-number {
    font-family: 'Tajawal', 'Cairo', 'Tahoma', monospace;
    font-weight: 600;
    direction: ltr;
    text-align: center;
}

/* تحسينات للعرض */
.smooth-text {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "liga" 1, "kern" 1;
}

/* تحسينات خاصة بـ PDF */
.pdf-optimized {
    font-variant-ligatures: common-ligatures;
    font-kerning: normal;
    text-rendering: geometricPrecision;
}

/* أحجام الخطوط المحسنة */
.text-xs { font-size: 9pt; }
.text-sm { font-size: 10pt; }
.text-base { font-size: 12pt; }
.text-lg { font-size: 14pt; }
.text-xl { font-size: 16pt; }
.text-2xl { font-size: 18pt; }
.text-3xl { font-size: 20pt; }

/* أوزان الخطوط */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

/* تباعد الأسطر */
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

/* تحسينات للطباعة */
@media print {
    .arabic-text {
        font-size: 11pt;
        line-height: 1.4;
    }
    
    .arabic-title {
        font-size: 13pt;
        line-height: 1.3;
    }
    
    .arabic-number {
        font-size: 10pt;
    }
}
