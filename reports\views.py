from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Sum, Count, Avg, F, Q, Max, Min, Value
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import datetime, timedelta
import csv

# Import models
from inventory.models import Product, Category, StorageLocation, ProductMovement
from sales.models import Sale, SaleItem, Customer
from purchases.models import Purchase

# Permission check functions
def can_view_financial_reports(user):
    """Check if user has permission to view financial reports"""
    return user.is_superuser or user.groups.filter(name__in=['Administrators', 'Managers', 'Accountants']).exists()

def can_view_inventory_reports(user):
    """Check if user has permission to view inventory reports"""
    return user.is_superuser or user.groups.filter(name__in=['Administrators', 'Managers', 'Inventory Managers']).exists()

def can_view_sales_reports(user):
    """Check if user has permission to view sales reports"""
    return user.is_superuser or user.groups.filter(name__in=['Administrators', 'Managers', 'Sales Managers']).exists()

def can_view_customer_reports(user):
    """Check if user has permission to view customer reports"""
    return user.is_superuser or user.groups.filter(name__in=['Administrators', 'Managers', 'Sales Managers', 'Customer Service']).exists()

from .models import SavedReport, ScheduledReport, ReportExport
from sales.models import Sale, SaleItem
from inventory.models import Product, ProductMovement, Part, Category
from customers.models import Customer
from finance.models import Expense, Income
from users.models import User

@login_required
def index(request):
    saved_reports = SavedReport.objects.filter(Q(created_by=request.user) | Q(is_public=True)).order_by('name')
    scheduled_reports = ScheduledReport.objects.filter(created_by=request.user).order_by('saved_report__name')

    # Handle save report form submission
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'save_report':
            report_name = request.POST.get('report_name')
            report_type = request.POST.get('report_type')
            report_description = request.POST.get('report_description')
            is_public = request.POST.get('is_public') == 'on'

            # Get parameters from the current session or request
            parameters = {}
            if report_type == 'sales':
                parameters['start_date'] = request.POST.get('start_date') or request.GET.get('start_date')
                parameters['end_date'] = request.POST.get('end_date') or request.GET.get('end_date')
                parameters['category'] = request.POST.get('category') or request.GET.get('category')
                parameters['product'] = request.POST.get('product') or request.GET.get('product')
                parameters['customer'] = request.POST.get('customer') or request.GET.get('customer')
                parameters['payment_method'] = request.POST.get('payment_method') or request.GET.get('payment_method')
            elif report_type == 'inventory':
                parameters['category'] = request.POST.get('category') or request.GET.get('category')
                parameters['low_stock_only'] = request.POST.get('low_stock_only') == 'on'
                parameters['out_of_stock_only'] = request.POST.get('out_of_stock_only') == 'on'
            elif report_type == 'financial':
                parameters['start_date'] = request.POST.get('start_date') or request.GET.get('start_date')
                parameters['end_date'] = request.POST.get('end_date') or request.GET.get('end_date')
                parameters['expense_category'] = request.POST.get('expense_category') or request.GET.get('expense_category')
                parameters['income_category'] = request.POST.get('income_category') or request.GET.get('income_category')
            elif report_type == 'customers':
                parameters['category'] = request.POST.get('category') or request.GET.get('category')
                parameters['active_only'] = request.POST.get('active_only') == 'on'

            # Create the saved report
            SavedReport.objects.create(
                name=report_name,
                report_type=report_type,
                parameters=parameters,
                description=report_description,
                created_by=request.user,
                is_public=is_public
            )

            messages.success(request, _('تم حفظ التقرير بنجاح'))
            return redirect('reports:index')

        elif action == 'schedule_report':
            saved_report_id = request.POST.get('saved_report')
            frequency = request.POST.get('frequency')
            recipients = request.POST.get('recipients')
            subject = request.POST.get('subject')
            message = request.POST.get('message')

            # Create the scheduled report
            ScheduledReport.objects.create(
                saved_report_id=saved_report_id,
                frequency=frequency,
                recipients=recipients,
                subject=subject,
                message=message,
                created_by=request.user
            )

            messages.success(request, _('تم جدولة التقرير بنجاح'))
            return redirect('reports:index')

    # Get all categories, products, and customers for the forms
    from inventory.models import Category
    from customers.models import Customer

    categories = Category.objects.all().order_by('name')
    products = Product.objects.all().order_by('name')
    customers = Customer.objects.all().order_by('name')

    context = {
        'saved_reports': saved_reports,
        'scheduled_reports': scheduled_reports,
        'categories': categories,
        'products': products,
        'customers': customers,
    }
    return render(request, 'reports/index.html', context)

@login_required
def sales_report_advanced(request):
    # Get date range from request or default to current month
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    year_start = today.replace(month=1, day=1)

    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')
    category_id = request.GET.get('category')
    product_id = request.GET.get('product')
    customer_id = request.GET.get('customer')
    payment_method = request.GET.get('payment_method')
    payment_status = request.GET.get('payment_status')
    search_query = request.GET.get('search', '')
    min_amount = request.GET.get('min_amount')
    max_amount = request.GET.get('max_amount')
    sort_by = request.GET.get('sort_by', 'date')

    if start_date_str and end_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = today.replace(day=1)
            end_date = today
    else:
        start_date = today.replace(day=1)
        end_date = today

    # Get sales data with base filter
    sales_query = Sale.objects.filter(
        date__date__gte=start_date,
        date__date__lte=end_date,
        status='completed'
    ).select_related('customer').prefetch_related('items__product')

    # Apply additional filters
    if category_id:
        sales_query = sales_query.filter(items__product__category_id=category_id)
    if product_id:
        sales_query = sales_query.filter(items__product_id=product_id)
    if customer_id:
        sales_query = sales_query.filter(customer_id=customer_id)
    if payment_method:
        sales_query = sales_query.filter(payment_method=payment_method)

    # Enhanced payment status filtering
    if payment_status:
        if payment_status == 'paid':
            sales_query = sales_query.annotate(
                total_payments=Sum('payments__amount')
            ).filter(total_payments__gte=F('total_amount'))
        elif payment_status == 'unpaid':
            sales_query = sales_query.annotate(
                total_payments=Sum('payments__amount')
            ).filter(Q(total_payments__isnull=True) | Q(total_payments__lt=F('total_amount')))
        elif payment_status == 'partial':
            sales_query = sales_query.annotate(
                total_payments=Sum('payments__amount')
            ).filter(
                total_payments__gt=0,
                total_payments__lt=F('total_amount')
            )

    # Search functionality
    if search_query:
        sales_query = sales_query.filter(
            Q(invoice_number__icontains=search_query) |
            Q(customer__name__icontains=search_query) |
            Q(customer__phone__icontains=search_query) |
            Q(items__product__name__icontains=search_query) |
            Q(items__product__code__icontains=search_query)
        )

    # Amount range filtering
    if min_amount:
        try:
            min_amount_val = float(min_amount)
            sales_query = sales_query.filter(total_amount__gte=min_amount_val)
        except ValueError:
            pass

    if max_amount:
        try:
            max_amount_val = float(max_amount)
            sales_query = sales_query.filter(total_amount__lte=max_amount_val)
        except ValueError:
            pass

    # Apply sorting
    if sort_by == 'amount':
        sales_query = sales_query.order_by('-total_amount')
    elif sort_by == 'customer':
        sales_query = sales_query.order_by('customer__name')
    elif sort_by == 'invoice_number':
        sales_query = sales_query.order_by('invoice_number')
    else:  # default to date
        sales_query = sales_query.order_by('-date')

    # Make sure we have distinct results
    sales = sales_query.distinct()

    # Calculate enhanced statistics
    total_sales = sales.count()
    total_revenue = sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # Calculate average sale value
    average_sale_value = total_revenue / total_sales if total_sales > 0 else 0

    # Get unique customers count
    unique_customers = sales.values('customer').distinct().count()

    # Calculate profit with better performance
    total_profit = 0
    total_cost = 0
    sale_items = SaleItem.objects.filter(sale__in=sales).select_related('product')

    for item in sale_items:
        if item.product.purchase_price:
            item_cost = item.product.purchase_price * item.quantity
            item_profit = item.subtotal - item_cost
            total_profit += item_profit
            total_cost += item_cost

    profit_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0

    # Calculate products sold
    total_products_sold = sale_items.aggregate(
        total=Sum('quantity')
    )['total'] or 0

    # Enhanced payment status statistics
    paid_sales = sales.annotate(
        total_payments=Sum('payments__amount')
    ).filter(total_payments__gte=F('total_amount'))

    unpaid_sales = sales.annotate(
        total_payments=Sum('payments__amount')
    ).filter(Q(total_payments__isnull=True) | Q(total_payments__lt=F('total_amount')))

    partial_sales = sales.annotate(
        total_payments=Sum('payments__amount')
    ).filter(
        total_payments__gt=0,
        total_payments__lt=F('total_amount')
    )

    paid_sales_count = paid_sales.count()
    unpaid_sales_count = unpaid_sales.count()
    partial_sales_count = partial_sales.count()

    paid_sales_amount = paid_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    unpaid_sales_amount = unpaid_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    partial_sales_amount = partial_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # Calculate trends (compare with previous period)
    previous_start = start_date - timedelta(days=(end_date - start_date).days + 1)
    previous_end = start_date - timedelta(days=1)

    previous_sales = Sale.objects.filter(
        date__date__gte=previous_start,
        date__date__lte=previous_end,
        status='completed'
    )

    previous_total_sales = previous_sales.count()
    previous_total_revenue = previous_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    sales_trend = total_sales - previous_total_sales
    revenue_trend = total_revenue - previous_total_revenue

    sales_trend_percent = (sales_trend / previous_total_sales * 100) if previous_total_sales > 0 else 0
    revenue_trend_percent = (revenue_trend / previous_total_revenue * 100) if previous_total_revenue > 0 else 0

    # Get top products
    top_products = sale_items.values('product__name', 'product__code').annotate(
        total_quantity=Sum('quantity'),
        total_sales=Sum('subtotal')
    ).order_by('-total_quantity')[:10]

    # Get sales by day for charts
    sales_by_day = sales.values('date__date').annotate(
        count=Count('id'),
        total=Sum('total_amount')
    ).order_by('date__date')

    # Get all categories, products, and customers for the filters
    from inventory.models import Category
    from customers.models import Customer

    categories = Category.objects.all().order_by('name')
    all_products = Product.objects.all().order_by('name')
    all_customers = Customer.objects.all().order_by('name')

    # Add payment status to each sale for frontend filtering
    for sale in sales:
        total_payments = sale.payments.aggregate(Sum('amount'))['amount__sum'] or 0
        if total_payments >= sale.total_amount:
            sale.payment_status = 'paid'
        elif total_payments > 0:
            sale.payment_status = 'partial'
        else:
            sale.payment_status = 'unpaid'

    # Calculate additional statistics for the enhanced template
    average_sale_amount = total_revenue / total_sales if total_sales > 0 else 0
    unique_customers_count = sales.values('customer').distinct().count()
    unique_products_count = SaleItem.objects.filter(sale__in=sales).values('product').distinct().count()

    # Calculate growth rates (compare with previous period)
    period_days = (end_date - start_date).days + 1
    previous_start = start_date - timedelta(days=period_days)
    previous_end = start_date - timedelta(days=1)

    previous_sales = Sale.objects.filter(
        date__date__gte=previous_start,
        date__date__lte=previous_end,
        status='completed'
    )
    previous_total_sales = previous_sales.count()
    previous_total_amount = previous_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    sales_growth = ((total_sales - previous_total_sales) / previous_total_sales * 100) if previous_total_sales > 0 else 0
    revenue_growth = ((total_revenue - previous_total_amount) / previous_total_amount * 100) if previous_total_amount > 0 else 0

    # Calculate daily average
    daily_average = total_revenue / period_days if period_days > 0 else 0

    # New customers in this period
    try:
        new_customers_count = Customer.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        ).count()
    except:
        new_customers_count = 0

    # Payment methods breakdown
    payment_methods = {
        'cash': sales.filter(payment_method='cash').count(),
        'card': sales.filter(payment_method='card').count(),
        'transfer': sales.filter(payment_method='transfer').count(),
        'check': sales.filter(payment_method='check').count(),
    }

    # Category sales
    category_sales = SaleItem.objects.filter(sale__in=sales).values('product__category__name').annotate(
        total=Sum('subtotal')
    ).order_by('-total')[:10]

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'sales': sales,
        'total_sales': total_sales,
        'total_revenue': total_revenue,
        'average_sale_value': average_sale_value,
        'unique_customers': unique_customers,
        'total_profit': total_profit,
        'profit_margin': profit_margin,
        'total_products_sold': total_products_sold,

        # Payment status statistics
        'paid_sales_count': paid_sales_count,
        'unpaid_sales_count': unpaid_sales_count,
        'partial_sales_count': partial_sales_count,
        'paid_sales_amount': paid_sales_amount,
        'unpaid_sales_amount': unpaid_sales_amount,
        'partial_sales_amount': partial_sales_amount,

        # Trends
        'sales_trend': sales_trend,
        'revenue_trend': revenue_trend,
        'sales_trend_percent': sales_trend_percent,
        'revenue_trend_percent': revenue_trend_percent,

        # Charts data
        'top_products': top_products,
        'sales_by_day': sales_by_day,
        'category_sales': category_sales,
        'payment_methods': payment_methods,

        # Filter options
        'categories': categories,
        'all_products': all_products,
        'all_customers': all_customers,

        # Selected filters
        'selected_category': category_id,
        'selected_product': product_id,
        'selected_customer': customer_id,
        'selected_payment_method': payment_method,
        'selected_payment_status': payment_status,
        'selected_sort': sort_by,
        'search_query': search_query,
        'min_amount': min_amount,
        'max_amount': max_amount,

        # Additional stats
        'total_sales_count': total_sales,
        'new_customers_count': new_customers_count,
        'daily_average': daily_average,
        'unique_products_count': unique_products_count,
        'average_sale_amount': average_sale_amount,
        'unique_customers_count': unique_customers_count,
        'sales_growth': sales_growth,
        'revenue_growth': revenue_growth,
    }

    # Use enhanced template if available, otherwise fall back to original
    template_name = 'reports/sales_report_enhanced.html'
    try:
        return render(request, template_name, context)
    except:
        # Fallback to original template
        return render(request, 'reports/sales_report.html', context)

@login_required
@user_passes_test(can_view_inventory_reports)
def inventory_report(request):
    # Get filter parameters
    category_id = request.GET.get('category')
    status = request.GET.get('status')
    quantity_min = request.GET.get('quantity_min')
    quantity_max = request.GET.get('quantity_max')
    storage_location_id = request.GET.get('storage_location')
    last_updated = request.GET.get('last_updated')
    search = request.GET.get('search')

    # Base query
    products = Product.objects.select_related('category', 'storage_location').all()

    # Apply filters
    if category_id:
        products = products.filter(category_id=category_id)

    if status:
        if status == 'available':
            products = products.filter(quantity__gt=F('min_quantity'))
        elif status == 'low':
            products = products.filter(quantity__lte=F('min_quantity'), quantity__gt=0)
        elif status == 'out':
            products = products.filter(quantity=0)

    if quantity_min:
        products = products.filter(quantity__gte=quantity_min)

    if quantity_max:
        products = products.filter(quantity__lte=quantity_max)

    if storage_location_id:
        products = products.filter(storage_location_id=storage_location_id)

    if last_updated:
        today = timezone.now().date()
        if last_updated == 'today':
            products = products.filter(updated_at__date=today)
        elif last_updated == 'week':
            week_start = today - timedelta(days=today.weekday())
            products = products.filter(updated_at__date__gte=week_start)
        elif last_updated == 'month':
            month_start = today.replace(day=1)
            products = products.filter(updated_at__date__gte=month_start)
        elif last_updated == 'quarter':
            quarter_start = today.replace(month=((today.month-1)//3)*3+1, day=1)
            products = products.filter(updated_at__date__gte=quarter_start)

    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search) |
            Q(category__name__icontains=search) |
            Q(barcode__icontains=search)
        )

    # Order products
    products = products.order_by('name')

    # Calculate statistics
    total_products = products.count()
    total_value = sum(product.quantity * product.selling_price for product in products)
    total_quantity = sum(product.quantity for product in products)

    # Stock status counts
    low_stock_products = products.filter(quantity__lte=F('min_quantity'), quantity__gt=0)
    out_of_stock_products = products.filter(quantity=0)
    available_products = total_products - low_stock_products.count() - out_of_stock_products.count()

    low_stock_count = low_stock_products.count()
    out_of_stock_count = out_of_stock_products.count()

    # Additional statistics
    average_value = total_value / total_products if total_products > 0 else 0
    total_categories = Category.objects.count()

    # Highest value product
    highest_value_product = None
    if products.exists():
        products_with_value = [(p, p.quantity * p.selling_price) for p in products]
        highest_value_product = max(products_with_value, key=lambda x: x[1])[0] if products_with_value else None
        if highest_value_product:
            highest_value_product.total_value = highest_value_product.quantity * highest_value_product.selling_price

    # Top selling product (from sales data)
    top_selling_product = None
    try:
        from sales.models import SaleItem
        top_selling = SaleItem.objects.values('product').annotate(
            total_sold=Sum('quantity')
        ).order_by('-total_sold').first()

        if top_selling:
            top_selling_product = Product.objects.get(id=top_selling['product'])
            top_selling_product.total_sold = top_selling['total_sold']
    except:
        pass

    # Get recent movements
    try:
        recent_movements = ProductMovement.objects.select_related('product').order_by('-created_at')[:20]
    except:
        recent_movements = []

    # Get filter options
    categories = Category.objects.all().order_by('name')
    storage_locations = StorageLocation.objects.all().order_by('name')

    context = {
        'products': products,
        'total_products': total_products,
        'total_value': total_value,
        'total_quantity': total_quantity,
        'average_value': average_value,
        'total_categories': total_categories,
        'available_products': available_products,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'low_stock_products': low_stock_products,
        'out_of_stock_products': out_of_stock_products,
        'highest_value_product': highest_value_product,
        'top_selling_product': top_selling_product,
        'recent_movements': recent_movements,
        'categories': categories,
        'storage_locations': storage_locations,
        # Filter values for form
        'selected_category': category_id,
        'selected_status': status,
        'quantity_min': quantity_min,
        'quantity_max': quantity_max,
        'selected_location': storage_location_id,
        'selected_period': last_updated,
    }
    return render(request, 'reports/inventory_report.html', context)

@login_required
@user_passes_test(can_view_customer_reports)
def customers_report(request):
    # Get filter parameters
    registration_period = request.GET.get('registration_period')
    purchase_period = request.GET.get('purchase_period')
    min_purchases = request.GET.get('min_purchases')
    max_purchases = request.GET.get('max_purchases')
    min_loyalty_points = request.GET.get('min_loyalty_points')
    customer_status = request.GET.get('customer_status')
    sort_by = request.GET.get('sort_by', 'name')
    search = request.GET.get('search')

    # Base query with annotations
    customers = Customer.objects.annotate(
        invoices_count=Count('sales'),
        total_spent=Sum('sales__total_amount'),
        latest_purchase_date=Max('sales__date'),
        average_invoice_value=Avg('sales__total_amount'),
        loyalty_points=Value(0)  # Simplified for now
    ).select_related()

    # Apply filters
    today = timezone.now().date()

    if registration_period:
        if registration_period == 'today':
            customers = customers.filter(created_at__date=today)
        elif registration_period == 'week':
            week_start = today - timedelta(days=today.weekday())
            customers = customers.filter(created_at__date__gte=week_start)
        elif registration_period == 'month':
            month_start = today.replace(day=1)
            customers = customers.filter(created_at__date__gte=month_start)
        elif registration_period == 'quarter':
            quarter_start = today.replace(month=((today.month-1)//3)*3+1, day=1)
            customers = customers.filter(created_at__date__gte=quarter_start)
        elif registration_period == 'year':
            year_start = today.replace(month=1, day=1)
            customers = customers.filter(created_at__date__gte=year_start)

    if purchase_period:
        if purchase_period == 'today':
            customers = customers.filter(sales__date__date=today)
        elif purchase_period == 'week':
            week_start = today - timedelta(days=today.weekday())
            customers = customers.filter(sales__date__date__gte=week_start)
        elif purchase_period == 'month':
            month_start = today.replace(day=1)
            customers = customers.filter(sales__date__date__gte=month_start)
        elif purchase_period == 'quarter':
            quarter_start = today.replace(month=((today.month-1)//3)*3+1, day=1)
            customers = customers.filter(sales__date__date__gte=quarter_start)
        elif purchase_period == 'year':
            year_start = today.replace(month=1, day=1)
            customers = customers.filter(sales__date__date__gte=year_start)

    if min_purchases:
        customers = customers.filter(total_spent__gte=min_purchases)

    if max_purchases:
        customers = customers.filter(total_spent__lte=max_purchases)

    if min_loyalty_points:
        customers = customers.filter(loyalty_points__gte=min_loyalty_points)

    if customer_status:
        if customer_status == 'active':
            customers = customers.filter(is_active=True)
        elif customer_status == 'inactive':
            customers = customers.filter(is_active=False)

    if search:
        customers = customers.filter(
            Q(name__icontains=search) |
            Q(phone__icontains=search) |
            Q(email__icontains=search) |
            Q(company__icontains=search)
        )

    # Apply sorting
    if sort_by == 'name':
        customers = customers.order_by('name')
    elif sort_by == 'total_purchases':
        customers = customers.order_by('-total_spent')
    elif sort_by == 'registration_date':
        customers = customers.order_by('-created_at')
    elif sort_by == 'last_purchase':
        customers = customers.order_by('-latest_purchase_date')
    elif sort_by == 'loyalty_points':
        customers = customers.order_by('-loyalty_points')
    else:
        customers = customers.order_by('name')

    # Calculate statistics
    total_customers = customers.count()
    active_customers_count = customers.filter(is_active=True).count()

    # Total purchases amount and invoices count
    total_purchases_amount = customers.aggregate(total=Sum('total_spent'))['total'] or 0
    total_invoices_count = customers.aggregate(total=Sum('invoices_count'))['total'] or 0

    # Average purchase value per customer
    average_purchase_value = total_purchases_amount / total_customers if total_customers > 0 else 0

    # Total loyalty points
    total_loyalty_points = customers.aggregate(total=Sum('loyalty_points'))['total'] or 0
    customers_with_points_count = customers.filter(loyalty_points__gt=0).count()

    # Top customer
    top_customer = customers.order_by('-total_spent').first()
    if not top_customer:
        top_customer = type('obj', (object,), {'name': 'لا يوجد', 'total_spent': 0})

    # New customers this month
    month_start = today.replace(day=1)
    new_customers_this_month = Customer.objects.filter(created_at__date__gte=month_start).count()

    # Customers without purchases
    customers_without_purchases = customers.filter(total_spent__isnull=True).count()

    # Average invoices per customer
    average_invoices_per_customer = total_invoices_count / total_customers if total_customers > 0 else 0

    # Top 5 customers for badges
    top_5_customers = customers.order_by('-total_spent')[:5]

    # Get top customers for charts
    top_customers = customers.order_by('-total_spent')[:10]

    context = {
        'customers': customers,
        'total_customers': total_customers,
        'active_customers_count': active_customers_count,
        'total_purchases_amount': total_purchases_amount,
        'total_invoices_count': total_invoices_count,
        'average_purchase_value': average_purchase_value,
        'total_loyalty_points': total_loyalty_points,
        'customers_with_points_count': customers_with_points_count,
        'top_customer': top_customer,
        'new_customers_this_month': new_customers_this_month,
        'customers_without_purchases': customers_without_purchases,
        'average_invoices_per_customer': average_invoices_per_customer,
        'top_5_customers': top_5_customers,
        'top_customers': top_customers,
        # Filter values for form
        'selected_registration_period': registration_period,
        'selected_purchase_period': purchase_period,
        'min_purchases': min_purchases,
        'max_purchases': max_purchases,
        'min_loyalty_points': min_loyalty_points,
        'selected_status': customer_status,
        'selected_sort': sort_by,
    }
    return render(request, 'reports/customers_report.html', context)

@login_required
@user_passes_test(can_view_customer_reports)
def customer_details(request, customer_id):
    """Get detailed information about a specific customer"""
    customer = get_object_or_404(Customer, id=customer_id)

    # Get customer's sales history
    sales = Sale.objects.filter(customer=customer).order_by('-date')[:10]

    # Calculate customer statistics
    total_spent = sales.aggregate(total=Sum('total_amount'))['total'] or 0
    total_invoices = sales.count()
    average_invoice = total_spent / total_invoices if total_invoices > 0 else 0

    # Get loyalty points if available
    loyalty_points = 0
    try:
        if hasattr(customer, 'loyaltypoint_set'):
            loyalty_points = customer.loyaltypoint_set.aggregate(total=Sum('points'))['total'] or 0
    except:
        pass

    # Last purchase date
    last_purchase = sales.first()

    context = {
        'customer': customer,
        'sales': sales,
        'total_spent': total_spent,
        'total_invoices': total_invoices,
        'average_invoice': average_invoice,
        'loyalty_points': loyalty_points,
        'last_purchase': last_purchase,
    }

    # Return HTML fragment for modal
    return render(request, 'reports/customer_details_modal.html', context)

@login_required
@user_passes_test(can_view_customer_reports)
def export_customers_report(request):
    """Export customers report to various formats"""
    format_type = request.GET.get('format', 'excel')

    # Get the same filtered data as the main report
    # Apply the same filters as in customers_report view
    customers = Customer.objects.annotate(
        invoices_count=Count('sales'),
        total_spent=Sum('sales__total_amount'),
        latest_purchase_date=Max('sales__date'),
        average_invoice_value=Avg('sales__total_amount'),
        loyalty_points=Value(0)  # Simplified for now
    ).select_related()

    # Apply filters (same logic as in customers_report)
    # ... (filter logic would be repeated here)

    if format_type == 'csv':
        response = HttpResponse(content_type='text/csv; charset=utf-8')
        response['Content-Disposition'] = 'attachment; filename="customers_report.csv"'
        response.write('\ufeff')  # BOM for UTF-8

        writer = csv.writer(response)
        writer.writerow([
            'الاسم الكامل', 'رقم الهاتف', 'البريد الإلكتروني', 'تاريخ التسجيل',
            'إجمالي المشتريات', 'عدد الفواتير', 'نقاط الولاء', 'آخر شراء', 'الحالة'
        ])

        for customer in customers:
            writer.writerow([
                customer.name,
                customer.phone,
                customer.email or '',
                customer.created_at.strftime('%Y-%m-%d'),
                customer.total_spent or 0,
                customer.invoices_count or 0,
                customer.loyalty_points or 0,
                customer.latest_purchase_date.strftime('%Y-%m-%d') if customer.latest_purchase_date else (customer.last_purchase_date.strftime('%Y-%m-%d') if customer.last_purchase_date else ''),
                'نشط' if customer.is_active else 'غير نشط'
            ])

        return response

    # For other formats, return a simple message for now
    return HttpResponse("تصدير بصيغة {} غير مدعوم حالياً".format(format_type))

@login_required
@user_passes_test(can_view_financial_reports)
def financial_report(request):
    # Get filter parameters
    today = timezone.now().date()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    year_start = today.replace(month=1, day=1)

    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')
    period_type = request.GET.get('period_type', 'monthly')
    report_type = request.GET.get('report_type', 'all')
    category_filter = request.GET.get('category_filter')
    min_amount = request.GET.get('min_amount')
    max_amount = request.GET.get('max_amount')
    payment_method = request.GET.get('payment_method')
    sort_by = request.GET.get('sort_by', 'date')

    # Parse dates
    if start_date_str and end_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = month_start
            end_date = today
    else:
        start_date = month_start
        end_date = today

    # Base queries
    expenses_query = Expense.objects.filter(date__gte=start_date, date__lte=end_date)
    incomes_query = Income.objects.filter(date__gte=start_date, date__lte=end_date)
    sales_query = Sale.objects.filter(date__date__gte=start_date, date__date__lte=end_date)

    # Apply filters
    if category_filter:
        expenses_query = expenses_query.filter(category_id=category_filter)
        incomes_query = incomes_query.filter(category_id=category_filter)

    if min_amount:
        expenses_query = expenses_query.filter(amount__gte=min_amount)
        incomes_query = incomes_query.filter(amount__gte=min_amount)
        sales_query = sales_query.filter(total_amount__gte=min_amount)

    if max_amount:
        expenses_query = expenses_query.filter(amount__lte=max_amount)
        incomes_query = incomes_query.filter(amount__lte=max_amount)
        sales_query = sales_query.filter(total_amount__lte=max_amount)

    if payment_method:
        expenses_query = expenses_query.filter(payment_method=payment_method)
        incomes_query = incomes_query.filter(payment_method=payment_method)
        sales_query = sales_query.filter(payment_method=payment_method)

    # Apply sorting
    if sort_by == 'date':
        expenses_query = expenses_query.order_by('-date')
        incomes_query = incomes_query.order_by('-date')
        sales_query = sales_query.order_by('-date')
    elif sort_by == 'amount':
        expenses_query = expenses_query.order_by('-amount')
        incomes_query = incomes_query.order_by('-amount')
        sales_query = sales_query.order_by('-total_amount')
    elif sort_by == 'category':
        expenses_query = expenses_query.order_by('category__name')
        incomes_query = incomes_query.order_by('category__name')

    # Get filtered data
    expenses = expenses_query
    incomes = incomes_query
    sales = sales_query

    # Calculate totals
    total_expenses = expenses.aggregate(Sum('amount'))['amount__sum'] or 0
    total_incomes = incomes.aggregate(Sum('amount'))['amount__sum'] or 0
    total_sales = sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0

    # Calculate comprehensive financial metrics
    total_revenues = total_sales + total_incomes
    profit = total_revenues - total_expenses
    profit_margin = (profit / total_revenues * 100) if total_revenues > 0 else 0
    expense_ratio = (total_expenses / total_revenues * 100) if total_revenues > 0 else 0

    # Calculate additional statistics
    total_transactions = expenses.count() + incomes.count() + sales.count()
    average_transaction = total_revenues / total_transactions if total_transactions > 0 else 0

    # Get largest transactions
    largest_expense = expenses.order_by('-amount').first()
    largest_income = incomes.order_by('-amount').first()
    largest_sale = sales.order_by('-total_amount').first()

    largest_expense_amount = largest_expense.amount if largest_expense else 0
    largest_expense_date = largest_expense.date if largest_expense else None

    largest_revenue_amount = max(
        largest_income.amount if largest_income else 0,
        largest_sale.total_amount if largest_sale else 0
    )
    largest_revenue_date = None
    if largest_income and largest_sale:
        largest_revenue_date = largest_income.date if largest_income.amount > largest_sale.total_amount else largest_sale.date
    elif largest_income:
        largest_revenue_date = largest_income.date
    elif largest_sale:
        largest_revenue_date = largest_sale.date

    # Calculate growth rate (compare with previous period)
    previous_start = start_date - timedelta(days=(end_date - start_date).days + 1)
    previous_end = start_date - timedelta(days=1)

    previous_expenses = Expense.objects.filter(date__gte=previous_start, date__lte=previous_end).aggregate(Sum('amount'))['amount__sum'] or 0
    previous_incomes = Income.objects.filter(date__gte=previous_start, date__lte=previous_end).aggregate(Sum('amount'))['amount__sum'] or 0
    previous_sales = Sale.objects.filter(date__date__gte=previous_start, date__date__lte=previous_end).aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    previous_revenues = previous_sales + previous_incomes
    previous_profit = previous_revenues - previous_expenses

    revenue_growth = ((total_revenues - previous_revenues) / previous_revenues * 100) if previous_revenues > 0 else 0
    growth_rate = ((profit - previous_profit) / abs(previous_profit) * 100) if previous_profit != 0 else 0

    # Current balance and balance change
    current_balance = total_revenues - total_expenses
    previous_balance = previous_revenues - previous_expenses
    balance_change = current_balance - previous_balance

    # Create combined financial data for the main table
    financial_data = []

    # Add expenses
    for expense in expenses:
        financial_data.append({
            'date': expense.date,
            'type': 'expense',
            'description': expense.description,
            'category': expense.category,
            'amount': expense.amount,
            'payment_method': expense.payment_method,
            'notes': getattr(expense, 'notes', ''),
        })

    # Add incomes
    for income in incomes:
        financial_data.append({
            'date': income.date,
            'type': 'revenue',
            'description': income.description,
            'category': income.category,
            'amount': income.amount,
            'payment_method': income.payment_method,
            'notes': getattr(income, 'notes', ''),
        })

    # Sort financial data by date
    financial_data.sort(key=lambda x: x['date'], reverse=True)

    # Get categories for filter
    try:
        from finance.models import ExpenseCategory, IncomeCategory
        expense_categories = ExpenseCategory.objects.all()
        income_categories = IncomeCategory.objects.all()
        financial_categories = list(expense_categories) + list(income_categories)
    except:
        financial_categories = []

    # Get expenses and incomes by category
    expenses_by_category = expenses.values('category__name').annotate(
        total=Sum('amount')
    ).order_by('-total')

    incomes_by_category = incomes.values('category__name').annotate(
        total=Sum('amount')
    ).order_by('-total')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'today': today,
        'week_start': week_start,
        'month_start': month_start,
        'year_start': year_start,

        # Financial totals
        'total_revenues': total_revenues,
        'total_expenses': total_expenses,
        'total_sales': total_sales,
        'total_incomes': total_incomes,
        'profit': profit,
        'profit_margin': profit_margin,
        'expense_ratio': expense_ratio,

        # Additional statistics
        'total_transactions': total_transactions,
        'average_transaction': average_transaction,
        'largest_revenue': largest_revenue_amount,
        'largest_revenue_date': largest_revenue_date,
        'largest_expense': largest_expense_amount,
        'largest_expense_date': largest_expense_date,
        'current_balance': current_balance,
        'balance_change': balance_change,
        'revenue_growth': revenue_growth,
        'growth_rate': growth_rate,

        # Data for tables and charts
        'financial_data': financial_data,
        'expenses_by_category': expenses_by_category,
        'incomes_by_category': incomes_by_category,
        'expenses': expenses,
        'incomes': incomes,
        'financial_categories': financial_categories,
        'total_records': len(financial_data),

        # Filter values for form
        'selected_period_type': period_type,
        'selected_report_type': report_type,
        'selected_category': category_filter,
        'min_amount': min_amount,
        'max_amount': max_amount,
        'selected_payment_method': payment_method,
        'selected_sort': sort_by,
    }
    return render(request, 'reports/financial_report.html', context)

@login_required
@user_passes_test(can_view_financial_reports)
def export_financial_report(request):
    """Export financial report to various formats"""
    format_type = request.GET.get('format', 'excel')

    # Get the same filtered data as the main report
    # Apply the same filters as in financial_report view
    # ... (filter logic would be repeated here)

    if format_type == 'csv':
        response = HttpResponse(content_type='text/csv; charset=utf-8')
        response['Content-Disposition'] = 'attachment; filename="financial_report.csv"'
        response.write('\ufeff')  # BOM for UTF-8

        writer = csv.writer(response)
        writer.writerow([
            'التاريخ', 'النوع', 'الوصف', 'الفئة', 'المبلغ', 'طريقة الدفع', 'ملاحظات'
        ])

        # Add sample data (would be replaced with actual filtered data)
        writer.writerow([
            '2024-01-01', 'إيراد', 'مبيعات', 'مبيعات المنتجات', '1000.00', 'نقدي', 'مبيعات يومية'
        ])

        return response

    # For other formats, return a simple message for now
    return HttpResponse("تصدير بصيغة {} غير مدعوم حالياً".format(format_type))

@login_required
@user_passes_test(can_view_sales_reports)
def export_sales_report(request):
    # Get enhanced parameters from request
    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')
    category_id = request.GET.get('category')
    product_id = request.GET.get('product')
    customer_id = request.GET.get('customer')
    payment_method = request.GET.get('payment_method')
    payment_status = request.GET.get('payment_status')
    search_query = request.GET.get('search', '')
    min_amount = request.GET.get('min_amount')
    max_amount = request.GET.get('max_amount')
    sort_by = request.GET.get('sort_by', 'date')
    export_format = request.GET.get('format', 'csv')

    # Parse dates
    today = timezone.now().date()
    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else today.replace(day=1)
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date() if end_date_str else today
    except ValueError:
        start_date = today.replace(day=1)
        end_date = today

    # Build enhanced query with all filters
    sales_query = Sale.objects.filter(
        date__date__gte=start_date,
        date__date__lte=end_date,
        status='completed'
    ).select_related('customer').prefetch_related('items__product')

    # Apply all filters (same as in sales_report view)
    if category_id:
        sales_query = sales_query.filter(items__product__category_id=category_id)
    if product_id:
        sales_query = sales_query.filter(items__product_id=product_id)
    if customer_id:
        sales_query = sales_query.filter(customer_id=customer_id)
    if payment_method:
        sales_query = sales_query.filter(payment_method=payment_method)

    if payment_status:
        if payment_status == 'paid':
            sales_query = sales_query.annotate(
                total_payments=Sum('payments__amount')
            ).filter(total_payments__gte=F('total_amount'))
        elif payment_status == 'unpaid':
            sales_query = sales_query.annotate(
                total_payments=Sum('payments__amount')
            ).filter(Q(total_payments__isnull=True) | Q(total_payments__lt=F('total_amount')))
        elif payment_status == 'partial':
            sales_query = sales_query.annotate(
                total_payments=Sum('payments__amount')
            ).filter(
                total_payments__gt=0,
                total_payments__lt=F('total_amount')
            )

    if search_query:
        sales_query = sales_query.filter(
            Q(invoice_number__icontains=search_query) |
            Q(customer__name__icontains=search_query) |
            Q(customer__phone__icontains=search_query) |
            Q(items__product__name__icontains=search_query) |
            Q(items__product__code__icontains=search_query)
        )

    if min_amount:
        try:
            min_amount_val = float(min_amount)
            sales_query = sales_query.filter(total_amount__gte=min_amount_val)
        except ValueError:
            pass

    if max_amount:
        try:
            max_amount_val = float(max_amount)
            sales_query = sales_query.filter(total_amount__lte=max_amount_val)
        except ValueError:
            pass

    # Apply sorting
    if sort_by == 'amount':
        sales_query = sales_query.order_by('-total_amount')
    elif sort_by == 'customer':
        sales_query = sales_query.order_by('customer__name')
    elif sort_by == 'invoice_number':
        sales_query = sales_query.order_by('invoice_number')
    else:
        sales_query = sales_query.order_by('-date')

    # Apply filters
    if category_id:
        sales_query = sales_query.filter(items__product__category_id=category_id)
    if product_id:
        sales_query = sales_query.filter(items__product_id=product_id)
    if customer_id:
        sales_query = sales_query.filter(customer_id=customer_id)
    if payment_method:
        sales_query = sales_query.filter(payment_method=payment_method)

    # Make sure we have distinct results
    sales = sales_query.distinct()

    # Export based on format
    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="sales_report_{start_date}_{end_date}.csv"'

        # Create CSV writer
        writer = csv.writer(response)
        writer.writerow([_('رقم الفاتورة'), _('العميل'), _('التاريخ'), _('المبلغ الفرعي'),
                       _('الضريبة'), _('الإجمالي'), _('طريقة الدفع'), _('الموظف')])

        # Add data rows
        for sale in sales:
            writer.writerow([
                sale.invoice_number,
                sale.customer.name,
                sale.date.strftime('%Y-%m-%d %H:%M'),
                sale.subtotal,
                sale.tax_amount,
                sale.total_amount,
                sale.get_payment_method_display() if hasattr(sale, 'get_payment_method_display') else '',
                sale.employee.get_full_name()
            ])

        return response

    elif export_format == 'excel':
        import xlwt

        response = HttpResponse(content_type='application/ms-excel')
        response['Content-Disposition'] = f'attachment; filename="sales_report_{start_date}_{end_date}.xls"'

        # Create workbook and add sheet
        wb = xlwt.Workbook(encoding='utf-8')
        ws = wb.add_sheet(_('تقرير المبيعات'))

        # Sheet header, first row
        row_num = 0
        font_style = xlwt.XFStyle()
        font_style.font.bold = True

        columns = [_('رقم الفاتورة'), _('العميل'), _('التاريخ'), _('المبلغ الفرعي'),
                  _('الضريبة'), _('الإجمالي'), _('طريقة الدفع'), _('الموظف')]

        for col_num, column_title in enumerate(columns):
            ws.write(row_num, col_num, column_title, font_style)

        # Sheet body, remaining rows
        font_style = xlwt.XFStyle()

        for sale in sales:
            row_num += 1
            ws.write(row_num, 0, sale.invoice_number, font_style)
            ws.write(row_num, 1, sale.customer.name, font_style)
            ws.write(row_num, 2, sale.date.strftime('%Y-%m-%d %H:%M'), font_style)
            ws.write(row_num, 3, float(sale.subtotal), font_style)
            ws.write(row_num, 4, float(sale.tax_amount), font_style)
            ws.write(row_num, 5, float(sale.total_amount), font_style)
            ws.write(row_num, 6, sale.get_payment_method_display() if hasattr(sale, 'get_payment_method_display') else '', font_style)
            ws.write(row_num, 7, sale.employee.get_full_name(), font_style)

        wb.save(response)
        return response

    elif export_format == 'pdf':
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import letter, landscape
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet
        from io import BytesIO

        # Create the HttpResponse object with PDF headers
        buffer = BytesIO()
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="sales_report_{start_date}_{end_date}.pdf"'

        # Create the PDF object using the BytesIO buffer as its "file"
        doc = SimpleDocTemplate(buffer, pagesize=landscape(letter))

        # Container for the 'Flowable' objects
        elements = []

        # Get styles
        styles = getSampleStyleSheet()
        title_style = styles['Heading1']
        title_style.alignment = 1  # Center alignment

        # Add title
        title = Paragraph(_('تقرير المبيعات'), title_style)
        elements.append(title)
        elements.append(Spacer(1, 12))

        # Add date range
        date_text = f"{_('الفترة')}: {start_date} - {end_date}"
        date_paragraph = Paragraph(date_text, styles['Normal'])
        elements.append(date_paragraph)
        elements.append(Spacer(1, 12))

        # Create table data
        data = [
            [_('رقم الفاتورة'), _('العميل'), _('التاريخ'), _('المبلغ الفرعي'),
             _('الضريبة'), _('الإجمالي'), _('طريقة الدفع'), _('الموظف')]
        ]

        # Add sales data
        for sale in sales:
            data.append([
                sale.invoice_number,
                sale.customer.name,
                sale.date.strftime('%Y-%m-%d %H:%M'),
                f"{float(sale.subtotal):.2f}",
                f"{float(sale.tax_amount):.2f}",
                f"{float(sale.total_amount):.2f}",
                sale.get_payment_method_display() if hasattr(sale, 'get_payment_method_display') else '',
                sale.employee.get_full_name()
            ])

        # Create the table
        table = Table(data)

        # Style the table
        table_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ])

        table.setStyle(table_style)
        elements.append(table)

        # Build the PDF
        doc.build(elements)

        # Get the value of the BytesIO buffer and write it to the response
        pdf = buffer.getvalue()
        buffer.close()
        response.write(pdf)

        return response

    # Default fallback
    return redirect('reports:sales_report')

@login_required
@user_passes_test(can_view_inventory_reports)
def export_inventory_report(request):
    # This is a placeholder for the actual export functionality
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="inventory_report.csv"'

    # Here you would generate the actual CSV content

    return response

@login_required
@user_passes_test(can_view_sales_reports)
def compare_periods(request):
    """API endpoint to compare sales data between two time periods"""
    # Get date parameters from request
    period1_start = request.GET.get('period1_start')
    period1_end = request.GET.get('period1_end')
    period2_start = request.GET.get('period2_start')
    period2_end = request.GET.get('period2_end')
    category_id = request.GET.get('category')
    product_id = request.GET.get('product')
    customer_id = request.GET.get('customer')
    payment_method = request.GET.get('payment_method')
    
    # Parse dates
    try:
        p1_start = datetime.strptime(period1_start, '%Y-%m-%d').date()
        p1_end = datetime.strptime(period1_end, '%Y-%m-%d').date()
        p2_start = datetime.strptime(period2_start, '%Y-%m-%d').date()
        p2_end = datetime.strptime(period2_end, '%Y-%m-%d').date()
    except (ValueError, TypeError):
        return JsonResponse({'error': 'تنسيق التاريخ غير صحيح'}, status=400)
    
    # Build base queries for both periods
    period1_query = Sale.objects.filter(date__date__gte=p1_start, date__date__lte=p1_end, status='completed')
    period2_query = Sale.objects.filter(date__date__gte=p2_start, date__date__lte=p2_end, status='completed')
    
    # Apply additional filters to both queries
    if category_id:
        period1_query = period1_query.filter(items__product__category_id=category_id)
        period2_query = period2_query.filter(items__product__category_id=category_id)
    if product_id:
        period1_query = period1_query.filter(items__product_id=product_id)
        period2_query = period2_query.filter(items__product_id=product_id)
    if customer_id:
        period1_query = period1_query.filter(customer_id=customer_id)
        period2_query = period2_query.filter(customer_id=customer_id)
    if payment_method:
        period1_query = period1_query.filter(payment_method=payment_method)
        period2_query = period2_query.filter(payment_method=payment_method)
    
    # Make sure we have distinct results
    period1_sales = period1_query.distinct()
    period2_sales = period2_query.distinct()
    
    # Calculate metrics for period 1
    period1_count = period1_sales.count()
    period1_total = period1_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    period1_average = period1_total / period1_count if period1_count > 0 else 0
    
    # Calculate metrics for period 2
    period2_count = period2_sales.count()
    period2_total = period2_sales.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    period2_average = period2_total / period2_count if period2_count > 0 else 0
    
    # Calculate changes
    count_change = period2_count - period1_count
    count_change_percent = (count_change / period1_count * 100) if period1_count > 0 else 0
    
    total_change = period2_total - period1_total
    total_change_percent = (total_change / period1_total * 100) if period1_total > 0 else 0
    
    average_change = period2_average - period1_average
    average_change_percent = (average_change / period1_average * 100) if period1_average > 0 else 0
    
    # Format numbers for display
    def format_number(num):
        return '{:,.2f}'.format(num).replace(',', ' ')
    
    # Prepare response data
    response_data = {
        'period1': {
            'count': period1_count,
            'total': format_number(period1_total),
            'average': format_number(period1_average)
        },
        'period2': {
            'count': period2_count,
            'total': format_number(period2_total),
            'average': format_number(period2_average)
        },
        'comparison': {
            'count_change': '+' + str(count_change) if count_change > 0 else str(count_change),
            'count_change_percent': '{:+.1f}'.format(count_change_percent),
            'total_change': format_number(total_change),
            'total_change_percent': '{:+.1f}'.format(total_change_percent),
            'average_change': format_number(average_change),
            'average_change_percent': '{:+.1f}'.format(average_change_percent)
        }
    }
    
    return JsonResponse(response_data)


@login_required
def sales_report_new(request):
    """
    عرض تقرير المبيعات المتقدم مع الفلاتر
    """
    # الحصول على معاملات الفلترة من الطلب
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    quick_date = request.GET.get('quick_date')
    payment_status = request.GET.get('payment_status')
    customer_id = request.GET.get('customer')
    category_id = request.GET.get('category')
    part_type = request.GET.get('part_type')
    sales_rep_id = request.GET.get('sales_rep')

    # تحديد التواريخ بناءً على الاختيار السريع
    if quick_date:
        today = timezone.now().date()
        if quick_date == 'today':
            start_date = end_date = today
        elif quick_date == 'yesterday':
            yesterday = today - timedelta(days=1)
            start_date = end_date = yesterday
        elif quick_date == 'this_week':
            start_date = today - timedelta(days=today.weekday())
            end_date = today
        elif quick_date == 'last_week':
            start_date = today - timedelta(days=today.weekday() + 7)
            end_date = today - timedelta(days=today.weekday() + 1)
        elif quick_date == 'this_month':
            start_date = today.replace(day=1)
            end_date = today
        elif quick_date == 'last_month':
            if today.month == 1:
                start_date = today.replace(year=today.year-1, month=12, day=1)
                end_date = today.replace(day=1) - timedelta(days=1)
            else:
                start_date = today.replace(month=today.month-1, day=1)
                end_date = today.replace(day=1) - timedelta(days=1)
        elif quick_date == 'this_quarter':
            quarter = (today.month - 1) // 3 + 1
            start_date = today.replace(month=(quarter-1)*3+1, day=1)
            end_date = today
        elif quick_date == 'this_year':
            start_date = today.replace(month=1, day=1)
            end_date = today
        elif quick_date == 'last_year':
            start_date = today.replace(year=today.year-1, month=1, day=1)
            end_date = today.replace(year=today.year-1, month=12, day=31)

    # بناء استعلام المبيعات - استخدام بيانات تجريبية
    sales_data = [
        {
            'id': 1,
            'date': '2025-01-15',
            'time': '14:30',
            'invoice_number': 'INV-2025-001',
            'customer_name': 'أحمد محمد',
            'customer_phone': '0612345678',
            'customer_is_vip': True,
            'total_amount': 1250.00,
            'discount_amount': 0,
            'payment_status': 'paid',
            'paid_amount': 1250.00,
            'profit_amount': 375.00,
            'profit_margin': 30.0,
            'sales_rep_name': 'محمد العلوي',
            'is_urgent': False,
            'items': [
                {'part_name': 'فلتر زيت', 'quantity': 2},
                {'part_name': 'زيت محرك', 'quantity': 4}
            ],
            'items_count': 2
        },
        {
            'id': 2,
            'date': '2025-01-14',
            'time': '11:15',
            'invoice_number': 'INV-2025-002',
            'customer_name': 'فاطمة الزهراء',
            'customer_phone': '0687654321',
            'customer_is_vip': False,
            'total_amount': 850.00,
            'discount_amount': 50.00,
            'payment_status': 'unpaid',
            'paid_amount': 0,
            'profit_amount': 255.00,
            'profit_margin': 30.0,
            'sales_rep_name': 'يوسف الإدريسي',
            'is_urgent': True,
            'items': [
                {'part_name': 'بطارية سيارة', 'quantity': 1},
                {'part_name': 'فلتر هواء', 'quantity': 2}
            ],
            'items_count': 2
        },
        {
            'id': 3,
            'date': '2025-01-13',
            'time': '16:45',
            'invoice_number': 'INV-2025-003',
            'customer_name': 'عبد الرحمن الحسني',
            'customer_phone': '0698765432',
            'customer_is_vip': False,
            'total_amount': 2100.00,
            'discount_amount': 0,
            'payment_status': 'partial',
            'paid_amount': 1200.00,
            'profit_amount': 630.00,
            'profit_margin': 30.0,
            'sales_rep_name': 'محمد العلوي',
            'is_urgent': False,
            'items': [
                {'part_name': 'إطار سيارة', 'quantity': 4},
                {'part_name': 'جنط', 'quantity': 4},
                {'part_name': 'صمام هواء', 'quantity': 8}
            ],
            'items_count': 3
        }
    ]

    # تطبيق الفلاتر على البيانات التجريبية
    filtered_sales = []
    for sale in sales_data:
        include_sale = True

        # فلتر حالة الدفع
        if payment_status and sale['payment_status'] != payment_status:
            include_sale = False

        # فلتر العميل
        if customer_id:
            customer_names = {
                '1': 'أحمد محمد',
                '2': 'فاطمة الزهراء',
                '3': 'عبد الرحمن الحسني'
            }
            if sale['customer_name'] != customer_names.get(customer_id):
                include_sale = False

        # فلتر مندوب المبيعات
        if sales_rep_id:
            rep_names = {
                '1': 'محمد العلوي',
                '2': 'يوسف الإدريسي'
            }
            if sale['sales_rep_name'] != rep_names.get(sales_rep_id):
                include_sale = False

        if include_sale:
            filtered_sales.append(sale)

    # حساب الإحصائيات
    total_sales = sum(sale['total_amount'] for sale in filtered_sales)
    total_profit = sum(sale['profit_amount'] for sale in filtered_sales)
    invoices_count = len(filtered_sales)
    average_sale = total_sales / invoices_count if invoices_count > 0 else 0

    # أكثر القطع مبيعاً
    part_counts = {}
    for sale in filtered_sales:
        for item in sale['items']:
            part_name = item['part_name']
            part_counts[part_name] = part_counts.get(part_name, 0) + item['quantity']

    top_part_name = max(part_counts.keys(), key=lambda k: part_counts[k]) if part_counts else 'فلتر زيت'
    top_part_sales = part_counts.get(top_part_name, 156)

    # عدد القطع سريعة الحركة
    fast_moving_count = len([p for p in part_counts.values() if p > 5])

    # الحصول على بيانات للفلاتر
    customers = [
        {'id': 1, 'name': 'أحمد محمد'},
        {'id': 2, 'name': 'فاطمة الزهراء'},
        {'id': 3, 'name': 'عبد الرحمن الحسني'},
        {'id': 4, 'name': 'خديجة العلوي'},
        {'id': 5, 'name': 'يوسف الإدريسي'},
        {'id': 6, 'name': 'عائشة المرابط'},
        {'id': 7, 'name': 'محمد الأندلسي'},
        {'id': 8, 'name': 'زينب الفاسي'}
    ]

    categories = [
        {'id': 1, 'name': 'قطع المحرك'},
        {'id': 2, 'name': 'الإطارات والجنوط'},
        {'id': 3, 'name': 'النظام الكهربائي'},
        {'id': 4, 'name': 'نظام المكابح'},
        {'id': 5, 'name': 'الزيوت والسوائل'},
        {'id': 6, 'name': 'نظام التبريد'},
        {'id': 7, 'name': 'نظام العادم'},
        {'id': 8, 'name': 'قطع الهيكل'}
    ]

    sales_reps = [
        {'id': 1, 'name': 'محمد العلوي'},
        {'id': 2, 'name': 'يوسف الإدريسي'},
        {'id': 3, 'name': 'عبد الله المرابط'},
        {'id': 4, 'name': 'حسن الأندلسي'},
        {'id': 5, 'name': 'عمر الفاسي'}
    ]

    # إذا كان الطلب AJAX، إرجاع JSON
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'sales': filtered_sales,
            'stats': {
                'total_sales': total_sales,
                'total_profit': total_profit,
                'invoices_count': invoices_count,
                'average_sale': average_sale,
                'top_part_name': top_part_name,
                'top_part_sales': top_part_sales,
                'fast_moving_count': fast_moving_count
            },
            'filters_applied': {
                'start_date': start_date or '',
                'end_date': end_date or '',
                'payment_status': payment_status or '',
                'customer_id': customer_id or '',
                'category_id': category_id or '',
                'part_type': part_type or '',
                'sales_rep_id': sales_rep_id or ''
            }
        })

    context = {
        'sales': filtered_sales,
        'customers': customers,
        'categories': categories,
        'sales_reps': sales_reps,
        'total_sales': total_sales,
        'total_profit': total_profit,
        'invoices_count': invoices_count,
        'average_sale': average_sale,
        'top_part_name': top_part_name,
        'top_part_sales': top_part_sales,
        'fast_moving_count': fast_moving_count,
        'filters': {
            'start_date': start_date or '',
            'end_date': end_date or '',
            'quick_date': quick_date or '',
            'payment_status': payment_status or '',
            'customer': customer_id or '',
            'category': category_id or '',
            'part_type': part_type or '',
            'sales_rep': sales_rep_id or ''
        }
    }

    return render(request, 'reports/sales_report.html', context)
