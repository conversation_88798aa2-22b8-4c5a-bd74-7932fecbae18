{% load i18n %}
{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="{% static 'css/modern-header.css' %}">
    <link rel="stylesheet" href="{% static 'css/dropdown-fix.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% csrf_token %}
    
    <!-- Modern Header -->
    <header class="modern-page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="modern-header-content">
                        <div class="modern-page-title">
                            <i class="fas fa-car-alt me-3"></i>
                            {% block page_title %}{{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}{% endblock %}
                        </div>
                        <div class="modern-page-subtitle">
                            {% block page_subtitle %}{% trans "نظام إدارة شامل ومتطور لمتجر قطع غيار السيارات" %}{% endblock %}
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="modern-header-actions">
                        {% block header_actions %}
                        {% if user.is_authenticated %}
                            <div class="dropdown">
                                <button class="modern-btn-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-2"></i>{{ user.username }}
                                </button>
                                <ul class="dropdown-menu modern-dropdown-menu" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item modern-dropdown-item" href="{% url 'settings_app:profile' %}">
                                        <i class="fas fa-user-edit"></i>{% trans "الملف الشخصي" %}
                                    </a></li>
                                    <li><a class="dropdown-item modern-dropdown-item" href="{% url 'settings_app:index' %}">
                                        <i class="fas fa-cog"></i>{% trans "الإعدادات" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item modern-dropdown-item" href="{% url 'logout' %}">
                                        <i class="fas fa-sign-out-alt"></i>{% trans "تسجيل الخروج" %}
                                    </a></li>
                                </ul>
                            </div>
                        {% else %}
                            <a href="{% url 'login' %}" class="modern-btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>{% trans "تسجيل الدخول" %}
                            </a>
                        {% endif %}
                        {% endblock %}
                        
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>
            </div>
            
            <!-- Breadcrumb and Back Button -->
            <div class="row mt-3">
                <div class="col-md-8">
                    <nav class="modern-breadcrumb-nav">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{% url 'dashboard:index' %}">{% trans "الرئيسية" %}</a>
                            </li>
                            {% block breadcrumb %}{% endblock %}
                        </ol>
                    </nav>
                </div>
                <div class="col-md-4 text-end">
                    <button onclick="goBack()" class="modern-btn-back" title="{% trans 'العودة' %}">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation Menu -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard:index' %}">
                            <i class="fas fa-tachometer-alt me-1"></i> {% trans "لوحة التحكم" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'inventory' %}active{% endif %}" href="#" id="inventoryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-boxes me-1"></i> {% trans "المخزون" %}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="inventoryDropdown">
                            <li><a class="dropdown-item" href="{% url 'inventory:index' %}">{% trans "إدارة المنتجات" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:categories' %}">{% trans "الفئات" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:stock_alerts' %}">{% trans "تنبيهات المخزون" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'customers' %}active{% endif %}" href="{% url 'customers:index' %}">
                            <i class="fas fa-users me-1"></i> {% trans "العملاء" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'sales' %}active{% endif %}" href="{% url 'sales:index' %}">
                            <i class="fas fa-shopping-cart me-1"></i> {% trans "المبيعات" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'purchases' %}active{% endif %}" href="#" id="purchasesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-truck me-1"></i> {% trans "المشتريات" %}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="purchasesDropdown">
                            <li><a class="dropdown-item" href="{% url 'purchases:index' %}">{% trans "لوحة التحكم" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:purchase_orders' %}">{% trans "طلبات الشراء" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'purchases:suppliers' %}">{% trans "الموردين" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'finance' %}active{% endif %}" href="{% url 'finance:index' %}">
                            <i class="fas fa-chart-line me-1"></i> {% trans "المالية" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'employees' %}active{% endif %}" href="{% url 'employees:index' %}">
                            <i class="fas fa-user-tie me-1"></i> {% trans "الموظفين" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'reports' %}active{% endif %}" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-chart-bar me-1"></i> {% trans "التقارير" %}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                            <li><a class="dropdown-item" href="{% url 'reports:index' %}">{% trans "جميع التقارير" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:sales_report' %}">{% trans "تقرير المبيعات" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:inventory_report' %}">{% trans "تقرير المخزون" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'settings_app' %}active{% endif %}" href="{% url 'settings_app:index' %}">
                            <i class="fas fa-cog me-1"></i> {% trans "الإعدادات" %}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container-fluid mt-4">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">جميع الحقوق محفوظة &copy; {{ current_year }} {{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}</p>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // Global JavaScript Functions
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = "{% url 'dashboard:index' %}";
            }
        }

        // Show notification function
        function showNotification(message, type = 'info', duration = 3000) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, duration);
        }

        // Simple and direct dropdown initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing dropdowns...');

            // Wait for Bootstrap to load
            setTimeout(function() {
                initializeDropdowns();
            }, 100);
        });

        function initializeDropdowns() {
            console.log('Initializing dropdowns...');

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Simple dropdown handling
            document.querySelectorAll('.dropdown-toggle').forEach(function(toggle) {
                console.log('Setting up dropdown:', toggle);

                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('Dropdown clicked');

                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                        if (menu !== toggle.nextElementSibling) {
                            menu.classList.remove('show');
                        }
                    });

                    // Toggle current dropdown
                    const menu = toggle.nextElementSibling;
                    if (menu && menu.classList.contains('dropdown-menu')) {
                        menu.classList.toggle('show');
                        console.log('Toggled dropdown menu');
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                        menu.classList.remove('show');
                    });
                }
            });

            // Add smooth scrolling to all anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add loading states to buttons
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    }
                });
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(() => {
                document.querySelectorAll('.alert:not(.alert-permanent)').forEach(alert => {
                    if (alert.classList.contains('show')) {
                        bootstrap.Alert.getOrCreateInstance(alert).close();
                    }
                });
            }, 5000);
        }
    </script>

    <!-- Custom JS (loaded after our initialization) -->
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/custom.js' %}"></script>
</body>
</html>
