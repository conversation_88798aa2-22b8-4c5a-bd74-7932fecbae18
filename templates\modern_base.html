<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="{% static 'css/modern-header.css' %}">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- Meta Tags for SEO -->
    <meta name="description" content="نظام إدارة متجر قطع غيار السيارات - إدارة شاملة للمخزون والمبيعات والتقارير">
    <meta name="keywords" content="قطع غيار, سيارات, مخزون, مبيعات, إدارة">
    <meta name="author" content="{{ company_info.name|default:'نظام إدارة متجر قطع غيار السيارات' }}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% csrf_token %}
    
    <!-- Modern Header -->
    <div class="modern-page-header fade-in">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="modern-page-title">
                        <i class="fas fa-car-alt me-3"></i>
                        {% block page_title %}{{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}{% endblock %}
                    </h1>
                    <p class="modern-page-subtitle">
                        {% block page_subtitle %}{% trans "نظام متكامل لإدارة المخزون والمبيعات والتقارير" %}{% endblock %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="modern-header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end flex-wrap">
                            {% block header_actions %}
                            {% if user.is_authenticated %}
                            <div class="dropdown">
                                <button class="btn modern-btn-secondary dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle me-1"></i>
                                    {{ user.get_full_name|default:user.username }}
                                </button>
                                <ul class="dropdown-menu modern-dropdown-menu">
                                    <li><a class="dropdown-item modern-dropdown-item" href="{% url 'settings_app:profile' %}">
                                        <i class="fas fa-user-cog"></i>{% trans "الملف الشخصي" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item modern-dropdown-item" href="{% url 'logout' %}">
                                        <i class="fas fa-sign-out-alt"></i>{% trans "تسجيل الخروج" %}
                                    </a></li>
                                </ul>
                            </div>
                            {% else %}
                            <a href="{% url 'login' %}" class="btn modern-btn-secondary">
                                <i class="fas fa-sign-in-alt me-1"></i>{% trans "تسجيل الدخول" %}
                            </a>
                            {% endif %}
                            {% endblock %}
                        </div>
                        <div class="modern-breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    {% block breadcrumb %}{% endblock %}
                                </ol>
                            </nav>
                            <button type="button" class="modern-btn-back" onclick="goBack()" title="{% trans 'العودة للصفحة السابقة' %}">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
        <div class="container-fluid">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard:index' %}">
                            <i class="fas fa-tachometer-alt me-1"></i> {% trans "لوحة التحكم" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'inventory' %}active{% endif %}" href="#" id="inventoryDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes me-1"></i> {% trans "المخزون" %}
                        </a>
                        <ul class="dropdown-menu modern-dropdown-menu">
                            <li><a class="dropdown-item modern-dropdown-item" href="{% url 'inventory:index' %}"><i class="fas fa-list"></i>{% trans "قائمة المنتجات" %}</a></li>
                            <li><a class="dropdown-item modern-dropdown-item" href="{% url 'inventory:add_product' %}"><i class="fas fa-plus"></i>{% trans "إضافة منتج" %}</a></li>
                            <li><a class="dropdown-item modern-dropdown-item" href="{% url 'inventory:categories' %}"><i class="fas fa-tags"></i>{% trans "الفئات" %}</a></li>
                            <li><a class="dropdown-item modern-dropdown-item" href="{% url 'inventory:stock_alerts' %}"><i class="fas fa-exclamation-triangle"></i>{% trans "تنبيهات المخزون" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'customers' %}active{% endif %}" href="{% url 'customers:index' %}">
                            <i class="fas fa-users me-1"></i> {% trans "العملاء" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'sales' %}active{% endif %}" href="{% url 'sales:index' %}">
                            <i class="fas fa-shopping-cart me-1"></i> {% trans "المبيعات" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.app_name == 'purchases' %}active{% endif %}" href="#" id="purchasesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-truck me-1"></i> {% trans "المشتريات" %}
                        </a>
                        <ul class="dropdown-menu modern-dropdown-menu">
                            <li><a class="dropdown-item modern-dropdown-item" href="{% url 'purchases:index' %}"><i class="fas fa-tachometer-alt"></i>{% trans "لوحة التحكم" %}</a></li>
                            <li><a class="dropdown-item modern-dropdown-item" href="{% url 'purchases:suppliers' %}"><i class="fas fa-users"></i>{% trans "الموردين" %}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'finance' %}active{% endif %}" href="{% url 'finance:index' %}">
                            <i class="fas fa-money-bill-wave me-1"></i> {% trans "المالية" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'employees' %}active{% endif %}" href="{% url 'employees:index' %}">
                            <i class="fas fa-user-tie me-1"></i> {% trans "الموظفين" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'reports' %}active{% endif %}" href="{% url 'reports:index' %}">
                            <i class="fas fa-chart-bar me-1"></i> {% trans "التقارير" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.app_name == 'settings_app' %}active{% endif %}" href="{% url 'settings_app:index' %}">
                            <i class="fas fa-cog me-1"></i> {% trans "الإعدادات" %}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container-fluid mt-4">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5" id="main-footer">
        <div class="container">
            <p class="mb-0">جميع الحقوق محفوظة &copy; {{ current_year }} {{ company_info.name|default:"نظام إدارة متجر قطع غيار السيارات" }}</p>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

    <!-- Custom JS -->
    {% load static %}
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/navbar-fix.js' %}"></script>
    <script src="{% static 'js/custom.js' %}"></script>

    <script>
        // Global JavaScript Functions
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = "{% url 'dashboard:index' %}";
            }
        }

        // Show notification function
        function showNotification(message, type = 'info', duration = 3000) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, duration);
        }

        // Initialize tooltips and other components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Add smooth scrolling to all anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add loading states to buttons
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    }
                });
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(() => {
                document.querySelectorAll('.alert:not(.alert-permanent)').forEach(alert => {
                    if (alert.classList.contains('show')) {
                        bootstrap.Alert.getOrCreateInstance(alert).close();
                    }
                });
            }, 5000);
        });

        // Enhanced search functionality
        function enhancedSearch(inputId, targetSelector) {
            const input = document.getElementById(inputId);
            if (!input) return;

            input.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const targets = document.querySelectorAll(targetSelector);

                targets.forEach(target => {
                    const text = target.textContent.toLowerCase();
                    const shouldShow = text.includes(searchTerm);
                    target.style.display = shouldShow ? '' : 'none';

                    // Add highlight effect
                    if (shouldShow && searchTerm) {
                        target.classList.add('search-highlight');
                    } else {
                        target.classList.remove('search-highlight');
                    }
                });
            });
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
