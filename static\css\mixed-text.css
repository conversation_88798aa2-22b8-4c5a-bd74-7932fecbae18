/* ملف CSS خاص بالنصوص المختلطة (عربي + فرنسي/إنجليزي) */

/* تحسينات أساسية للنصوص المختلطة */
.mixed-text-content {
    font-family: '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', 'Arial Unicode MS', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
    line-height: 1.9 !important;
    word-spacing: 0.2em !important;
    letter-spacing: 0.05em !important;
    unicode-bidi: bidi-override !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
    -webkit-hyphens: auto !important;
    -moz-hyphens: auto !important;
    -ms-hyphens: auto !important;
    font-size: 12pt !important;
    margin: 0 !important;
    padding: 8px 0 !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* تحسين النصوص اللاتينية المضمنة */
.mixed-text-content .latin-text {
    font-family: 'Tajawal', 'Tahoma', 'Arial', sans-serif !important;
    direction: ltr !important;
    unicode-bidi: embed !important;
    display: inline !important;
    font-weight: 500 !important;
    color: #2c3e50 !important;
    background-color: rgba(52, 152, 219, 0.1) !important;
    padding: 1px 3px !important;
    border-radius: 2px !important;
    margin: 0 2px !important;
}

/* تحسين عرض الأرقام */
.mixed-text-content .number {
    font-family: 'Tajawal', 'Tahoma', 'Courier New', monospace !important;
    direction: ltr !important;
    unicode-bidi: embed !important;
    font-weight: 600 !important;
    color: #e74c3c !important;
    background-color: rgba(231, 76, 60, 0.1) !important;
    padding: 1px 2px !important;
    border-radius: 2px !important;
}

/* تحسين عرض الرموز وعلامات الترقيم */
.mixed-text-content .symbol {
    font-family: 'Tajawal', 'Tahoma', sans-serif !important;
    font-weight: 600 !important;
    color: #8e44ad !important;
}

/* تحسين الفقرات */
.mixed-text-content p {
    margin-bottom: 15px !important;
    line-height: 1.9 !important;
    text-align: justify !important;
    text-justify: inter-word !important;
    text-indent: 0 !important;
}

.mixed-text-content p:last-child {
    margin-bottom: 0 !important;
}

/* تحسين الأسطر الجديدة */
.mixed-text-content br {
    line-height: 1.9 !important;
    margin: 5px 0 !important;
}

/* تحسين المسافات */
.mixed-text-content {
    word-spacing: normal !important;
    letter-spacing: normal !important;
}

/* تحسين النصوص الفرنسية المحددة */
.mixed-text-content [lang="fr"],
.mixed-text-content .french-text {
    font-family: 'Tajawal', 'Tahoma', 'Georgia', serif !important;
    direction: ltr !important;
    unicode-bidi: embed !important;
    font-style: italic !important;
    color: #27ae60 !important;
    background-color: rgba(39, 174, 96, 0.1) !important;
    padding: 1px 3px !important;
    border-radius: 2px !important;
}

/* تحسين النصوص الإنجليزية المحددة */
.mixed-text-content [lang="en"],
.mixed-text-content .english-text {
    font-family: 'Tajawal', 'Tahoma', 'Arial', sans-serif !important;
    direction: ltr !important;
    unicode-bidi: embed !important;
    color: #3498db !important;
    background-color: rgba(52, 152, 219, 0.1) !important;
    padding: 1px 3px !important;
    border-radius: 2px !important;
}

/* تحسين حاوي الملاحظات */
.notes-container {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    border: 2px solid #f39c12 !important;
    padding: 25px !important;
    margin: 20px 0 !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
    page-break-inside: avoid !important;
    position: relative !important;
}

.notes-container::before {
    content: "📝" !important;
    position: absolute !important;
    top: 10px !important;
    left: 15px !important;
    font-size: 16pt !important;
    opacity: 0.7 !important;
}

/* تحسينات للطباعة */
@media print {
    .mixed-text-content {
        font-size: 11pt !important;
        line-height: 1.8 !important;
        word-spacing: 0.15em !important;
        letter-spacing: 0.03em !important;
        color: #000 !important;
    }
    
    .mixed-text-content .latin-text {
        background-color: transparent !important;
        color: #000 !important;
        font-weight: 500 !important;
        padding: 0 !important;
        margin: 0 1px !important;
    }
    
    .mixed-text-content .number {
        background-color: transparent !important;
        color: #000 !important;
        font-weight: 600 !important;
        padding: 0 !important;
    }
    
    .mixed-text-content .symbol {
        color: #000 !important;
        font-weight: 600 !important;
    }
    
    .mixed-text-content [lang="fr"],
    .mixed-text-content .french-text {
        background-color: transparent !important;
        color: #000 !important;
        font-style: italic !important;
        padding: 0 !important;
    }
    
    .mixed-text-content [lang="en"],
    .mixed-text-content .english-text {
        background-color: transparent !important;
        color: #000 !important;
        padding: 0 !important;
    }
    
    .notes-container {
        background: #fff3cd !important;
        border: 1px solid #f39c12 !important;
        padding: 20px !important;
        margin: 15px 0 !important;
        border-radius: 5px !important;
        box-shadow: none !important;
        page-break-inside: avoid !important;
    }
    
    .notes-container::before {
        display: none !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media screen and (max-width: 768px) {
    .mixed-text-content {
        font-size: 11pt !important;
        line-height: 1.7 !important;
        padding: 5px 0 !important;
    }
    
    .notes-container {
        padding: 15px !important;
        margin: 10px 0 !important;
    }
}

/* تحسينات إضافية للوضوح */
.mixed-text-content strong,
.mixed-text-content b {
    font-weight: 700 !important;
    color: #2c3e50 !important;
}

.mixed-text-content em,
.mixed-text-content i {
    font-style: italic !important;
    color: #7f8c8d !important;
}

/* تحسين التباعد بين الكلمات */
.mixed-text-content {
    text-spacing: 0.1em !important;
}

/* تحسين عرض النصوص الطويلة */
.mixed-text-content {
    max-width: 100% !important;
    overflow-wrap: anywhere !important;
    word-break: break-word !important;
}

/* تحسين الخطوط للنصوص المختلطة */
.mixed-text-content {
    font-variant-ligatures: common-ligatures !important;
    font-kerning: normal !important;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1 !important;
}
