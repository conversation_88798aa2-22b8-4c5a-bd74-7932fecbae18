{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "المدفوعات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .payment-card {
        transition: all 0.3s;
    }
    
    .payment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .payment-method-badge {
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "المدفوعات" %}</h1>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Filter Section -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "تصفية النتائج" %}</h6>
    </div>
    <div class="card-body">
        <form method="get" action="{% url 'purchases:payments' %}">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">{% trans "جميع الموردين" %}</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier_filter == supplier.id|stringformat:"s" %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="date_from" class="form-label">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="date_to" class="form-label">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
            </div>
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter me-1"></i> {% trans "تصفية" %}
                </button>
                <a href="{% url 'purchases:payments' %}" class="btn btn-secondary ms-2">
                    <i class="fas fa-redo me-1"></i> {% trans "إعادة تعيين" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Payments List -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المدفوعات" %}</h6>
        <div>
            <a href="{% url 'purchases:payments_report' %}" class="btn btn-sm btn-info">
                <i class="fas fa-file-export me-1"></i> {% trans "تقرير المدفوعات" %}
            </a>
        </div>
    </div>
    <div class="card-body">
        {% if payments %}
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="paymentsTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{% trans "رقم الطلب" %}</th>
                        <th>{% trans "المورد" %}</th>
                        <th>{% trans "تاريخ الدفع" %}</th>
                        <th>{% trans "المبلغ" %}</th>
                        <th>{% trans "طريقة الدفع" %}</th>
                        <th>{% trans "المرجع" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td>
                            <a href="{% url 'purchases:view_purchase' purchase_id=payment.purchase.id %}">
                                {{ payment.purchase.reference_number }}
                            </a>
                        </td>
                        <td>{{ payment.purchase.supplier.name }}</td>
                        <td>{{ payment.payment_date|date:"Y-m-d" }}</td>
                        <td>{{ payment.amount|floatformat:2 }} ر.س</td>
                        <td>
                            {% if payment.payment_method == 'cash' %}
                            <span class="badge bg-success payment-method-badge">{% trans "نقدي" %}</span>
                            {% elif payment.payment_method == 'bank_transfer' %}
                            <span class="badge bg-primary payment-method-badge">{% trans "تحويل بنكي" %}</span>
                            {% elif payment.payment_method == 'check' %}
                            <span class="badge bg-info payment-method-badge">{% trans "شيك" %}</span>
                            {% elif payment.payment_method == 'credit_card' %}
                            <span class="badge bg-warning text-dark payment-method-badge">{% trans "بطاقة ائتمان" %}</span>
                            {% else %}
                            <span class="badge bg-secondary payment-method-badge">{{ payment.get_payment_method_display }}</span>
                            {% endif %}
                        </td>
                        <td>{{ payment.reference|default:"-" }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'purchases:view_purchase' purchase_id=payment.purchase.id %}#payments" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye me-1"></i> {% trans "عرض" %}
                                </a>
                                {% if perms.purchases.delete_supplierpayment %}
                                <a href="#" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deletePaymentModal{{ payment.id }}">
                                    <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                                </a>
                                {% endif %}
                            </div>
                            
                            <!-- Delete Payment Modal -->
                            <div class="modal fade" id="deletePaymentModal{{ payment.id }}" tabindex="-1" aria-labelledby="deletePaymentModalLabel{{ payment.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="deletePaymentModalLabel{{ payment.id }}">{% trans "تأكيد الحذف" %}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>{% trans "هل أنت متأكد من رغبتك في حذف هذه الدفعة؟" %}</p>
                                            <p><strong>{% trans "المبلغ" %}:</strong> {{ payment.amount|floatformat:2 }} ر.س</p>
                                            <p><strong>{% trans "تاريخ الدفع" %}:</strong> {{ payment.payment_date|date:"Y-m-d" }}</p>
                                            <p class="text-danger">{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                                            <a href="{% url 'purchases:delete_payment' payment_id=payment.id %}" class="btn btn-danger">{% trans "حذف" %}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            {% trans "لا توجد مدفوعات متاحة حالياً." %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // استدعاء دالة تهيئة القوائم المنسدلة من navbar-fix.js
        if (typeof initializeNavbarDropdowns === 'function') {
            initializeNavbarDropdowns();
        }
    });

    $(document).ready(function() {
        // تهيئة جدول البيانات
        $('#paymentsTable').DataTable({
            "language": {
                "url": "{% static 'vendor/datatables/ar.json' %}"
            },
            "order": [[2, "desc"]], // ترتيب حسب تاريخ الدفع (تنازلي)
            "pageLength": 25
        });
    });
</script>
{% endblock %}