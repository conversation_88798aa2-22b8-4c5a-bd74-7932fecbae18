{% extends 'modern_base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
{% endblock %}

{% block title %}{% trans "الإعدادات" %} | {{ block.super }}{% endblock %}

{% block page_title %}{% trans "مركز الإعدادات المتقدم" %}{% endblock %}
{% block page_subtitle %}{% trans "إدارة شاملة لجميع إعدادات النظام والتخصيصات" %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active text-white">{% trans "الإعدادات" %}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">

    <div class="row">
        <!-- General Settings -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "الإعدادات العامة" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "معلومات الشركة والشعار" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cog fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:general_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>

        <!-- User Management -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "إدارة المستخدمين" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "المستخدمين، الصلاحيات، المجموعات" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users-cog fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:users' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>

        <!-- Inventory Settings -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "إعدادات المخزون" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "مواقع التخزين وإعدادات الجرد" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:general_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>

        <!-- Backup Settings -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "إعدادات النسخ الاحتياطي" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "جدولة وتخزين النسخ الاحتياطي" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:backup_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Currency Settings -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "إعدادات العملة" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "العملة ورمزها وتنسيقها" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:currency_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>

        <!-- Tax Settings -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "إعدادات الضريبة" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "نسبة وطريقة تطبيق الضريبة" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:tax_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>
        <!-- Email Settings -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "إعدادات البريد" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "إعدادات خادم البريد الإلكتروني" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:email_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>

        <!-- Notifications -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "الإشعارات" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "إعدادات الإشعارات والتنبيهات" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:notification_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>

        <!-- Appearance -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "اللغة والمظهر" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "إعدادات اللغة واتجاه النص" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-paint-brush fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:language_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 dashboard-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{% trans "إعدادات الفاتورة" %}</div>
                            <div class="text-xs text-muted mt-2">{% trans "ترقيم وتنسيق الفواتير" %}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-info-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <a href="{% url 'settings_app:invoice_settings' %}" class="stretched-link"></a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
