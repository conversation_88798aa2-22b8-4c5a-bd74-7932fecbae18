{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* تطبيق خط Cairo على الصفحة */
    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    /* تصحيح حجم الخط في شريط التنقل */
    .nav-link {
        font-size: 1.1rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
    
    .navbar-brand {
        font-size: 1.2rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
</style>
{% endblock %}

{% block title %}{% trans "إعدادات الضريبة" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إعدادات الضريبة" %}</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTaxModal">
            <i class="fas fa-plus-circle me-1"></i> {% trans "إضافة ضريبة جديدة" %}
        </button>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "الضرائب المتاحة" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="taxesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "اسم الضريبة" %}</th>
                            <th>{% trans "النسبة" %}</th>
                            <th>{% trans "نوع الضريبة" %}</th>
                            <th>{% trans "الرقم الضريبي" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tax in tax_settings %}
                        <tr>
                            <td>{{ tax.name }}</td>
                            <td>{{ tax.rate }}%</td>
                            <td>{{ tax.get_tax_type_display }}</td>
                            <td>{{ tax.tax_number|default:"-" }}</td>
                            <td>
                                {% if tax.is_enabled %}
                                <span class="badge bg-success">{% trans "مفعل" %}</span>
                                {% else %}
                                <span class="badge bg-secondary">{% trans "معطل" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-info edit-tax" 
                                    data-id="{{ tax.id }}"
                                    data-name="{{ tax.name }}"
                                    data-rate="{{ tax.rate }}"
                                    data-tax-type="{{ tax.tax_type }}"
                                    data-tax-number="{{ tax.tax_number|default:'' }}"
                                    data-is-enabled="{{ tax.is_enabled|yesno:'true,false' }}"
                                    data-bs-toggle="modal" data-bs-target="#editTaxModal">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-tax" 
                                    data-id="{{ tax.id }}" 
                                    data-name="{{ tax.name }}"
                                    data-bs-toggle="modal" data-bs-target="#deleteTaxModal">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "لا توجد ضرائب مضافة" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Tax Modal -->
<div class="modal fade" id="addTaxModal" tabindex="-1" aria-labelledby="addTaxModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'settings_app:tax_settings' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="add">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTaxModalLabel">{% trans "إضافة ضريبة جديدة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">{% trans "اسم الضريبة" %}</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="rate" class="form-label">{% trans "نسبة الضريبة (%)" %}</label>
                        <input type="number" class="form-control" id="rate" name="rate" min="0" max="100" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="tax_type" class="form-label">{% trans "نوع الضريبة" %}</label>
                        <select class="form-select" id="tax_type" name="tax_type" required>
                            <option value="inclusive">{% trans "شامل (مضمنة في السعر)" %}</option>
                            <option value="exclusive" selected>{% trans "إضافي (تضاف للسعر)" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="tax_number" class="form-label">{% trans "الرقم الضريبي" %}</label>
                        <input type="text" class="form-control" id="tax_number" name="tax_number">
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_enabled" name="is_enabled" checked>
                        <label class="form-check-label" for="is_enabled">
                            {% trans "تفعيل الضريبة" %}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "إضافة" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Tax Modal -->
<div class="modal fade" id="editTaxModal" tabindex="-1" aria-labelledby="editTaxModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'settings_app:tax_settings' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="tax_id" id="edit_tax_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTaxModalLabel">{% trans "تعديل الضريبة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">{% trans "اسم الضريبة" %}</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_rate" class="form-label">{% trans "نسبة الضريبة (%)" %}</label>
                        <input type="number" class="form-control" id="edit_rate" name="rate" min="0" max="100" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_tax_type" class="form-label">{% trans "نوع الضريبة" %}</label>
                        <select class="form-select" id="edit_tax_type" name="tax_type" required>
                            <option value="inclusive">{% trans "شامل (مضمنة في السعر)" %}</option>
                            <option value="exclusive">{% trans "إضافي (تضاف للسعر)" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_tax_number" class="form-label">{% trans "الرقم الضريبي" %}</label>
                        <input type="text" class="form-control" id="edit_tax_number" name="tax_number">
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_is_enabled" name="is_enabled">
                        <label class="form-check-label" for="edit_is_enabled">
                            {% trans "تفعيل الضريبة" %}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ التغييرات" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Tax Modal -->
<div class="modal fade" id="deleteTaxModal" tabindex="-1" aria-labelledby="deleteTaxModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" id="deleteTaxForm">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteTaxModalLabel">{% trans "حذف الضريبة" %}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>{% trans "هل أنت متأكد من رغبتك في حذف الضريبة" %} <span id="tax_name_to_delete"></span>؟</p>
                    <p class="text-danger">{% trans "هذا الإجراء لا يمكن التراجع عنه." %}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-danger">{% trans "حذف" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit Tax
        document.querySelectorAll('.edit-tax').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const rate = this.getAttribute('data-rate');
                const taxType = this.getAttribute('data-tax-type');
                const taxNumber = this.getAttribute('data-tax-number');
                const isEnabled = this.getAttribute('data-is-enabled') === 'true';
                
                document.getElementById('edit_tax_id').value = id;
                document.getElementById('edit_name').value = name;
                document.getElementById('edit_rate').value = rate;
                document.getElementById('edit_tax_type').value = taxType;
                document.getElementById('edit_tax_number').value = taxNumber;
                document.getElementById('edit_is_enabled').checked = isEnabled;
            });
        });
        
        // Delete Tax
        document.querySelectorAll('.delete-tax').forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                
                document.getElementById('tax_name_to_delete').textContent = name;
                document.getElementById('deleteTaxForm').action = "{% url 'settings_app:delete_tax' 0 %}".replace('0', id);
            });
        });
    });
</script>
{% endblock %}
