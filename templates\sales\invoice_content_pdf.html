<!-- محتوى الفاتورة المحسن لـ PDF -->
{% load sales_extras %}

<!-- رأس الفاتورة -->
<div class="invoice-header no-page-break">
    <div class="row">
        <div class="col-md-6">
            <div style="display: table; width: 100%;">
                {% if invoice_settings.show_logo and company_info.logo %}
                <div style="display: table-cell; vertical-align: middle; width: 100px;">
                    <img src="{{ company_info.logo.url }}" alt="{{ company_info.name }}" class="invoice-logo">
                </div>
                {% endif %}
                <div style="display: table-cell; vertical-align: middle; padding-right: 15px;">
                    <h1 class="invoice-title arabic-title">{{ company_info.name|default:"اسم الشركة" }}</h1>
                    <span class="badge">فاتورة مبيعات</span>
                </div>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <h2 class="arabic-title">فاتورة #{{ sale.invoice_number }}</h2>
            <p class="mb-0"><strong>تاريخ الفاتورة:</strong> {{ sale.date|date:"Y-m-d" }}</p>
            <p class="mb-0"><strong>وقت الإصدار:</strong> {{ sale.date|date:"H:i" }}</p>
        </div>
    </div>
</div>

<!-- تفاصيل الفاتورة -->
<div class="invoice-details no-page-break">
    <div class="row">
        <div class="col-md-6">
            {% if invoice_settings.show_customer_info %}
            <h5 class="arabic-title">معلومات العميل</h5>
            <div style="background-color: #f8f9fa; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                <dl>
                    <dt>اسم العميل:</dt>
                    <dd>{{ sale.customer.name }}</dd>

                    {% if sale.customer.phone %}
                    <dt>رقم الهاتف:</dt>
                    <dd class="arabic-number">{{ sale.customer.phone }}</dd>
                    {% endif %}

                    {% if sale.customer.email %}
                    <dt>البريد الإلكتروني:</dt>
                    <dd>{{ sale.customer.email }}</dd>
                    {% endif %}

                    {% if sale.customer.address %}
                    <dt>العنوان:</dt>
                    <dd>{{ sale.customer.address }}</dd>
                    {% endif %}
                </dl>
            </div>
            {% endif %}
        </div>
        <div class="col-md-6">
            {% if invoice_settings.show_company_info %}
            <h5 class="arabic-title">معلومات الشركة</h5>
            <div style="background-color: #f8f9fa; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                <dl>
                    <dt>اسم الشركة:</dt>
                    <dd>{{ company_info.name|default:"اسم الشركة" }}</dd>

                    {% if company_info.phone %}
                    <dt>رقم الهاتف:</dt>
                    <dd class="arabic-number">{{ company_info.phone }}</dd>
                    {% endif %}

                    {% if company_info.email %}
                    <dt>البريد الإلكتروني:</dt>
                    <dd>{{ company_info.email }}</dd>
                    {% endif %}

                    {% if company_info.address %}
                    <dt>العنوان:</dt>
                    <dd>{{ company_info.address }}</dd>
                    {% endif %}

                    {% if company_info.tax_number %}
                    <dt>الرقم الضريبي:</dt>
                    <dd class="arabic-number">{{ company_info.tax_number }}</dd>
                    {% endif %}
                </dl>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- عناصر الفاتورة -->
<div class="invoice-items no-page-break">
    <h4 class="arabic-title mb-3">المنتجات</h4>
    <table class="table table-invoice">
        <thead>
            <tr>
                <th width="5%" class="text-center">#</th>
                <th width="15%" class="text-center">كود المنتج</th>
                <th width="35%" class="text-center">اسم المنتج</th>
                <th width="10%" class="text-center">الكمية</th>
                <th width="15%" class="text-center">سعر الوحدة</th>
                <th width="20%" class="text-center">المجموع</th>
            </tr>
        </thead>
        <tbody>
            {% for item in items %}
            <tr>
                <td class="text-center arabic-number">{{ forloop.counter }}</td>
                <td class="text-center arabic-number">{{ item.product.code|default:"-" }}</td>
                <td class="text-start"><strong>{{ item.product.name }}</strong></td>
                <td class="text-center arabic-number">{{ item.quantity }}</td>
                <td class="text-center arabic-number">{{ item.unit_price|floatformat:2 }} د.م</td>
                <td class="text-center arabic-number"><strong>{{ item.subtotal|floatformat:2 }} د.م</strong></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- ملخص الفاتورة ومعلومات الدفع -->
<div class="row">
    <div class="col-md-6">
        <div class="payment-info no-page-break">
            <h5 class="arabic-title">معلومات الدفع</h5>
            <dl>
                {% if invoice_settings.show_payment_info %}
                <dt>طريقة الدفع:</dt>
                <dd>
                    {% if sale.payment_method == 'cash' %}
                    نقدي
                    {% elif sale.payment_method == 'card' %}
                    بطاقة ائتمان
                    {% elif sale.payment_method == 'transfer' %}
                    تحويل بنكي
                    {% elif sale.payment_method == 'check' %}
                    شيك
                    {% elif sale.payment_method == 'credit' %}
                    آجل
                    {% else %}
                    غير محدد
                    {% endif %}
                </dd>
                {% endif %}

                <dt>حالة الدفع:</dt>
                <dd>
                    {% if sale.status == 'completed' %}
                    <span class="payment-status paid">مدفوع بالكامل</span>
                    {% elif sale.status == 'pending' %}
                    <span class="payment-status partial">معلق</span>
                    {% elif sale.status == 'cancelled' %}
                    <span class="payment-status unpaid">ملغي</span>
                    {% else %}
                    <span class="payment-status partial">غير محدد</span>
                    {% endif %}
                </dd>

                <!-- عرض ملخص المدفوعات دائماً -->
                <dt style="margin-top: 15px;">المجموع المدفوع:</dt>
                <dd>
                    <strong class="arabic-number">{{ total_paid|floatformat:2 }} د.م</strong>
                </dd>

                <dt>المبلغ المتبقي:</dt>
                <dd>
                    <strong class="arabic-number" style="color: #dc3545 !important; font-weight: bold !important; font-size: 14pt !important;">{{ remaining_amount|floatformat:2 }} د.م</strong>
                    <!-- Debug: remaining_amount = {{ remaining_amount }} -->
                </dd>

                <!-- عرض تفاصيل المدفوعات إذا وجدت -->
                {% if payments %}
                <dt style="margin-top: 15px;">تفاصيل الدفعات:</dt>
                <dd>
                    <table class="table table-sm" style="margin-top: 10px; font-size: 10pt;">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>المرجع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td class="arabic-number">{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                                <td class="arabic-number">{{ payment.amount|floatformat:2 }} د.م</td>
                                <td>{{ payment.get_payment_method_display }}</td>
                                <td>{{ payment.reference|default:"-" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </dd>
                {% else %}
                <dt style="margin-top: 15px;">ملاحظة:</dt>
                <dd>
                    <em>لم يتم تسجيل أي دفعات بعد</em>
                </dd>
                {% endif %}
            </dl>
        </div>

        <!-- رمز QR -->
        <div class="qr-code">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=INV-{{ sale.invoice_number }}" alt="QR Code">
            <p class="small mt-2">امسح الرمز للتحقق من الفاتورة</p>
        </div>
    </div>
    <div class="col-md-6">
        <div class="invoice-summary no-page-break">
            <div class="d-flex">
                <span>المجموع الفرعي:</span>
                <span class="arabic-number">{{ sale.subtotal|floatformat:2 }} د.م</span>
            </div>
            {% if invoice_settings.show_tax and sale.tax_amount %}
            <div class="d-flex">
                <span>ضريبة القيمة المضافة ({{ tax.rate|default:"0" }}%):</span>
                <span class="arabic-number">{{ sale.tax_amount|floatformat:2 }} د.م</span>
            </div>
            {% endif %}
            {% if sale.discount and sale.discount > 0 %}
            <div class="d-flex">
                <span>الخصم:</span>
                <span class="arabic-number">{{ sale.discount|floatformat:2 }} د.م</span>
            </div>
            {% endif %}
            <hr>
            <div class="d-flex">
                <strong>المجموع الكلي:</strong>
                <strong class="total-amount arabic-number">{{ sale.total_amount|floatformat:2 }} د.م</strong>
            </div>
        </div>
    </div>
</div>

<!-- الملاحظات -->
{% if sale.notes %}
<div class="mt-4 no-page-break">
    <h5 class="arabic-title">ملاحظات</h5>
    <div class="notes-container" style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px;">
        <div class="mixed-text-content">{{ sale.notes|format_mixed_text }}</div>
    </div>
</div>
{% endif %}

<!-- منطقة التوقيع -->
<div class="signature-area">
    <div class="signature-box">
        <p class="arabic-title">توقيع المستلم</p>
        <div class="signature-line"></div>
        <small class="text-muted">الاسم والتاريخ</small>
    </div>
    <div class="signature-box">
        <p class="arabic-title">ختم الشركة</p>
        <div class="signature-line"></div>
        <small class="text-muted">ختم الشركة</small>
    </div>
    <div class="signature-box">
        <p class="arabic-title">توقيع البائع</p>
        <div class="signature-line"></div>
        <small class="text-muted">{{ sale.employee.get_full_name|default:sale.employee.username }}</small>
    </div>
</div>

<!-- الشروط والأحكام -->
<div class="terms-conditions">
    <h6 class="arabic-title">الشروط والأحكام</h6>
    <ol>
        <li>جميع المبيعات نهائية ولا يمكن استردادها بعد مغادرة المتجر.</li>
        <li>يمكن استبدال المنتجات في غضون 14 يومًا من تاريخ الشراء مع تقديم الفاتورة الأصلية.</li>
        <li>لا يتم قبول المنتجات المستخدمة أو التالفة للاستبدال.</li>
        <li>تطبق ضمانات الشركة المصنعة على المنتجات المؤهلة.</li>
        <li>يحتفظ المتجر بالحق في تغيير هذه الشروط والأحكام في أي وقت.</li>
    </ol>
</div>

<!-- ذيل الفاتورة -->
<div class="invoice-footer">
    <div class="row">
        <div class="col-md-4">
            {% if company_info.phone %}
            <span class="arabic-number">{{ company_info.phone }}</span>
            {% endif %}
        </div>
        <div class="col-md-4">
            {% if company_info.email %}
            {{ company_info.email }}
            {% endif %}
        </div>
        <div class="col-md-4">
            {% if company_info.website %}
            {{ company_info.website }}
            {% endif %}
        </div>
    </div>
    <hr>
    <p>شكراً لتعاملكم معنا</p>
    {% if company_info.footer_text %}
    <p class="small">{{ company_info.footer_text }}</p>
    {% endif %}
    <p class="small">تم إنشاء هذه الفاتورة بواسطة {{ company_info.name|default:"النظام" }} &copy; {% now "Y" %}</p>
    <p class="small arabic-number">تاريخ الطباعة: {% now "Y-m-d H:i" %}</p>
</div>
