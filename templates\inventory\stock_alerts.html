{% extends 'base.html' %}
{% load i18n %}
{% load purchase_tags %}

{% block title %}{% trans "تنبيهات المخزون" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    /* Status Indicators */
    .status-low {
        color: #ffc107;
        font-weight: bold;
    }
    .status-out {
        color: #dc3545;
        font-weight: bold;
    }
    
    /* Product Cards */
    .product-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 20px;
        position: relative;
    }
    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 24px rgba(0,0,0,0.15);
    }
    .product-card.low {
        border-right: 4px solid #ffc107;
    }
    .product-card.out {
        border-right: 4px solid #dc3545;
    }
    .product-card .card-header {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    .product-card .card-body {
        padding: 15px;
    }
    .product-card .card-footer {
        background-color: rgba(0,0,0,0.02);
        padding: 12px 15px;
        border-top: 1px solid rgba(0,0,0,0.05);
    }
    .product-image {
        width: 70px;
        height: 70px;
        object-fit: contain;
        border-radius: 8px;
        margin-left: 15px;
        background-color: #f8f9fa;
        padding: 5px;
    }
    .product-info h5 {
        margin-bottom: 5px;
        font-weight: 600;
    }
    .product-info .product-code {
        color: #6c757d;
        font-size: 0.85rem;
    }
    .product-meta {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
    }
    .meta-item {
        background-color: #f8f9fa;
        border-radius: 20px;
        padding: 5px 12px;
        margin-left: 10px;
        margin-bottom: 8px;
        font-size: 0.85rem;
        display: inline-flex;
        align-items: center;
    }
    .meta-item i {
        margin-left: 5px;
        font-size: 0.9rem;
    }
    .stock-indicator {
        display: flex;
        align-items: center;
        margin-top: 10px;
    }
    .stock-bar {
        height: 8px;
        flex-grow: 1;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin-left: 10px;
    }
    .stock-bar-fill.low {
        height: 100%;
        background-color: #ffc107;
        width: 30%;
    }
    .stock-bar-fill.out {
        height: 100%;
        background-color: #dc3545;
        width: 0%;
    }
    .stock-text {
        font-weight: 600;
        font-size: 0.9rem;
        white-space: nowrap;
    }
    .action-buttons {
        display: flex;
        justify-content: space-between;
    }
    .action-buttons .btn {
        margin-left: 5px;
        border-radius: 6px;
        padding: 0.4rem 0.75rem;
        font-size: 0.85rem;
    }
    .action-buttons .btn i {
        margin-left: 5px;
    }
    .product-select-container {
        position: absolute;
        top: 15px;
        left: 15px;
        z-index: 10;
    }
    .product-select {
        width: 20px;
        height: 20px;
    }
    
    /* Stats Cards */
    .stats-section {
        margin-bottom: 30px;
    }
    .stats-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s;
        height: 100%;
    }
    .stats-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }
    .stats-icon {
        font-size: 2.8rem;
        opacity: 0.8;
    }
    .stats-card .card-body {
        padding: 20px;
    }
    .stats-detail {
        background-color: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 8px 12px;
        margin-top: 10px;
        font-size: 0.9rem;
    }
    .stats-detail i {
        margin-left: 5px;
    }
    
    /* Filter Section */
    .filter-section {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    }
    .filter-section .form-label {
        font-weight: 500;
        margin-bottom: 8px;
    }
    .filter-section .form-select,
    .filter-section .form-control {
        border-radius: 8px;
        padding: 10px 15px;
        border: 1px solid #dee2e6;
    }
    .filter-section .btn {
        border-radius: 8px;
        padding: 10px 15px;
    }
    
    /* Additional Styles */
    .section-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #dee2e6;
        color: #495057;
    }
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        background-color: #f8f9fa;
        border-radius: 12px;
        margin-bottom: 20px;
    }
    .empty-state i {
        font-size: 3rem;
        color: #adb5bd;
        margin-bottom: 15px;
    }
    .empty-state p {
        color: #6c757d;
        font-size: 1.1rem;
    }
    .purchase-order-form {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    }
    
    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .product-image {
            width: 60px;
            height: 60px;
        }
        .meta-item {
            margin-bottom: 5px;
        }
        .action-buttons {
            flex-wrap: wrap;
        }
        .action-buttons .btn {
            margin-bottom: 5px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-exclamation-triangle me-3"></i>
                        {% trans "تنبيهات المخزون" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "مراقبة وإدارة المنتجات منخفضة المخزون والمنتجات النافدة مع تنبيهات ذكية" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="generatePurchaseOrders()">
                                <i class="fas fa-shopping-cart me-1"></i>
                                {% trans "طلبات شراء" %}
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download me-1"></i>
                                    {% trans "تصدير" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item export-btn" href="#" data-type="pdf">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>{% trans "تصدير PDF" %}
                                    </a></li>
                                    <li><a class="dropdown-item export-btn" href="#" data-type="excel">
                                        <i class="fas fa-file-excel text-success me-2"></i>{% trans "تصدير Excel" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="window.print()">
                                        <i class="fas fa-print text-primary me-2"></i>{% trans "طباعة" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportLowStock()">
                                        <i class="fas fa-exclamation-circle text-warning me-2"></i>{% trans "المنتجات منخفضة المخزون" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportOutOfStock()">
                                        <i class="fas fa-times-circle text-danger me-2"></i>{% trans "المنتجات النافدة" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'inventory:index' %}">
                                            <i class="fas fa-warehouse me-1"></i>{% trans "المخزون" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "التنبيهات" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPurchaseOrderModal">
            <i class="fas fa-file-invoice me-1"></i> {% trans "إنشاء طلب شراء" %}
        </button>
        <a href="{% url 'inventory:index' %}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى المخزون" %}
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="stats-section">
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="stats-card bg-warning text-white">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">{% trans "منتجات منخفضة المخزون" %}</h6>
                        <h2 class="mb-0">{{ low_stock_count }}</h2>
                        <p class="mb-0 small">{% trans "منتجات تحتاج إلى إعادة تعبئة" %}</p>
                        <div class="stats-detail">
                            <i class="fas fa-info-circle"></i> {% trans "تحت الحد الأدنى للمخزون" %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="stats-card bg-danger text-white">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">{% trans "منتجات نفدت من المخزون" %}</h6>
                        <h2 class="mb-0">{{ out_of_stock_count }}</h2>
                        <p class="mb-0 small">{% trans "منتجات غير متوفرة حالياً" %}</p>
                        <div class="stats-detail">
                            <i class="fas fa-info-circle"></i> {% trans "بحاجة إلى طلب شراء فوري" %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="stats-card bg-primary text-white">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-uppercase mb-1">{% trans "إجمالي المنتجات" %}</h6>
                        <h2 class="mb-0">{{ low_stock_count|add:out_of_stock_count }}</h2>
                        <p class="mb-0 small">{% trans "تحتاج إلى اهتمام" %}</p>
                        <div class="stats-detail">
                            <i class="fas fa-info-circle"></i> {% trans "قيمة المخزون المطلوب" %}: {{ total_purchase_value|default:"0" }} د.م
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="filter-section mb-4">
    <div class="row">
        <div class="col-md-3 mb-2">
            <label for="categoryFilter" class="form-label">{% trans "الفئة" %}</label>
            <select class="form-select" id="categoryFilter">
                <option value="">{% trans "جميع الفئات" %}</option>
                {% for category in categories %}
                <option value="{{ category.id }}">{{ category.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3 mb-2">
            <label for="supplierFilter" class="form-label">{% trans "المورد" %}</label>
            <select class="form-select" id="supplierFilter">
                <option value="">{% trans "جميع الموردين" %}</option>
                {% for supplier in suppliers %}
                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3 mb-2">
            <label for="stockStatusFilter" class="form-label">{% trans "حالة المخزون" %}</label>
            <select class="form-select" id="stockStatusFilter">
                <option value="">{% trans "جميع الحالات" %}</option>
                <option value="low" {% if request.GET.status == 'low' or not request.GET.status %}selected{% endif %}>{% trans "منخفض" %}</option>
                <option value="out" {% if request.GET.status == 'out' %}selected{% endif %}>{% trans "نفد" %}</option>
            </select>
        </div>
        <div class="col-md-3 mb-2">
            <label for="quickSearch" class="form-label">{% trans "بحث سريع" %}</label>
            <div class="input-group">
                <input type="text" class="form-control" id="quickSearch" placeholder="{% trans 'اسم المنتج أو الكود...' %}">
                <button class="btn btn-primary" type="button" id="searchBtn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Products Section -->
<div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 class="section-title mb-0">{% trans "المنتجات التي تحتاج إلى إعادة تعبئة" %}</h5>
        <div class="form-check">
            <input class="form-check-input" type="checkbox" id="selectAllProducts">
            <label class="form-check-label" for="selectAllProducts">
                {% trans "تحديد الكل" %}
            </label>
        </div>
    </div>
    
    <div class="row" id="productsContainer">
        {% for product in products %}
        <div class="col-md-6 col-lg-4 product-item" 
             data-category="{{ product.category.id }}" 
             data-supplier="{{ product.supplier.id|default:'' }}" 
             data-status="{% if product.quantity == 0 %}out{% else %}low{% endif %}">
            <div class="product-card {% if product.quantity == 0 %}out{% else %}low{% endif %}">
                <div class="product-select-container">
                    <input type="checkbox" class="form-check-input product-select" 
                           value="{{ product.id }}" 
                           data-name="{{ product.name }}" 
                           data-code="{{ product.code }}" 
                           data-quantity="{{ product.min_quantity|add:5|sub:product.quantity }}">
                </div>
                
                <div class="card-header">
                    {% if product.image %}
                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
                    {% else %}
                    <div class="product-image d-flex align-items-center justify-content-center">
                        <i class="fas fa-box fa-2x text-muted"></i>
                    </div>
                    {% endif %}
                    <div class="product-info">
                        <h5>{{ product.name }}</h5>
                        <div class="product-code">{{ product.code }}</div>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="product-meta">
                        <div class="meta-item">
                            <i class="fas fa-tag"></i> {{ product.category.name }}
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-truck"></i> {{ product.supplier.name|default:"غير محدد" }}
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-money-bill-wave"></i> {{ product.purchase_price }} د.م
                        </div>
                    </div>
                    
                    <div class="stock-indicator">
                        <div class="stock-bar">
                            <div class="stock-bar-fill {% if product.quantity == 0 %}out{% else %}low{% endif %}"></div>
                        </div>
                        <div class="stock-text">
                            {% if product.quantity == 0 %}
                            <span class="status-out">{% trans "نفد" %}</span>
                            {% else %}
                            <span class="status-low">{% trans "منخفض" %}</span> ({{ product.quantity }})
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-6">
                            <small class="text-muted">{% trans "الحد الأدنى" %}</small>
                            <div><strong>{{ product.min_quantity }}</strong></div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">{% trans "الكمية المطلوبة" %}</small>
                            <div><strong>{{ product.min_quantity|add:5|sub:product.quantity }}</strong></div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <div class="action-buttons">
                        <a href="{% url 'inventory:product_detail' product.id %}" class="btn btn-sm btn-info" title="{% trans 'عرض التفاصيل' %}">
                            <i class="fas fa-eye"></i> {% trans "التفاصيل" %}
                        </a>
                        <a href="#" class="btn btn-sm btn-info stock-movement-btn" data-id="{{ product.id }}" data-name="{{ product.name }}" data-bs-toggle="modal" data-bs-target="#stockMovementModal" title="{% trans 'حركة المخزون' %}">
                            <i class="fas fa-exchange-alt"></i> {% trans "الحركة" %}
                        </a>
                        <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-sm btn-primary" title="{% trans 'تعديل' %}">
                            <i class="fas fa-edit"></i> {% trans "تعديل" %}
                        </a>
                        <a href="#" class="btn btn-sm btn-success add-to-po" data-id="{{ product.id }}" data-name="{{ product.name }}" data-code="{{ product.code }}" data-quantity="{{ product.min_quantity|add:5|sub:product.quantity }}" title="{% trans 'إضافة إلى طلب الشراء' %}">
                            <i class="fas fa-cart-plus"></i> {% trans "إضافة" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <p>{% trans "لا توجد منتجات تحتاج إلى إعادة تعبئة" %}</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Stock Movement Modal -->
<div class="modal fade" id="stockMovementModal" tabindex="-1" aria-labelledby="stockMovementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockMovementModalLabel">{% trans "حركة المخزون" %}: <span id="productName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="stockMovementForm">
                    <input type="hidden" id="productId" name="product_id">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="movementType" class="form-label">{% trans "نوع الحركة" %}</label>
                            <select class="form-select" id="movementType" name="movement_type" required>
                                <option value="in">{% trans "وارد (إضافة)" %}</option>
                                <option value="out">{% trans "صادر (سحب)" %}</option>
                                <option value="adjustment">{% trans "تعديل" %}</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="quantity" class="form-label">{% trans "الكمية" %}</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reference" class="form-label">{% trans "المرجع" %}</label>
                        <input type="text" class="form-control" id="reference" name="reference" placeholder="{% trans 'مثال: فاتورة شراء رقم 123' %}">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>

                <hr>

                <h6 class="mb-3">{% trans "سجل الحركات السابقة" %}</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered" id="movementsTable">
                        <thead>
                            <tr>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "نوع الحركة" %}</th>
                                <th>{% trans "الكمية" %}</th>
                                <th>{% trans "المرجع" %}</th>
                            </tr>
                        </thead>
                        <tbody id="movementsTableBody">
                            <!-- سيتم ملء هذا الجزء ديناميكيًا -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                <button type="button" class="btn btn-primary" id="saveMovementBtn">{% trans "حفظ" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Purchase Order Modal -->
<div class="modal fade" id="createPurchaseOrderModal" tabindex="-1" aria-labelledby="createPurchaseOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPurchaseOrderModalLabel">{% trans "إنشاء طلب شراء" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="purchaseOrderForm" action="{% url 'inventory:create_purchase_order' %}" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="supplier" class="form-label">{% trans "المورد" %}</label>
                            <select class="form-select" id="supplier" name="supplier" required>
                                <option value="">{% trans "اختر المورد" %}</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="expected_delivery_date" class="form-label">{% trans "تاريخ التسليم المتوقع" %}</label>
                            <input type="date" class="form-control" id="expected_delivery_date" name="expected_delivery_date" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>

                    <h6 class="mt-4 mb-3">{% trans "المنتجات المطلوبة" %}</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="poItemsTable">
                            <thead>
                                <tr>
                                    <th>{% trans "الكود" %}</th>
                                    <th>{% trans "اسم المنتج" %}</th>
                                    <th>{% trans "الكمية" %}</th>
                                    <th>{% trans "الإجراءات" %}</th>
                                </tr>
                            </thead>
                            <tbody id="poItemsBody">
                                <tr id="noItemsRow">
                                    <td colspan="4" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-1"></i>
                        {% trans "يمكنك إضافة المنتجات إلى طلب الشراء من خلال تحديد المنتجات من الجدول أعلاه ثم النقر على زر 'إنشاء طلب شراء'، أو يمكنك النقر على أيقونة السلة بجانب كل منتج لإضافته مباشرة." %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary" id="submitPOBtn" disabled>{% trans "إنشاء طلب الشراء" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function generatePurchaseOrders() {
        // فتح نافذة إنشاء طلبات الشراء للمنتجات المختارة
        const selectedProducts = document.querySelectorAll('.product-select:checked');
        if (selectedProducts.length === 0) {
            alert('{% trans "يرجى اختيار منتج واحد على الأقل لإنشاء طلب شراء" %}');
            return;
        }

        // فتح نافذة طلب الشراء
        $('#purchaseOrderModal').modal('show');
    }

    function exportLowStock() {
        // تصدير المنتجات منخفضة المخزون فقط
        const lowStockProducts = document.querySelectorAll('.product-card.low');
        if (lowStockProducts.length === 0) {
            alert('{% trans "لا توجد منتجات منخفضة المخزون للتصدير" %}');
            return;
        }

        // تصدير البيانات
        exportFilteredData('low');
    }

    function exportOutOfStock() {
        // تصدير المنتجات النافدة فقط
        const outOfStockProducts = document.querySelectorAll('.product-card.out');
        if (outOfStockProducts.length === 0) {
            alert('{% trans "لا توجد منتجات نافدة للتصدير" %}');
            return;
        }

        // تصدير البيانات
        exportFilteredData('out');
    }

    function exportFilteredData(status) {
        // تصدير البيانات المفلترة حسب الحالة
        const products = [];
        const productCards = document.querySelectorAll(`.product-card.${status}`);

        productCards.forEach(card => {
            const productName = card.querySelector('.card-title').textContent.trim();
            const productCode = card.querySelector('.text-muted').textContent.trim();
            const quantity = card.querySelector('.stock-text').textContent.trim();

            products.push({
                name: productName,
                code: productCode,
                quantity: quantity,
                status: status === 'low' ? 'منخفض المخزون' : 'نفد المخزون'
            });
        });

        // تحويل إلى CSV وتحميل
        const csvContent = convertToCSV(products);
        downloadCSV(csvContent, `stock_alerts_${status}.csv`);
    }

    function convertToCSV(data) {
        const headers = ['اسم المنتج', 'الكود', 'الكمية', 'الحالة'];
        const csvRows = [headers.join(',')];

        data.forEach(row => {
            csvRows.push([row.name, row.code, row.quantity, row.status].join(','));
        });

        return csvRows.join('\n');
    }

    function downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'inventory:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // Select All Checkbox
        $('#selectAllProducts').change(function() {
            $('.product-select').prop('checked', $(this).prop('checked'));
            updateSelectedProducts();
        });

        // Individual Checkboxes
        $(document).on('change', '.product-select', function() {
            updateSelectedProducts();
        });

        // Apply all filters function
        function applyFilters() {
            var categoryId = $('#categoryFilter').val();
            var supplierId = $('#supplierFilter').val();
            var status = $('#stockStatusFilter').val();
            var searchTerm = $('#quickSearch').val().toLowerCase();
            
            $('.product-item').each(function() {
                var $item = $(this);
                var productData = {
                    category: $item.data('category'),
                    supplier: $item.data('supplier'),
                    status: $item.data('status'),
                    name: $item.find('.product-info h5').text().toLowerCase(),
                    code: $item.find('.product-code').text().toLowerCase(),
                    categoryText: $item.find('.meta-item:nth-child(1)').text().toLowerCase(),
                    supplierText: $item.find('.meta-item:nth-child(2)').text().toLowerCase()
                };
                
                // Check if item matches all filters
                var matchesCategory = !categoryId || productData.category == categoryId;
                var matchesSupplier = !supplierId || productData.supplier == supplierId;
                var matchesStatus = !status || productData.status == status;
                var matchesSearch = !searchTerm || 
                                    productData.name.includes(searchTerm) || 
                                    productData.code.includes(searchTerm) || 
                                    productData.categoryText.includes(searchTerm) || 
                                    productData.supplierText.includes(searchTerm);
                
                // Show/hide based on filter results
                if (matchesCategory && matchesSupplier && matchesStatus && matchesSearch) {
                    $item.fadeIn(300);
                } else {
                    $item.fadeOut(300);
                }
            });
            
            // Check if any products are visible
            setTimeout(function() {
                if ($('.product-item:visible').length === 0) {
                    if ($('#noProductsMessage').length === 0) {
                        $('#productsContainer').append('<div id="noProductsMessage" class="col-12"><div class="empty-state"><i class="fas fa-search"></i><p>{% trans "لا توجد منتجات تطابق معايير البحث" %}</p></div></div>');
                    }
                } else {
                    $('#noProductsMessage').remove();
                }
            }, 350);
        }

        // Category Filter
        $('#categoryFilter').change(function() {
            applyFilters();
        });

        // Supplier Filter
        $('#supplierFilter').change(function() {
            applyFilters();
        });

        // Stock Status Filter
        $('#stockStatusFilter').change(function() {
            applyFilters();
        });

        // Quick Search
        $('#searchBtn').click(function() {
            applyFilters();
        });

        $('#quickSearch').keyup(function() {
            applyFilters();
        });
        
        // Clear all filters
        $('.filter-section').append('<div class="text-center mt-3"><button id="clearFilters" class="btn btn-outline-secondary"><i class="fas fa-undo me-1"></i> {% trans "إعادة ضبط التصفية" %}</button></div>');
        
        $('#clearFilters').click(function() {
            $('#categoryFilter').val('');
            $('#supplierFilter').val('');
            $('#stockStatusFilter').val('');
            $('#quickSearch').val('');
            applyFilters();
        });


        // Stock Movement Modal
        $('.stock-movement-btn').click(function() {
            var productId = $(this).data('id');
            var productName = $(this).data('name');

            $('#productId').val(productId);
            $('#productName').text(productName);

            // Show loading indicator
            $('#movementsTableBody').html('<tr><td colspan="4" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل البيانات...</td></tr>');

            // Load product movements
            $.ajax({
                url: '/inventory/product/' + productId + '/movements/',
                type: 'GET',
                success: function(data) {
                    var tbody = $('#movementsTableBody');
                    tbody.empty();

                    if (data.movements && data.movements.length > 0) {
                        $.each(data.movements, function(i, movement) {
                            var typeClass = '';
                            if (movement.movement_type === 'in') typeClass = 'text-success';
                            else if (movement.movement_type === 'out') typeClass = 'text-danger';
                            else typeClass = 'text-primary';
                            
                            var row = '<tr>' +
                                '<td>' + movement.created_at + '</td>' +
                                '<td><span class="' + typeClass + '">' + movement.movement_type_display + '</span></td>' +
                                '<td>' + movement.quantity + '</td>' +
                                '<td>' + (movement.reference || '-') + '</td>' +
                                '</tr>';
                            tbody.append(row);
                        });
                    } else {
                        tbody.append('<tr><td colspan="4" class="text-center"><i class="fas fa-info-circle me-2 text-muted"></i>لا توجد حركات سابقة</td></tr>');
                    }
                },
                error: function() {
                    $('#movementsTableBody').html('<tr><td colspan="4" class="text-center text-danger"><i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء تحميل البيانات</td></tr>');
                }
            });
        });

        // Save Stock Movement
        $('#saveMovementBtn').click(function() {
            var form = $('#stockMovementForm');

            if (!form[0].checkValidity()) {
                form[0].reportValidity();
                return;
            }

            // Show loading state
            var $btn = $(this);
            var originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...');
            $btn.prop('disabled', true);

            var formData = {
                product_id: $('#productId').val(),
                movement_type: $('#movementType').val(),
                quantity: $('#quantity').val(),
                reference: $('#reference').val(),
                notes: $('#notes').val()
            };

            $.ajax({
                url: '/inventory/stock-movement/add/',
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message with toast instead of alert
                        var successToast = `
                            <div class="position-fixed top-0 end-0 p-3" style="z-index: 9999">
                                <div class="toast show bg-success text-white" role="alert" aria-live="assertive" aria-atomic="true">
                                    <div class="toast-header bg-success text-white">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <strong class="me-auto">نجاح</strong>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                                    </div>
                                    <div class="toast-body">
                                        تم حفظ حركة المخزون بنجاح
                                    </div>
                                </div>
                            </div>
                        `;
                        $(successToast).appendTo('body');
                        setTimeout(function() {
                            $('.toast').toast('hide');
                            // Close modal and reload page
                            $('#stockMovementModal').modal('hide');
                            location.reload();
                        }, 1500);
                    } else {
                        // Reset button state
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                        
                        // Show error message
                        var errorMsg = response.error || 'حدث خطأ غير معروف';
                        var errorToast = `
                            <div class="position-fixed top-0 end-0 p-3" style="z-index: 9999">
                                <div class="toast show bg-danger text-white" role="alert" aria-live="assertive" aria-atomic="true">
                                    <div class="toast-header bg-danger text-white">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong class="me-auto">خطأ</strong>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                                    </div>
                                    <div class="toast-body">
                                        ${errorMsg}
                                    </div>
                                </div>
                            </div>
                        `;
                        $(errorToast).appendTo('body');
                        setTimeout(function() {
                            $('.toast').toast('hide');
                        }, 3000);
                    }
                },
                error: function() {
                    // Reset button state
                    $btn.html(originalText);
                    $btn.prop('disabled', false);
                    
                    // Show error message
                    var errorToast = `
                        <div class="position-fixed top-0 end-0 p-3" style="z-index: 9999">
                            <div class="toast show bg-danger text-white" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="toast-header bg-danger text-white">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong class="me-auto">خطأ</strong>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body">
                                    حدث خطأ أثناء معالجة الطلب
                                </div>
                            </div>
                        </div>
                    `;
                    $(errorToast).appendTo('body');
                    setTimeout(function() {
                        $('.toast').toast('hide');
                    }, 3000);
                }
            });
        });

        // Add to Purchase Order
        $('.add-to-po').click(function(e) {
            e.preventDefault();
            var productId = $(this).data('id');
            var productName = $(this).data('name');
            var productCode = $(this).data('code');
            var suggestedQuantity = $(this).data('quantity');

            // Add visual feedback
            var $card = $(this).closest('.product-card');
            $card.addClass('border-success');
            setTimeout(function() {
                $card.removeClass('border-success');
            }, 1000);

            addProductToPO(productId, productName, productCode, suggestedQuantity);

            // Show notification
            var addedToast = `
                <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 9999">
                    <div class="toast show bg-success text-white" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-body">
                            <i class="fas fa-check-circle me-2"></i>
                            تمت إضافة ${productName} إلى طلب الشراء
                        </div>
                    </div>
                </div>
            `;
            $(addedToast).appendTo('body');
            setTimeout(function() {
                $('.toast').toast('hide');
            }, 1500);

            // Open the modal
            $('#createPurchaseOrderModal').modal('show');
        });

        // Update selected products
        function updateSelectedProducts() {
            var selectedProducts = [];

            $('.product-select:checked').each(function() {
                var $this = $(this);
                selectedProducts.push({
                    id: $this.val(),
                    name: $this.data('name'),
                    code: $this.data('code'),
                    quantity: $this.data('quantity')
                });
            });

            // Enable/disable submit button
            $('#submitPOBtn').prop('disabled', selectedProducts.length === 0);

            // Update PO items table
            updatePOItemsTable(selectedProducts);
            
            // Update selection counter
            var selectedCount = selectedProducts.length;
            if ($('#selectionCounter').length === 0) {
                $('.filter-section').after(`
                    <div id="selectionCounter" class="alert alert-info mb-4 d-flex justify-content-between align-items-center" style="display: none;">
                        <div>
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="selectedCountText">تم تحديد ${selectedCount} منتج</span>
                        </div>
                        <button id="createPOFromSelected" class="btn btn-primary">
                            <i class="fas fa-file-invoice me-1"></i> إنشاء طلب شراء
                        </button>
                    </div>
                `);
                
                // Add event listener to the new button
                $('#createPOFromSelected').click(function() {
                    $('#createPurchaseOrderModal').modal('show');
                });
            } else {
                $('#selectedCountText').text(`تم تحديد ${selectedCount} منتج`);
            }
            
            if (selectedCount > 0) {
                $('#selectionCounter').slideDown();
            } else {
                $('#selectionCounter').slideUp();
            }
        }

        // Add product to PO
        function addProductToPO(productId, productName, productCode, quantity) {
            // Check if product already exists
            var existingRow = $('#poItem_' + productId);

            if (existingRow.length > 0) {
                // Update quantity
                var currentQty = parseInt(existingRow.find('.item-quantity').val());
                existingRow.find('.item-quantity').val(currentQty + 1);
                // Highlight the updated row
                existingRow.addClass('bg-light');
                setTimeout(function() {
                    existingRow.removeClass('bg-light');
                }, 1000);
            } else {
                // Add new row
                var newRow = `
                    <tr id="poItem_${productId}" class="new-item">
                        <td>${productCode}</td>
                        <td>${productName}</td>
                        <td>
                            <input type="hidden" name="product_ids[]" value="${productId}">
                            <input type="number" class="form-control form-control-sm item-quantity" name="quantities[]" value="${quantity}" min="1">
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-item" data-id="${productId}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;

                // Remove "no items" row if exists
                $('#noItemsRow').remove();

                // Append new row
                $('#poItemsBody').append(newRow);
                
                // Highlight the new row
                setTimeout(function() {
                    $('#poItem_' + productId).removeClass('new-item');
                }, 1000);

                // Enable submit button
                $('#submitPOBtn').prop('disabled', false);
            }
        }

        // Update PO items table
        function updatePOItemsTable(products) {
            var tbody = $('#poItemsBody');
            tbody.empty();

            if (products.length === 0) {
                tbody.html('<tr id="noItemsRow"><td colspan="4" class="text-center"><i class="fas fa-shopping-cart text-muted me-2"></i>لم يتم إضافة منتجات بعد</td></tr>');
            } else {
                $.each(products, function(i, product) {
                    var row = `
                        <tr id="poItem_${product.id}">
                            <td>${product.code}</td>
                            <td>${product.name}</td>
                            <td>
                                <input type="hidden" name="product_ids[]" value="${product.id}">
                                <input type="number" class="form-control form-control-sm item-quantity" name="quantities[]" value="${product.quantity}" min="1">
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger remove-item" data-id="${product.id}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            }
        }

        // Remove item from PO
        $(document).on('click', '.remove-item', function() {
            var productId = $(this).data('id');
            var $row = $('#poItem_' + productId);
            var productName = $row.find('td:nth-child(2)').text();
            
            // Fade out and remove the row
            $row.fadeOut(300, function() {
                $(this).remove();
                
                // Uncheck the corresponding checkbox
                $('.product-select[value="' + productId + '"]').prop('checked', false);
                updateSelectedProducts();
                
                // If no items left, add "no items" row
                if ($('#poItemsBody tr').length === 0) {
                    $('#poItemsBody').html('<tr id="noItemsRow"><td colspan="4" class="text-center"><i class="fas fa-shopping-cart text-muted me-2"></i>لم يتم إضافة منتجات بعد</td></tr>');
                    $('#submitPOBtn').prop('disabled', true);
                }
                
                // Show notification
                var removedToast = `
                    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 9999">
                        <div class="toast show bg-secondary text-white" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="toast-body">
                                <i class="fas fa-trash me-2"></i>
                                تمت إزالة ${productName} من طلب الشراء
                            </div>
                        </div>
                    </div>
                `;
                $(removedToast).appendTo('body');
                setTimeout(function() {
                    $('.toast').toast('hide');
                }, 1500);
            });
        });

        // Form submission
        $('#purchaseOrderForm').submit(function(e) {
            if ($('#poItemsBody tr').not('#noItemsRow').length === 0) {
                e.preventDefault();
                
                var errorToast = `
                    <div class="position-fixed top-0 end-0 p-3" style="z-index: 9999">
                        <div class="toast show bg-danger text-white" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="toast-header bg-danger text-white">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong class="me-auto">خطأ</strong>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                            <div class="toast-body">
                                يرجى إضافة منتج واحد على الأقل إلى طلب الشراء
                            </div>
                        </div>
                    </div>
                `;
                $(errorToast).appendTo('body');
                setTimeout(function() {
                    $('.toast').toast('hide');
                }, 3000);
                
                return false;
            }
            
            // Show loading state
            var $btn = $('#submitPOBtn');
            var originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-1"></i> جاري الإنشاء...');
            $btn.prop('disabled', true);
            
            return true;
        });

        // Add CSS for new item highlight
        $('<style>\n\
            .new-item { animation: highlightRow 1s ease; }\n\
            @keyframes highlightRow {\n\
                0% { background-color: rgba(25, 135, 84, 0.2); }\n\
                100% { background-color: transparent; }\n\
            }\n\
            .product-card.border-success {\n\
                border: 2px solid #198754 !important;\n\
                transition: all 0.3s ease;\n\
            }\n\
        </style>').appendTo('head');

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // Initialize filters on page load
        applyFilters();
        
        // تحديث روابط التصدير مع الفلاتر الحالية
        function updateExportLinks() {
            // الحصول على قيم الفلاتر الحالية
            var searchQuery = $('#quickSearch').val();
            var categoryFilter = $('#categoryFilter').val();
            var supplierFilter = $('#supplierFilter').val();
            var stockStatusFilter = $('#stockStatusFilter').val();

            // تحديث روابط التصدير
            $('.export-btn').each(function() {
                var exportType = $(this).data('type');
                var baseUrl = '{% url "inventory:export_stock_alerts" %}';
                var newUrl = baseUrl + '?type=' + exportType;

                // إضافة متغيرات الفلترة إلى الرابط
                if (searchQuery) newUrl += '&search=' + encodeURIComponent(searchQuery);
                if (categoryFilter) newUrl += '&category=' + encodeURIComponent(categoryFilter);
                if (supplierFilter) newUrl += '&supplier=' + encodeURIComponent(supplierFilter);
                if (stockStatusFilter) newUrl += '&status=' + encodeURIComponent(stockStatusFilter);

                // تحديث الرابط
                $(this).attr('href', newUrl);
            });
        }

        // معالجة أزرار التصدير
        $('#exportPdfBtn, #exportExcelBtn').click(function(e) {
            e.preventDefault();

            var exportType = $(this).data('type');
            var searchQuery = $('#quickSearch').val();
            var categoryFilter = $('#categoryFilter').val();
            var supplierFilter = $('#supplierFilter').val();
            var stockStatusFilter = $('#stockStatusFilter').val();

            // بناء URL التصدير مع الفلاتر
            var exportUrl = '{% url "inventory:export_stock_alerts" %}?type=' + exportType;

            if (searchQuery) exportUrl += '&search=' + encodeURIComponent(searchQuery);
            if (categoryFilter) exportUrl += '&category=' + encodeURIComponent(categoryFilter);
            if (supplierFilter) exportUrl += '&supplier=' + encodeURIComponent(supplierFilter);
            if (stockStatusFilter) exportUrl += '&status=' + encodeURIComponent(stockStatusFilter);

            // فتح رابط التصدير
            window.open(exportUrl, '_blank');
        });

        // تحديث روابط التصدير عند تغيير الفلاتر
        $('#quickSearch, #categoryFilter, #supplierFilter, #stockStatusFilter').on('change keyup', function() {
            updateExportLinks();
        });

        // تحديث روابط التصدير عند تحميل الصفحة
        updateExportLinks();

        // تطبيق الفلاتر من URL عند تحميل الصفحة
        $(document).ready(function() {
            // الحصول على معاملات URL
            var urlParams = new URLSearchParams(window.location.search);
            var status = urlParams.get('status');

            // تطبيق فلتر الحالة إذا كان موجود في URL
            if (status) {
                $('#stockStatusFilter').val(status);
                applyFilters();
            }
        });
    });
</script>
{% endblock %}
