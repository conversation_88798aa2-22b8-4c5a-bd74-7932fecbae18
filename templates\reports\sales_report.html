{% extends 'base.html' %}
{% load i18n %}
{% load static %}

<!-- CSRF Token for AJAX requests -->
<meta name="csrf-token" content="{{ csrf_token }}">
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

{% block title %}{% trans "تقرير المبيعات المتقدم" %} | {% trans "زاكورة" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    * {
        box-sizing: border-box;
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
    }

    .stats-card.success::before { background: var(--gradient-success); }
    .stats-card.warning::before { background: var(--gradient-warning); }
    .stats-card.info::before { background: var(--gradient-info); }
    .stats-card.danger::before { background: var(--gradient-danger); }

    .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .stats-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--secondary-color);
        margin: 0;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        background: var(--gradient-primary);
        box-shadow: var(--shadow-md);
    }

    .stats-value {
        font-size: 2.2rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stats-change {
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-weight: 500;
    }

    .stats-change.positive { color: var(--success-color); }
    .stats-change.negative { color: var(--danger-color); }

    .stats-card.success .stats-icon { background: var(--gradient-success); }
    .stats-card.warning .stats-icon { background: var(--gradient-warning); }
    .stats-card.info .stats-icon { background: var(--gradient-info); }
    .stats-card.danger .stats-icon { background: var(--gradient-danger); }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
    }

    .filter-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-toggle {
        background: var(--gradient-primary);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .filter-toggle:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .filter-toggle i {
        transition: transform 0.3s ease;
    }

    .filter-toggle[aria-expanded="true"] i {
        transform: rotate(180deg);
    }

    #filterCollapse {
        overflow: hidden;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        outline: none;
    }

    .btn {
        border-radius: 8px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-primary {
        background: var(--gradient-primary);
        color: white;
    }

    .btn-secondary {
        background: linear-gradient(135deg, var(--secondary-color) 0%, #475569 100%);
        color: white;
    }

    .btn-success {
        background: var(--gradient-success);
        color: white;
    }

    .btn-info {
        background: var(--gradient-info);
        color: white;
    }

    .btn-warning {
        background: var(--gradient-warning);
        color: white;
    }

    .btn-danger {
        background: var(--gradient-danger);
        color: white;
    }

    /* Animation */
    .fade-in {
        opacity: 0;
        animation: fadeIn 0.5s ease-in-out forwards;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Charts Section */
    .charts-section {
        margin-bottom: 2rem;
    }

    .chart-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        height: 400px;
        margin-bottom: 1.5rem;
    }

    .chart-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .chart-container {
        position: relative;
        height: calc(100% - 60px);
    }

    /* Table Section */
    .table-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
    }

    .table-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .table-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    /* DataTable Customization */
    .dataTables_wrapper {
        direction: rtl;
    }

    .dataTables_filter {
        text-align: left;
        margin-bottom: 1rem;
    }

    .dataTables_filter input {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 0.5rem;
        margin-right: 0.5rem;
    }

    .dataTables_length {
        text-align: right;
        margin-bottom: 1rem;
    }

    .dataTables_info {
        text-align: right;
        margin-top: 1rem;
    }

    .dataTables_paginate {
        text-align: left;
        margin-top: 1rem;
    }

    .table {
        direction: rtl;
    }

    .table thead th {
        background: var(--light-color);
        border-bottom: 2px solid var(--border-color);
        font-weight: 600;
        color: var(--dark-color);
        padding: 1rem 0.75rem;
    }

    .table tbody td {
        padding: 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid var(--border-color);
    }

    .table tbody tr:hover {
        background: rgba(37, 99, 235, 0.05);
    }

    .table-row-visible {
        animation: fadeInRow 0.3s ease-in-out;
    }

    .table-row-hidden {
        animation: fadeOutRow 0.3s ease-in-out;
    }

    @keyframes fadeInRow {
        from { opacity: 0; transform: translateX(20px); }
        to { opacity: 1; transform: translateX(0); }
    }

    @keyframes fadeOutRow {
        from { opacity: 1; transform: translateX(0); }
        to { opacity: 0; transform: translateX(-20px); }
    }

    /* Filter status indicators */
    .filter-active {
        border-left: 4px solid var(--primary-color) !important;
        background: rgba(37, 99, 235, 0.05);
    }

    .filter-section.has-active-filters {
        border: 2px solid var(--primary-color);
        box-shadow: 0 0 15px rgba(37, 99, 235, 0.2);
    }

    .filter-section.has-active-filters .filter-title {
        color: var(--primary-color);
    }

    /* Results counter animation */
    #resultsCount {
        transition: all 0.3s ease;
    }

    #resultsCount.updated {
        transform: scale(1.2);
        color: var(--primary-color) !important;
    }

    /* Customer and Rep Avatars */
    .customer-avatar, .rep-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 16px;
        flex-shrink: 0;
    }

    .rep-avatar {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    /* Parts List */
    .parts-list {
        max-width: 200px;
    }

    .part-item {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .part-item:last-child {
        margin-bottom: 0;
    }

    /* Select2 Customization */
    .select2-container--bootstrap-5 .select2-selection {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        min-height: calc(2.25rem + 2px);
    }

    .select2-container--bootstrap-5 .select2-selection:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }

    /* Alerts */
    .alert {
        border-radius: 10px;
        border: none;
    }

    .alert-heading {
        font-weight: 600;
        margin-bottom: 0.75rem;
    }

    /* Print Styles */
    @media print {
        .header-actions,
        .filter-section,
        .table-actions,
        .btn,
        .pagination {
            display: none !important;
        }

        .page-header {
            background: var(--primary-color) !important;
            -webkit-print-color-adjust: exact;
        }

        .stats-card,
        .chart-card,
        .table-section {
            break-inside: avoid;
            box-shadow: none !important;
            border: 1px solid #ddd !important;
        }

        .table {
            font-size: 0.8rem;
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-title { font-size: 1.8rem; }
        .stats-grid { grid-template-columns: 1fr; }
        .header-actions { text-align: center; margin-top: 1rem; }
        .filter-header { flex-direction: column; gap: 1rem; }
        .table-header { flex-direction: column; gap: 1rem; }
        .chart-card { height: 300px; }
        .customer-avatar, .rep-avatar { width: 30px; height: 30px; font-size: 12px; }
        .parts-list { max-width: 150px; }
    }

    @media (max-width: 576px) {
        .page-header { padding: 1.5rem 0; }
        .stats-card { padding: 1rem; }
        .filter-section, .table-section { padding: 1rem; }
        .chart-card { height: 250px; }
        .table-responsive { font-size: 0.8rem; }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-chart-line me-3"></i>
                        {% trans "تقرير المبيعات المتقدم" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "تحليل شامل ومتقدم لأداء المبيعات مع إحصائيات تفاعلية وتنبؤات ذكية" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <button type="button" class="btn btn-success btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-1"></i>
                                {% trans "طباعة" %}
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download me-1"></i>
                                    {% trans "تصدير" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel text-success me-2"></i>Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                        <i class="fas fa-file-csv text-primary me-2"></i>CSV
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'reports:index' %}">{% trans "التقارير" %}</a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "تقرير المبيعات" %}</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Statistics Dashboard -->
        <div class="stats-grid fade-in">
            <!-- Total Sales -->
            <div class="stats-card">
                <div class="stats-header">
                    <div>
                        <h6 class="stats-title">{% trans "إجمالي المبيعات" %}</h6>
                        <div class="stats-value">{{ total_sales|floatformat:0 }} {% trans "د.م" %}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12.5% {% trans "من الشهر الماضي" %}</span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>

            <!-- Total Profit -->
            <div class="stats-card success">
                <div class="stats-header">
                    <div>
                        <h6 class="stats-title">{% trans "إجمالي الأرباح" %}</h6>
                        <div class="stats-value">{{ total_profit|floatformat:0 }} {% trans "د.م" %}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8.3% {% trans "من الشهر الماضي" %}</span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>

            <!-- Number of Invoices -->
            <div class="stats-card info">
                <div class="stats-header">
                    <div>
                        <h6 class="stats-title">{% trans "عدد الفواتير" %}</h6>
                        <div class="stats-value">{{ invoices_count }}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+15.2% {% trans "من الشهر الماضي" %}</span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                </div>
            </div>

            <!-- Top Selling Parts -->
            <div class="stats-card warning">
                <div class="stats-header">
                    <div>
                        <h6 class="stats-title">{% trans "أكثر القطع مبيعاً" %}</h6>
                        <div class="stats-value">{{ top_part_name }}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>{{ top_part_sales }} {% trans "قطعة" %}</span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                </div>
            </div>

            <!-- Fast Moving Parts -->
            <div class="stats-card danger">
                <div class="stats-header">
                    <div>
                        <h6 class="stats-title">{% trans "القطع سريعة الحركة" %}</h6>
                        <div class="stats-value">{{ fast_moving_count }}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>{% trans "قطعة نشطة" %}</span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                </div>
            </div>

            <!-- Average Sale Value -->
            <div class="stats-card">
                <div class="stats-header">
                    <div>
                        <h6 class="stats-title">{% trans "متوسط قيمة البيع" %}</h6>
                        <div class="stats-value">{{ average_sale|floatformat:0 }} {% trans "د.م" %}</div>
                        <div class="stats-change positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+5.7% {% trans "من الشهر الماضي" %}</span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Filters Section -->
        <div class="filter-section fade-in">
            <div class="filter-header">
                <h3 class="filter-title">
                    <i class="fas fa-filter"></i>
                    {% trans "فلاتر التقرير المتقدمة" %}
                </h3>
                <button type="button" class="filter-toggle" aria-expanded="true" aria-controls="filterCollapse">
                    <i class="fas fa-chevron-down"></i>
                    {% trans "إظهار/إخفاء الفلاتر" %}
                </button>
            </div>

            <div class="collapse show" id="filterCollapse">
                <form method="get" id="filterForm" action="{% url 'reports:sales_report' %}">
                    <div class="row">
                        <!-- Date Range Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="start_date" class="form-label">
                                <i class="fas fa-calendar-alt text-primary"></i>
                                {% trans "من تاريخ" %}
                            </label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   value="{{ filters.start_date }}">
                        </div>

                        <div class="col-md-3 mb-3">
                            <label for="end_date" class="form-label">
                                <i class="fas fa-calendar-alt text-primary"></i>
                                {% trans "إلى تاريخ" %}
                            </label>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                   value="{{ filters.end_date }}">
                        </div>

                        <!-- Quick Date Selection -->
                        <div class="col-md-3 mb-3">
                            <label for="quick_date" class="form-label">
                                <i class="fas fa-clock text-info"></i>
                                {% trans "اختيار سريع" %}
                            </label>
                            <select class="form-select" id="quick_date" name="quick_date">
                                <option value="">{% trans "اختر فترة" %}</option>
                                <option value="today" {% if filters.quick_date == 'today' %}selected{% endif %}>
                                    {% trans "اليوم" %}
                                </option>
                                <option value="yesterday" {% if filters.quick_date == 'yesterday' %}selected{% endif %}>
                                    {% trans "أمس" %}
                                </option>
                                <option value="this_week" {% if filters.quick_date == 'this_week' %}selected{% endif %}>
                                    {% trans "هذا الأسبوع" %}
                                </option>
                                <option value="last_week" {% if filters.quick_date == 'last_week' %}selected{% endif %}>
                                    {% trans "الأسبوع الماضي" %}
                                </option>
                                <option value="this_month" {% if filters.quick_date == 'this_month' %}selected{% endif %}>
                                    {% trans "هذا الشهر" %}
                                </option>
                                <option value="last_month" {% if filters.quick_date == 'last_month' %}selected{% endif %}>
                                    {% trans "الشهر الماضي" %}
                                </option>
                                <option value="this_quarter" {% if filters.quick_date == 'this_quarter' %}selected{% endif %}>
                                    {% trans "هذا الربع" %}
                                </option>
                                <option value="this_year" {% if filters.quick_date == 'this_year' %}selected{% endif %}>
                                    {% trans "هذا العام" %}
                                </option>
                                <option value="last_year" {% if filters.quick_date == 'last_year' %}selected{% endif %}>
                                    {% trans "العام الماضي" %}
                                </option>
                            </select>
                        </div>

                        <!-- Payment Status Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="payment_status" class="form-label">
                                <i class="fas fa-credit-card text-warning"></i>
                                {% trans "حالة الدفع" %}
                            </label>
                            <select class="form-select" id="payment_status" name="payment_status">
                                <option value="">{% trans "جميع الحالات" %}</option>
                                <option value="paid" {% if filters.payment_status == 'paid' %}selected{% endif %}>
                                    {% trans "مدفوع" %}
                                </option>
                                <option value="unpaid" {% if filters.payment_status == 'unpaid' %}selected{% endif %}>
                                    {% trans "غير مدفوع" %}
                                </option>
                                <option value="partial" {% if filters.payment_status == 'partial' %}selected{% endif %}>
                                    {% trans "مدفوع جزئياً" %}
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Customer Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="customer" class="form-label">
                                <i class="fas fa-user text-success"></i>
                                {% trans "العميل" %}
                            </label>
                            <select class="form-select select2" id="customer" name="customer">
                                <option value="">{% trans "جميع العملاء" %}</option>
                                {% for customer in customers %}
                                    <option value="{{ customer.id }}" {% if filters.customer == customer.id|stringformat:"s" %}selected{% endif %}>
                                        {{ customer.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Category Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="category" class="form-label">
                                <i class="fas fa-tags text-primary"></i>
                                {% trans "فئة القطعة" %}
                            </label>
                            <select class="form-select select2" id="category" name="category">
                                <option value="">{% trans "جميع الفئات" %}</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if filters.category == category.id|stringformat:"s" %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Part Type Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="part_type" class="form-label">
                                <i class="fas fa-cog text-info"></i>
                                {% trans "نوع القطعة" %}
                            </label>
                            <select class="form-select select2" id="part_type" name="part_type">
                                <option value="">{% trans "جميع الأنواع" %}</option>
                                <option value="fast_moving" {% if filters.part_type == 'fast_moving' %}selected{% endif %}>
                                    {% trans "سريعة الحركة" %}
                                </option>
                                <option value="slow_moving" {% if filters.part_type == 'slow_moving' %}selected{% endif %}>
                                    {% trans "بطيئة الحركة" %}
                                </option>
                                <option value="high_profit" {% if filters.part_type == 'high_profit' %}selected{% endif %}>
                                    {% trans "عالية الربح" %}
                                </option>
                            </select>
                        </div>

                        <!-- Sales Representative Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="sales_rep" class="form-label">
                                <i class="fas fa-user-tie text-secondary"></i>
                                {% trans "مندوب المبيعات" %}
                            </label>
                            <select class="form-select select2" id="sales_rep" name="sales_rep">
                                <option value="">{% trans "جميع المندوبين" %}</option>
                                {% for rep in sales_reps %}
                                    <option value="{{ rep.id }}" {% if filters.sales_rep == rep.id|stringformat:"s" %}selected{% endif %}>
                                        {{ rep.get_full_name|default:rep.username }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- Filter Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="filter-actions">
                                    <button type="submit" class="btn btn-primary" id="applyFiltersBtn">
                                        <i class="fas fa-filter"></i>
                                        {% trans "تطبيق الفلاتر" %}
                                    </button>
                                    <button type="button" class="btn btn-info ms-2" id="refreshChartsBtn" onclick="loadChartsData()">
                                        <i class="fas fa-chart-line"></i>
                                        تحديث الرسوم البيانية
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                        <i class="fas fa-undo"></i>
                                        {% trans "إعادة تعيين" %}
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="saveFilters()">
                                        <i class="fas fa-save"></i>
                                        {% trans "حفظ الفلاتر" %}
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="predictSales()">
                                        <i class="fas fa-crystal-ball"></i>
                                        {% trans "التنبؤ بالمبيعات" %}
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="exportFilteredData()">
                                        <i class="fas fa-download"></i>
                                        {% trans "تصدير النتائج" %}
                                    </button>
                                </div>
                                <div class="filter-info">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        {% trans "عدد النتائج" %}: <span class="fw-bold text-primary" id="resultsCount">{{ sales|length }}</span>
                                </small>
                                <br>
                                <small class="text-muted">
                                    <i class="fas fa-filter"></i>
                                    <span id="activeFiltersCount">لا توجد فلاتر نشطة</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section fade-in">
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            {% trans "اتجاه المبيعات الشهرية" %}
                        </h5>
                        <div class="chart-container">
                            <canvas id="salesTrendChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-pie text-success me-2"></i>
                            {% trans "توزيع المبيعات حسب الفئات" %}
                        </h5>
                        <div class="chart-container">
                            <canvas id="categorySalesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-bar text-warning me-2"></i>
                            {% trans "أفضل 10 قطع مبيعاً" %}
                        </h5>
                        <div class="chart-container">
                            <canvas id="topPartsChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-area text-info me-2"></i>
                            {% trans "تحليل الطلب والمخزون" %}
                        </h5>
                        <div class="chart-container">
                            <canvas id="demandAnalysisChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Predictive Analytics Section -->
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-crystal-ball text-danger me-2"></i>
                            {% trans "التنبؤ بالمبيعات المستقبلية" %}
                            <small class="text-muted ms-2">{% trans "(بناءً على البيانات التاريخية)" %}</small>
                        </h5>
                        <div class="chart-container">
                            <canvas id="salesPredictionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Data Table -->
        <div class="table-section fade-in">
            <div class="table-header">
                <h3 class="table-title">
                    <i class="fas fa-table text-primary me-2"></i>
                    {% trans "جدول المبيعات التفاعلي" %}
                </h3>
                <div class="table-actions">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success btn-sm" id="showPaid" data-status="paid">
                            <i class="fas fa-check-circle"></i> {% trans "مدفوع" %}
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" id="showUnpaid" data-status="unpaid">
                            <i class="fas fa-times-circle"></i> {% trans "غير مدفوع" %}
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" id="showPartial" data-status="partial">
                            <i class="fas fa-clock"></i> {% trans "جزئي" %}
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm active" id="showAll" data-status="all">
                            <i class="fas fa-list"></i> {% trans "الكل" %}
                        </button>
                    </div>
                    <div class="btn-group ms-2" role="group">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="exportTableToPDF()">
                            <i class="fas fa-file-pdf"></i> PDF
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="exportTableToExcel()">
                            <i class="fas fa-file-excel"></i> Excel
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover" id="salesTable">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "تاريخ البيع" %}</th>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <th>{% trans "اسم العميل" %}</th>
                            <th>{% trans "إجمالي المبلغ" %}</th>
                            <th>{% trans "حالة الدفع" %}</th>
                            <th>{% trans "القطع المباعة" %}</th>
                            <th>{% trans "مندوب المبيعات" %}</th>
                            <th>{% trans "الربح" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales %}
                        <tr data-payment-status="{{ sale.payment_status }}">
                            <td>
                                <span class="text-muted">{{ sale.date }}</span>
                                <br>
                                <small class="text-secondary">{{ sale.time }}</small>
                            </td>
                            <td>
                                <strong class="text-primary">#{{ sale.invoice_number }}</strong>
                                {% if sale.is_urgent %}
                                    <span class="badge bg-danger ms-1">{% trans "عاجل" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="customer-avatar me-2">
                                        {{ sale.customer_name|first|upper }}
                                    </div>
                                    <div>
                                        <strong>{{ sale.customer_name }}</strong>
                                        {% if sale.customer_phone %}
                                            <br><small class="text-muted">{{ sale.customer_phone }}</small>
                                        {% endif %}
                                        {% if sale.customer_is_vip %}
                                            <span class="badge bg-warning text-dark ms-1">VIP</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <strong class="text-success">{{ sale.total_amount|floatformat:2 }} {% trans "د.م" %}</strong>
                                {% if sale.discount_amount %}
                                    <br><small class="text-muted">{% trans "خصم" %}: {{ sale.discount_amount|floatformat:2 }} {% trans "د.م" %}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.payment_status == 'paid' %}
                                    <span class="badge bg-success">{% trans "مدفوع" %}</span>
                                {% elif sale.payment_status == 'unpaid' %}
                                    <span class="badge bg-danger">{% trans "غير مدفوع" %}</span>
                                {% elif sale.payment_status == 'partial' %}
                                    <span class="badge bg-warning">{% trans "جزئي" %}</span>
                                    <br><small class="text-muted">{{ sale.paid_amount|floatformat:2 }} / {{ sale.total_amount|floatformat:2 }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="parts-list">
                                    {% for item in sale.items|slice:":3" %}
                                        <div class="part-item">
                                            <strong>{{ item.part_name }}</strong>
                                            <span class="text-muted">({{ item.quantity }})</span>
                                        </div>
                                    {% endfor %}
                                    {% if sale.items_count > 3 %}
                                        <small class="text-muted">{% trans "و" %} {{ sale.items_count|add:"-3" }} {% trans "قطع أخرى" %}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if sale.sales_rep_name %}
                                    <div class="d-flex align-items-center">
                                        <div class="rep-avatar me-2">
                                            {{ sale.sales_rep_name|first|upper }}
                                        </div>
                                        <span>{{ sale.sales_rep_name }}</span>
                                    </div>
                                {% else %}
                                    <span class="text-muted">{% trans "غير محدد" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-info">{{ sale.profit_amount|floatformat:2 }} {% trans "د.م" %}</strong>
                                <br>
                                <small class="text-muted">{{ sale.profit_margin|floatformat:1 }}%</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewSaleDetails({{ sale.id }})" data-bs-toggle="tooltip" title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="editSale({{ sale.id }})" data-bs-toggle="tooltip" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="printInvoice({{ sale.id }})" data-bs-toggle="tooltip" title="{% trans 'طباعة الفاتورة' %}">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center text-muted py-5">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <br>
                                <h5>{% trans "لا توجد مبيعات" %}</h5>
                                <p>{% trans "لا توجد مبيعات تطابق المعايير المحددة" %}</p>
                                <button type="button" class="btn btn-primary" onclick="resetFilters()">
                                    <i class="fas fa-undo me-1"></i>
                                    {% trans "إعادة تعيين الفلاتر" %}
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if sales.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if sales.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ sales.previous_page_number }}">
                                <i class="fas fa-chevron-right"></i> {% trans "السابق" %}
                            </a>
                        </li>
                    {% endif %}

                    {% for num in sales.paginator.page_range %}
                        {% if sales.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > sales.number|add:'-3' and num < sales.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if sales.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ sales.next_page_number }}">
                                {% trans "التالي" %} <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>

        <!-- Alerts and Notifications Section -->
        <div class="row fade-in">
            <div class="col-lg-6 mb-4">
                <div class="alert alert-warning border-0 shadow-sm">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% trans "تنبيهات المبيعات" %}
                    </h6>
                    <ul class="mb-0">
                        <li>{% trans "انخفاض في مبيعات فئة الإطارات بنسبة 15%" %}</li>
                        <li>{% trans "ارتفاع غير متوقع في مبيعات قطع المحرك" %}</li>
                        <li>{% trans "3 عملاء لم يسددوا مستحقاتهم منذ أكثر من 30 يوم" %}</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="alert alert-info border-0 shadow-sm">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-2"></i>
                        {% trans "توصيات ذكية" %}
                    </h6>
                    <ul class="mb-0">
                        <li>{% trans "يُنصح بزيادة مخزون فلاتر الزيت" %}</li>
                        <li>{% trans "فرصة لتقديم عروض على القطع بطيئة الحركة" %}</li>
                        <li>{% trans "العميل أحمد محمد مرشح لبرنامج العملاء المميزين" %}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- jQuery and Bootstrap are already loaded in base.html -->
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

<script>
$(document).ready(function() {
    // Prevent auto-refresh issues
    window.isUpdating = false;

    // Disable DataTables warnings in console
    $.fn.dataTable.ext.errMode = 'none';

    // Handle DataTables errors
    $(document).on('error.dt', function(e, settings, techNote, message) {
        console.log('DataTables error handled:', message);
        // Optionally show a user-friendly message
        // showNotification('حدث خطأ في عرض الجدول، جاري إعادة التحميل...', 'warning');
    });

    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        dir: 'rtl',
        placeholder: 'اختر...',
        allowClear: true,
        language: {
            noResults: function() {
                return "لا توجد نتائج";
            },
            searching: function() {
                return "جاري البحث...";
            }
        }
    });

    // Initialize DataTable
    function initializeDataTable() {
        // Destroy existing DataTable if it exists
        if ($.fn.DataTable.isDataTable('#salesTable')) {
            $('#salesTable').DataTable().clear().destroy();
            $('#salesTable').empty();
        }

        // Wait a moment before reinitializing
        setTimeout(function() {
            try {
                return $('#salesTable').DataTable({
        language: {
            "sProcessing": "جاري التحميل...",
            "sLengthMenu": "أظهر _MENU_ مدخلات",
            "sZeroRecords": "لم يعثر على أية سجلات",
            "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
            "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
            "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
            "sInfoPostFix": "",
            "sSearch": "ابحث:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "الأول",
                "sPrevious": "السابق",
                "sNext": "التالي",
                "sLast": "الأخير"
            }
        },
        responsive: true,
        pageLength: 10,
        order: [[0, 'desc']], // Sort by date descending
        columnDefs: [
            { orderable: false, targets: [5, 8] }, // Disable sorting for parts and actions columns
            { className: 'text-center', targets: [4, 8] }, // Center align status and actions
            { width: "120px", targets: [0] }, // Date column width
            { width: "140px", targets: [1] }, // Invoice number column width
            { width: "180px", targets: [2] }, // Customer column width
            { width: "120px", targets: [3] }, // Amount column width
            { width: "100px", targets: [4] }, // Status column width
            { width: "200px", targets: [5] }, // Parts column width
            { width: "150px", targets: [6] }, // Sales rep column width
            { width: "100px", targets: [7] }, // Profit column width
            { width: "160px", targets: [8] }  // Actions column width
        ],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> طباعة',
                className: 'btn btn-info btn-sm'
            }
        ],
        scrollX: true,
        autoWidth: false,
        // Handle errors gracefully
        error: function(xhr, error, thrown) {
            console.log('DataTable error:', error);
            hideLoading();
        }
                });
            } catch (error) {
                console.log('DataTable initialization error:', error);
            }
        }, 100);
    }

    // Initialize DataTable on page load
    try {
        var table = initializeDataTable();
    } catch (error) {
        console.log('DataTable initialization error:', error);
    }

    // Payment status filter
    var activeFilters = ['paid', 'unpaid', 'partial']; // Show all by default

    $('#showPaid, #showUnpaid, #showPartial, #showAll').click(function() {
        var status = $(this).data('status');

        if (status === 'all') {
            // Show all
            activeFilters = ['paid', 'unpaid', 'partial'];
            $('#showPaid, #showUnpaid, #showPartial, #showAll').removeClass('active');
            $('#showAll').addClass('active');
        } else {
            // Toggle individual status
            $('#showAll').removeClass('active');
            $(this).toggleClass('active');

            if ($(this).hasClass('active')) {
                if (!activeFilters.includes(status)) {
                    activeFilters.push(status);
                }
            } else {
                activeFilters = activeFilters.filter(f => f !== status);
            }
        }

        // Apply filters to DataTable
        table.rows().every(function() {
            var row = this.node();
            var rowStatus = $(row).data('payment-status');
            if (activeFilters.includes(rowStatus)) {
                $(row).show();
            } else {
                $(row).hide();
            }
        });
        table.draw();
    });

    // Set initial state
    $('#showAll').addClass('active');

    // Quick date selection
    $('#quick_date').change(function() {
        var selectedPeriod = $(this).val();
        var today = new Date();
        var startDate, endDate;

        switch(selectedPeriod) {
            case 'today':
                startDate = endDate = today.toISOString().split('T')[0];
                break;
            case 'yesterday':
                var yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                startDate = endDate = yesterday.toISOString().split('T')[0];
                break;
            case 'this_week':
                var firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
                startDate = firstDay.toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_week':
                var lastWeekStart = new Date(today.setDate(today.getDate() - today.getDay() - 7));
                var lastWeekEnd = new Date(today.setDate(today.getDate() - today.getDay() - 1));
                startDate = lastWeekStart.toISOString().split('T')[0];
                endDate = lastWeekEnd.toISOString().split('T')[0];
                break;
            case 'this_month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_month':
                startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1).toISOString().split('T')[0];
                endDate = new Date(today.getFullYear(), today.getMonth(), 0).toISOString().split('T')[0];
                break;
            case 'this_quarter':
                var quarter = Math.floor((today.getMonth() + 3) / 3);
                startDate = new Date(today.getFullYear(), (quarter - 1) * 3, 1).toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'this_year':
                startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_year':
                startDate = new Date(today.getFullYear() - 1, 0, 1).toISOString().split('T')[0];
                endDate = new Date(today.getFullYear() - 1, 11, 31).toISOString().split('T')[0];
                break;
        }

        if (startDate && endDate) {
            $('#start_date').val(startDate);
            $('#end_date').val(endDate);
            // Auto submit the form
            applyFilters();
            // Also update charts immediately with new dates
            setTimeout(function() {
                loadChartsData();
            }, 2000);
        }
    });

    // Apply filters function
    function applyFilters() {
        showLoading();

        // Destroy DataTable before applying filters to avoid column mismatch
        if ($.fn.DataTable.isDataTable('#salesTable')) {
            $('#salesTable').DataTable().destroy();
        }

        $('#filterForm').submit();
    }

    // Form submission handler
    $('#filterForm').on('submit', function(e) {
        showLoading();
        // Store that we need to reload charts after page load
        sessionStorage.setItem('reloadCharts', 'true');
        // Let the form submit normally to Django view
    });

    // Function to reload charts when filters change
    function reloadChartsWithCurrentFilters() {
        // Reload charts with current URL parameters
        loadChartsData();
    }

    // Override the form submission to also reload charts after page reload
    var originalSubmit = $('#filterForm')[0].submit;
    $('#filterForm')[0].submit = function() {
        // Store that we need to reload charts after page load
        sessionStorage.setItem('reloadCharts', 'true');
        return originalSubmit.call(this);
    };

    // Check if we need to reload charts after page load
    if (sessionStorage.getItem('reloadCharts') === 'true') {
        sessionStorage.removeItem('reloadCharts');
        // Reload charts after a short delay to ensure page is fully loaded
        setTimeout(function() {
            loadChartsData();
        }, 1500);
    }

    // Add visual indicators for active filters (without auto-apply)
    $('#start_date, #end_date, #payment_status, #customer, #category, #part_type, #sales_rep').change(function() {
        // Add visual indicator for active filter
        var element = $(this);
        if (element.val() && element.val() !== '') {
            element.addClass('filter-active');
        } else {
            element.removeClass('filter-active');
        }

        // Update filter info without applying filters
        updateFilterInfo();
    });

    // Apply filters on page load if URL has parameters
    $(document).ready(function() {
        var urlParams = new URLSearchParams(window.location.search);
        var hasFilters = false;

        // Set form values from URL parameters
        urlParams.forEach((value, key) => {
            var element = document.querySelector(`[name="${key}"]`);
            if (element && value) {
                element.value = value;
                if (element.classList.contains('select2')) {
                    $(element).val(value).trigger('change');
                }
                hasFilters = true;
            }
        });

        // Apply visual indicators for active filters
        $('#start_date, #end_date, #payment_status, #customer, #category, #part_type, #sales_rep').each(function() {
            var element = $(this);
            if (element.val() && element.val() !== '') {
                element.addClass('filter-active');
            }
        });

        // Note: Filters are already applied by Django view based on URL parameters
        // No need to apply them again via JavaScript

        // Update filter info
        updateFilterInfo();
    });

    // Initialize Charts
    loadChartsData();

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add fade-in animation
    $('.fade-in').each(function(index) {
        $(this).delay(index * 100).animate({opacity: 1}, 500);
    });
});

// Load charts data from server
function loadChartsData() {
    showLoading();

    // Get current filter parameters
    var params = new URLSearchParams(window.location.search);

    $.ajax({
        url: '/reports/sales/chart-data/?' + params.toString(),
        method: 'GET',
        timeout: 10000, // 10 seconds timeout
        cache: false
    })
    .done(function(data) {
        hideLoading();
        initializeCharts(data);
    })
    .fail(function(xhr, status, error) {
        hideLoading();
        console.log('Chart data error:', status, error);
        showNotification('تم تحميل البيانات الافتراضية للرسوم البيانية', 'info');
        // Use fallback data
        initializeCharts({
            monthly_sales: [15000, 18000, 22000, 19000, 25000, 28000, 32000, 29000, 35000, 31000, 38000, 42000],
            monthly_profits: [4500, 5400, 6600, 5700, 7500, 8400, 9600, 8700, 10500, 9300, 11400, 12600],
            monthly_labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            category_labels: ['قطع المحرك', 'الإطارات', 'الكهرباء', 'المكابح', 'الزيوت', 'أخرى'],
            category_values: [35000, 28000, 22000, 18000, 15000, 12000],
            parts_labels: ['فلتر زيت', 'إطار سيارة', 'بطارية', 'مكابح', 'زيت محرك', 'شمعات', 'فلتر هواء', 'حزام', 'مضخة ماء', 'رديتر'],
            parts_values: [156, 142, 128, 115, 98, 87, 76, 65, 54, 43],
            demand_analysis: {
                fast_moving: [{x: 150, y: 25}, {x: 120, y: 30}, {x: 180, y: 20}],
                medium_moving: [{x: 80, y: 45}, {x: 60, y: 50}, {x: 100, y: 40}],
                slow_moving: [{x: 30, y: 80}, {x: 25, y: 90}, {x: 40, y: 75}]
            },
            prediction_data: [45000, 48000, 52000, 49000, 55000, 58000],
            prediction_labels: ['الشهر +1', 'الشهر +2', 'الشهر +3', 'الشهر +4', 'الشهر +5', 'الشهر +6']
        });
    });
}

// Chart initialization with data
function initializeCharts(data) {

    // Sales Trend Chart
    var salesTrendCtx = document.getElementById('salesTrendChart');
    if (salesTrendCtx) {
        new Chart(salesTrendCtx, {
            type: 'line',
            data: {
                labels: data.monthly_labels,
                datasets: [{
                    label: 'المبيعات (د.م)',
                    data: data.monthly_sales,
                    borderColor: 'rgb(37, 99, 235)',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: 'rgb(37, 99, 235)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }, {
                    label: 'الأرباح (د.م)',
                    data: data.monthly_profits,
                    borderColor: 'rgb(5, 150, 105)',
                    backgroundColor: 'rgba(5, 150, 105, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: 'rgb(5, 150, 105)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + ' د.م';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' د.م';
                            }
                        }
                    }
                }
            }
        });
    }

    // Category Sales Chart
    var categorySalesCtx = document.getElementById('categorySalesChart');
    if (categorySalesCtx) {
        new Chart(categorySalesCtx, {
            type: 'doughnut',
            data: {
                labels: data.category_labels,
                datasets: [{
                    data: data.category_values,
                    backgroundColor: [
                        'rgb(37, 99, 235)',
                        'rgb(5, 150, 105)',
                        'rgb(217, 119, 6)',
                        'rgb(8, 145, 178)',
                        'rgb(220, 38, 38)',
                        'rgb(147, 51, 234)'
                    ],
                    borderWidth: 3,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.parsed;
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = ((value / total) * 100).toFixed(1);
                                return label + ': ' + value.toLocaleString() + ' د.م (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    // Top Parts Chart
    var topPartsCtx = document.getElementById('topPartsChart');
    if (topPartsCtx) {
        new Chart(topPartsCtx, {
            type: 'bar',
            data: {
                labels: data.parts_labels,
                datasets: [{
                    label: 'الكمية المباعة',
                    data: data.parts_values,
                    backgroundColor: [
                        'rgba(37, 99, 235, 0.8)',
                        'rgba(5, 150, 105, 0.8)',
                        'rgba(217, 119, 6, 0.8)',
                        'rgba(8, 145, 178, 0.8)',
                        'rgba(220, 38, 38, 0.8)',
                        'rgba(147, 51, 234, 0.8)',
                        'rgba(99, 102, 241, 0.8)',
                        'rgba(236, 72, 153, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 146, 60, 0.8)'
                    ],
                    borderColor: [
                        'rgb(37, 99, 235)',
                        'rgb(5, 150, 105)',
                        'rgb(217, 119, 6)',
                        'rgb(8, 145, 178)',
                        'rgb(220, 38, 38)',
                        'rgb(147, 51, 234)',
                        'rgb(99, 102, 241)',
                        'rgb(236, 72, 153)',
                        'rgb(34, 197, 94)',
                        'rgb(251, 146, 60)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'الكمية: ' + context.parsed.y + ' قطعة';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + ' قطعة';
                            }
                        }
                    }
                }
            }
        });
    }

    // Demand Analysis Chart
    var demandAnalysisCtx = document.getElementById('demandAnalysisChart');
    if (demandAnalysisCtx) {
        new Chart(demandAnalysisCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'سريعة الحركة',
                    data: data.demand_analysis.fast_moving,
                    backgroundColor: 'rgba(5, 150, 105, 0.6)',
                    borderColor: 'rgb(5, 150, 105)',
                    pointRadius: 8
                }, {
                    label: 'متوسطة الحركة',
                    data: data.demand_analysis.medium_moving,
                    backgroundColor: 'rgba(217, 119, 6, 0.6)',
                    borderColor: 'rgb(217, 119, 6)',
                    pointRadius: 8
                }, {
                    label: 'بطيئة الحركة',
                    data: data.demand_analysis.slow_moving,
                    backgroundColor: 'rgba(220, 38, 38, 0.6)',
                    borderColor: 'rgb(220, 38, 38)',
                    pointRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': مبيعات ' + context.parsed.x + ', مخزون ' + context.parsed.y;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'المبيعات الشهرية'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'مستوى المخزون'
                        }
                    }
                }
            }
        });
    }

    // Sales Prediction Chart
    var salesPredictionCtx = document.getElementById('salesPredictionChart');
    if (salesPredictionCtx) {
        var historicalData = data.monthly_sales;
        var predictedData = data.prediction_data;
        var allLabels = [...data.monthly_labels, ...data.prediction_labels];

        new Chart(salesPredictionCtx, {
            type: 'line',
            data: {
                labels: allLabels,
                datasets: [{
                    label: 'المبيعات التاريخية',
                    data: [...historicalData, ...Array(predictedData.length).fill(null)],
                    borderColor: 'rgb(37, 99, 235)',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: 'rgb(37, 99, 235)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }, {
                    label: 'التنبؤ المستقبلي',
                    data: [...Array(historicalData.length).fill(null), ...predictedData],
                    borderColor: 'rgb(220, 38, 38)',
                    backgroundColor: 'rgba(220, 38, 38, 0.1)',
                    borderDash: [5, 5],
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: 'rgb(220, 38, 38)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.parsed.y === null) return '';
                                return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + ' د.م';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' د.م';
                            }
                        }
                    }
                }
            }
        });
    }
}

// Utility Functions
function refreshData() {
    showLoading();

    // Simply reload the page to avoid DataTable column mismatch issues
    location.reload();
}

function resetFilters() {
    showLoading();
    $('#filterForm')[0].reset();
    $('.select2').val(null).trigger('change');

    // Clear URL parameters and reload
    var baseUrl = window.location.pathname;
    window.location.href = baseUrl;
}

function saveFilters() {
    var filters = $('#filterForm').serialize();
    localStorage.setItem('salesReportFilters', filters);

    // Show success message
    showNotification('تم حفظ الفلاتر بنجاح', 'success');
}

// Update filter info display
function updateFilterInfo() {
    var activeFilters = 0;
    var form = $('#filterForm');
    var filterNames = [];

    // Check all form inputs
    form.find('input, select').each(function() {
        var value = $(this).val();
        var name = $(this).attr('name');
        if (value && value !== '' && value !== 'all') {
            activeFilters++;

            // Get filter name for display
            var label = $('label[for="' + $(this).attr('id') + '"]').text().replace(/[^\u0600-\u06FF\s]/g, '').trim();
            if (label) {
                filterNames.push(label);
            }
        }
    });

    // Update the filter count display
    var activeFiltersElement = $('#activeFiltersCount');
    if (activeFiltersElement.length) {
        if (activeFilters > 0) {
            activeFiltersElement.html(activeFilters + ' فلاتر نشطة: ' + filterNames.join(', '));
            activeFiltersElement.removeClass('text-muted').addClass('text-primary');
        } else {
            activeFiltersElement.text('لا توجد فلاتر نشطة');
            activeFiltersElement.removeClass('text-primary').addClass('text-muted');
        }
    }

    return activeFilters;
}

// Simulate data filtering (since we don't have real backend)
function simulateDataFiltering() {
    var startDate = $('#start_date').val();
    var endDate = $('#end_date').val();
    var paymentStatus = $('#payment_status').val();
    var customer = $('#customer').val();
    var category = $('#category').val();
    var partType = $('#part_type').val();
    var salesRep = $('#sales_rep').val();

    // Hide/show table rows based on filters
    $('#salesTable tbody tr').each(function() {
        var row = $(this);
        var show = true;

        // Filter by payment status
        if (paymentStatus && paymentStatus !== '') {
            var rowStatus = row.data('payment-status');
            if (rowStatus !== paymentStatus) {
                show = false;
            }
        }

        // Filter by customer (simulate)
        if (customer && customer !== '') {
            var customerName = row.find('td:nth-child(3) strong').text();
            var selectedCustomerName = $('#customer option:selected').text();
            if (customerName !== selectedCustomerName) {
                show = false;
            }
        }

        // Filter by sales rep (simulate)
        if (salesRep && salesRep !== '') {
            var repName = row.find('td:nth-child(7) span').text();
            var selectedRepName = $('#sales_rep option:selected').text();
            if (repName !== selectedRepName && repName !== 'غير محدد') {
                show = false;
            }
        }

        if (show) {
            row.show().addClass('table-row-visible').removeClass('table-row-hidden');
        } else {
            row.hide().addClass('table-row-hidden').removeClass('table-row-visible');
        }
    });

    // Update DataTable
    if ($.fn.DataTable.isDataTable('#salesTable')) {
        $('#salesTable').DataTable().draw();
    }

    // Update statistics (simulate)
    updateStatistics();
}

function loadSavedFilters() {
    var savedFilters = localStorage.getItem('salesReportFilters');
    if (savedFilters) {
        // Parse and apply saved filters
        var params = new URLSearchParams(savedFilters);
        params.forEach((value, key) => {
            var element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = value;
                if (element.classList.contains('select2')) {
                    $(element).trigger('change');
                }
            }
        });
    }
}

// Update statistics based on visible rows
function updateStatistics() {
    var visibleRows = $('#salesTable tbody tr:visible');
    var totalSales = 0;
    var totalProfit = 0;
    var paidCount = 0;
    var unpaidCount = 0;
    var partialCount = 0;

    visibleRows.each(function() {
        var row = $(this);

        // Extract amount (remove currency and commas)
        var amountText = row.find('td:nth-child(4) strong').text();
        var amount = parseFloat(amountText.replace(/[^\d.]/g, '')) || 0;
        totalSales += amount;

        // Extract profit
        var profitText = row.find('td:nth-child(8) strong').text();
        var profit = parseFloat(profitText.replace(/[^\d.]/g, '')) || 0;
        totalProfit += profit;

        // Count payment statuses
        var status = row.data('payment-status');
        if (status === 'paid') paidCount++;
        else if (status === 'unpaid') unpaidCount++;
        else if (status === 'partial') partialCount++;
    });

    // Update statistics cards
    $('.stats-card:nth-child(1) .stats-value').text(totalSales.toLocaleString() + ' د.م');
    $('.stats-card:nth-child(2) .stats-value').text(totalProfit.toLocaleString() + ' د.م');
    $('.stats-card:nth-child(3) .stats-value').text(visibleRows.length);

    // Update results count with animation
    var resultsCount = $('#resultsCount');
    if (resultsCount.length) {
        resultsCount.addClass('updated');
        resultsCount.text(visibleRows.length);
        setTimeout(function() {
            resultsCount.removeClass('updated');
        }, 500);
    }

    // Update filter section appearance
    var filterSection = $('.filter-section');
    var activeFilters = updateFilterInfo();
    if (activeFilters > 0) {
        filterSection.addClass('has-active-filters');
    } else {
        filterSection.removeClass('has-active-filters');
    }

    // Show notification
    showNotification('تم تحديث البيانات: ' + visibleRows.length + ' نتيجة', 'info');
}

function predictSales() {
    showLoading();

    // Simulate API call for sales prediction
    setTimeout(function() {
        hideLoading();
        showNotification('تم تحديث التنبؤات بناءً على البيانات الحالية', 'info');

        // Scroll to prediction chart
        document.getElementById('salesPredictionChart').scrollIntoView({
            behavior: 'smooth'
        });
    }, 2000);
}

// Export Functions
function exportToPDF() {
    var currentUrl = window.location.href;
    var exportUrl = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'export=pdf';
    window.open(exportUrl, '_blank');
}

function exportToExcel() {
    var currentUrl = window.location.href;
    var exportUrl = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'export=excel';
    window.open(exportUrl, '_blank');
}

function exportToCSV() {
    var currentUrl = window.location.href;
    var exportUrl = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'export=csv';
    window.open(exportUrl, '_blank');
}

function exportTableToPDF() {
    window.print();
}

function exportTableToExcel() {
    // Use DataTables export functionality
    $('#salesTable').DataTable().button('.buttons-excel').trigger();
}

function exportFilteredData() {
    var visibleRows = $('#salesTable tbody tr:visible');
    if (visibleRows.length === 0) {
        showNotification('لا توجد بيانات لتصديرها', 'warning');
        return;
    }

    showLoading();

    // Simulate export process
    setTimeout(function() {
        hideLoading();
        showNotification('تم تصدير ' + visibleRows.length + ' سجل بنجاح', 'success');
    }, 2000);
}

// Sale Management Functions
function viewSaleDetails(saleId) {
    var detailUrl = '/sales/view/' + saleId + '/';
    window.open(detailUrl, '_blank', 'width=1000,height=700');
}

function editSale(saleId) {
    var editUrl = '/sales/edit/' + saleId + '/';
    window.location.href = editUrl;
}

// Invoice Functions
function printInvoice(saleId) {
    var printUrl = '/sales/print_invoice/' + saleId + '/';
    window.open(printUrl, '_blank', 'width=800,height=600');
}

function sendInvoiceEmail(saleId) {
    showLoading();

    // Use the correct URL for email
    $.ajax({
        url: '/sales/email/' + saleId + '/',
        method: 'POST',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        success: function(response) {
            hideLoading();
            showNotification('تم إرسال الفاتورة بالبريد الإلكتروني بنجاح', 'success');
        },
        error: function(xhr, status, error) {
            hideLoading();
            showNotification('حدث خطأ أثناء إرسال الفاتورة', 'error');
        }
    });
}

// UI Helper Functions
function showLoading() {
    if (window.isUpdating) return; // Prevent multiple loading overlays
    window.isUpdating = true;

    if (!document.getElementById('loadingOverlay')) {
        var overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.innerHTML = `
            <div class="d-flex justify-content-center align-items-center h-100">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <div class="mt-2">جاري التحميل...</div>
                </div>
            </div>
        `;
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 9999;
            backdrop-filter: blur(5px);
        `;
        document.body.appendChild(overlay);
    }
}

function hideLoading() {
    window.isUpdating = false;
    var overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.remove();
    }
}

function showNotification(message, type = 'info') {
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = 'fas fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            icon = 'fas fa-exclamation-circle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-triangle';
            break;
    }

    var notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed shadow-lg"
             style="top: 20px; left: 20px; z-index: 9999; min-width: 300px; max-width: 500px;">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.alert('close');
    }, 5000);
}

// Advanced Analytics Functions
function analyzeCustomerBehavior() {
    showLoading();

    // Simulate advanced analytics
    setTimeout(function() {
        hideLoading();
        showNotification('تم تحليل سلوك العملاء وإنشاء التوصيات', 'success');
    }, 3000);
}

function generateInventoryAlerts() {
    var alerts = [
        'فلتر الزيت: مخزون منخفض (أقل من 10 قطع)',
        'إطارات الشتاء: موسم الذروة قادم',
        'بطاريات السيارات: ارتفاع في الطلب'
    ];

    alerts.forEach(function(alert, index) {
        setTimeout(function() {
            showNotification(alert, 'warning');
        }, index * 1000);
    });
}

// Load saved filters on page load
$(document).ready(function() {
    loadSavedFilters();

    // Initialize filter toggle functionality
    initializeFilterToggle();

    // Fix navbar dropdowns
    initializeNavbarDropdowns();

    // Auto-refresh disabled to prevent page flickering
    // Uncomment below if you want auto-refresh every 5 minutes
    /*
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            // Only refresh if page is visible
            $.get(window.location.href + '&ajax=1')
                .done(function(data) {
                    // Update statistics without full page reload
                    console.log('Data refreshed');
                });
        }
    }, 300000); // 5 minutes
    */
});

// Initialize filter toggle functionality
function initializeFilterToggle() {
    $(document).ready(function() {
        $('.filter-toggle').click(function() {
            const $filterCollapse = $('#filterCollapse');
            const $icon = $(this).find('i');
            const isVisible = $filterCollapse.is(':visible');

            if (isVisible) {
                // Hide filters
                $filterCollapse.slideUp(300);
                $(this).attr('aria-expanded', 'false');
                $icon.css('transform', 'rotate(0deg)');
            } else {
                // Show filters
                $filterCollapse.slideDown(300);
                $(this).attr('aria-expanded', 'true');
                $icon.css('transform', 'rotate(180deg)');
            }
        });

        // Set initial state
        const $filterCollapse = $('#filterCollapse');
        const $icon = $('.filter-toggle').find('i');
        if ($filterCollapse.hasClass('show')) {
            $icon.css('transform', 'rotate(180deg)');
        }
    });
}

// Fix navbar dropdowns
function initializeNavbarDropdowns() {
    // Re-initialize all Bootstrap dropdowns
    const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
    const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Ensure dropdowns work properly
    $('.dropdown-toggle').off('click.bs.dropdown').on('click', function(e) {
        e.preventDefault();
        const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
        dropdown.toggle();
    });
}
</script>
{% endblock %}
