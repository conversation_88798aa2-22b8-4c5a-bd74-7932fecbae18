{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تقرير المبيعات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daterangepicker.css' %}">
<style>
    /* RTL Support */
    html[dir="rtl"] {
        text-align: right;
    }

    html[dir="rtl"] .dropdown-menu {
        right: 0 !important;
        left: auto !important;
        text-align: right;
    }

    html[dir="rtl"] .input-group-text {
        border-radius: 0 25px 25px 0;
        border-left: none;
    }

    html[dir="rtl"] .form-control {
        border-radius: 25px 0 0 25px;
    }

    html[dir="rtl"] .float-end {
        float: left !important;
    }

    html[dir="rtl"] .float-start {
        float: right !important;
    }

    html[dir="rtl"] .me-2, html[dir="rtl"] .ms-2 {
        margin-right: 0;
        margin-left: 0.5rem;
    }

    html[dir="rtl"] .ms-2, html[dir="rtl"] .me-2 {
        margin-left: 0;
        margin-right: 0.5rem;
    }

    html[dir="rtl"] .d-flex.justify-content-between {
        flex-direction: row-reverse;
        justify-content: space-between;
    }

    html[dir="rtl"] .dropdown-menu-right {
        right: auto !important;
        left: 0 !important;
    }

    html[dir="rtl"] .border-right-dark {
        border-right: none !important;
        border-left: 0.25rem solid #5a5c69 !important;
    }

    html[dir="rtl"] .border-right-secondary {
        border-right: none !important;
        border-left: 0.25rem solid #858796 !important;
    }

    html[dir="rtl"] .border-right-purple {
        border-right: none !important;
        border-left: 0.25rem solid #6f42c1 !important;
    }

    html[dir="rtl"] .border-right-orange {
        border-right: none !important;
        border-left: 0.25rem solid #fd7e14 !important;
    }

    html[dir="rtl"] .categories-list .badge {
        margin-right: 0;
        margin-left: 2px;
    }

    html[dir="rtl"] .products-list .product-item {
        text-align: right;
    }

    /* Enhanced Dashboard Cards */
    .stats-card {
        background: linear-gradient(135deg, #fff 0%, #f8f9fc 100%);
        border-radius: 20px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-light) 100%);
    }

    .stats-card-primary {
        --card-color: #4e73df;
        --card-color-light: #6f8ef7;
    }

    .stats-card-success {
        --card-color: #1cc88a;
        --card-color-light: #36d9a3;
    }

    .stats-card-info {
        --card-color: #36b9cc;
        --card-color-light: #6ecdd7;
    }

    .stats-card-warning {
        --card-color: #f6c23e;
        --card-color-light: #f8d866;
    }

    .stats-card-body {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: linear-gradient(135deg, var(--card-color) 0%, var(--card-color-light) 100%);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .stats-content {
        flex: 1;
        margin-right: 1rem;
    }

    .stats-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stats-subtitle {
        font-size: 0.75rem;
        color: #95a5a6;
        font-weight: 500;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 2rem;
    }

    .filter-section {
        background: #f8f9fc;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e3e6f0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .search-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .products-list .product-item {
        font-size: 0.85rem;
        line-height: 1.2;
    }

    .categories-list .badge {
        font-size: 0.7rem;
        margin-right: 2px;
    }

    .sale-row:hover {
        background-color: #f8f9fa;
    }

    .btn-group .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 25px;
        margin: 0.25rem;
        transition: all 0.3s ease;
    }

    .btn-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .table thead th {
        background: linear-gradient(135deg, #4e73df 0%, #6f8ef7 100%);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
    }

    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fc;
        transform: scale(1.01);
    }

    .enhanced-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .enhanced-table .table {
        margin-bottom: 0;
    }

    .enhanced-table .table thead th {
        background: linear-gradient(135deg, #4e73df 0%, #6f8ef7 100%);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
    }

    .enhanced-table .table tbody tr {
        transition: all 0.2s ease;
    }

    .enhanced-table .table tbody tr:hover {
        background-color: #f8f9fc;
        transform: scale(1.01);
    }

    .section-title {
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(90deg, #4e73df, #6f8ef7);
        border-radius: 2px;
    }

    .page-header {
        background: linear-gradient(135deg, #4e73df 0%, #6f8ef7 100%);
        color: white;
        padding: 30px 0;
        border-radius: 0 0 15px 15px;
        margin-bottom: 30px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4e73df 0%, #6f8ef7 100%);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #3a5bc7 0%, #5a7fe6 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .quick-filter {
        background: white;
        border: 2px solid #e3e6f0;
        border-radius: 25px;
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .quick-filter:hover, .quick-filter.active {
        background: var(--bs-primary);
        color: white;
        border-color: var(--bs-primary);
    }

    @media (max-width: 768px) {
        .filter-section {
            padding: 1rem;
        }

        .table-responsive {
            font-size: 0.8rem;
        }

        .btn-group {
            flex-direction: column;
        }

        .btn-group .btn {
            margin-bottom: 2px;
        }

        .stats-card-body {
            flex-direction: column;
            text-align: center;
        }

        .stats-content {
            margin-right: 0;
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}

    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1">
                        <i class="fas fa-chart-line me-2"></i>{% trans "تقرير المبيعات" %}
                    </h1>
                    <p class="mb-0 opacity-75">{% trans "مراقبة أداء مبيعاتك وتحليلها" %}</p>
                </div>
                <div class="d-flex align-items-center">
                    <a href="{% url 'reports:index' %}" class="btn btn-light me-3">
                        <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-download me-1"></i> {% trans "تصدير" %}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>PDF
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                <i class="fas fa-file-excel me-2"></i>Excel
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                                <i class="fas fa-file-csv me-2"></i>CSV
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid px-4">
        <!-- Search Section -->
        <div class="search-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="globalSearch"
                               placeholder="{% trans 'البحث في المبيعات (رقم الفاتورة، العميل، المنتجات...)' %}">
                        <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-info" type="button" data-bs-toggle="collapse"
                            data-bs-target="#filterSection" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i>{% trans "فلاتر متقدمة" %}
                    </button>
                </div>
            </div>
        </div>

    <!-- Advanced Filters Section -->
    <div class="collapse" id="filterSection">
        <div class="filter-section">
            <form id="filterForm" method="GET">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="daterange" class="form-label">
                            <i class="fas fa-calendar me-1"></i>{% trans "نطاق التاريخ" %}
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="daterange" name="daterange" value="{{ start_date|date:'Y-m-d' }} - {{ end_date|date:'Y-m-d' }}">
                            <button class="btn btn-outline-secondary" type="button" id="daterange-btn">
                                <i class="fas fa-calendar"></i>
                            </button>
                        </div>
                        <input type="hidden" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                        <input type="hidden" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="category" class="form-label">
                            <i class="fas fa-tags me-1"></i>{% trans "فئة المنتج" %}
                        </label>
                        <select class="form-select" id="category" name="category">
                            <option value="">{% trans "جميع الفئات" %}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if selected_category == category.id %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="product" class="form-label">
                            <i class="fas fa-box me-1"></i>{% trans "المنتج" %}
                        </label>
                        <select class="form-select" id="product" name="product">
                            <option value="">{% trans "جميع المنتجات" %}</option>
                            {% for product in all_products %}
                            <option value="{{ product.id }}" {% if selected_product == product.id %}selected{% endif %}>{{ product.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="customer" class="form-label">
                            <i class="fas fa-user me-1"></i>{% trans "العميل" %}
                        </label>
                        <select class="form-select" id="customer" name="customer">
                            <option value="">{% trans "جميع العملاء" %}</option>
                            {% for customer in all_customers %}
                            <option value="{{ customer.id }}" {% if selected_customer == customer.id %}selected{% endif %}>{{ customer.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="payment_status" class="form-label">
                            <i class="fas fa-credit-card me-1"></i>{% trans "حالة الدفع" %}
                        </label>
                        <select class="form-select" id="payment_status" name="payment_status">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="paid" {% if selected_payment_status == 'paid' %}selected{% endif %}>
                                ✓ {% trans "مدفوع" %}
                            </option>
                            <option value="unpaid" {% if selected_payment_status == 'unpaid' %}selected{% endif %}>
                                ✗ {% trans "غير مدفوع" %}
                            </option>
                        </select>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="payment_method" class="form-label">
                            <i class="fas fa-money-bill me-1"></i>{% trans "طريقة الدفع" %}
                        </label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="">{% trans "جميع طرق الدفع" %}</option>
                            <option value="cash" {% if selected_payment_method == 'cash' %}selected{% endif %}>
                                💵 {% trans "نقدي" %}
                            </option>
                            <option value="card" {% if selected_payment_method == 'card' %}selected{% endif %}>
                                💳 {% trans "بطاقة ائتمان" %}
                            </option>
                            <option value="transfer" {% if selected_payment_method == 'transfer' %}selected{% endif %}>
                                🏦 {% trans "تحويل بنكي" %}
                            </option>
                            <option value="check" {% if selected_payment_method == 'check' %}selected{% endif %}>
                                📄 {% trans "شيك" %}
                            </option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="min_amount" class="form-label">
                            <i class="fas fa-sort-numeric-up me-1"></i>{% trans "الحد الأدنى للمبلغ" %}
                        </label>
                        <input type="number" class="form-control" id="min_amount" name="min_amount"
                               value="{{ min_amount }}" placeholder="0" min="0" step="0.01">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="max_amount" class="form-label">
                            <i class="fas fa-sort-numeric-down me-1"></i>{% trans "الحد الأعلى للمبلغ" %}
                        </label>
                        <input type="number" class="form-control" id="max_amount" name="max_amount"
                               value="{{ max_amount }}" placeholder="10000" min="0" step="0.01">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="sort_by" class="form-label">
                            <i class="fas fa-sort me-1"></i>{% trans "ترتيب حسب" %}
                        </label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="date" {% if selected_sort == 'date' %}selected{% endif %}>{% trans "التاريخ" %}</option>
                            <option value="amount" {% if selected_sort == 'amount' %}selected{% endif %}>{% trans "المبلغ" %}</option>
                            <option value="customer" {% if selected_sort == 'customer' %}selected{% endif %}>{% trans "العميل" %}</option>
                            <option value="invoice_number" {% if selected_sort == 'invoice_number' %}selected{% endif %}>{% trans "رقم الفاتورة" %}</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>{% trans "تطبيق الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                    <i class="fas fa-undo me-1"></i>{% trans "إعادة تعيين" %}
                                </button>
                            </div>
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "عدد النتائج" %}: <span id="resultsCount">{{ sales.count }}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>



    <!-- Sales Summary Cards -->
    <div class="row mb-5">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-primary h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "إجمالي المبيعات" %}</div>
                        <div class="stats-number">{{ total_sales }}</div>
                        <div class="stats-subtitle">
                            {% if sales_trend > 0 %}
                                <span class="text-success"><i class="fas fa-arrow-up"></i> {{ sales_trend_percent|floatformat:1 }}%</span>
                            {% elif sales_trend < 0 %}
                                <span class="text-danger"><i class="fas fa-arrow-down"></i> {{ sales_trend_percent|floatformat:1 }}%</span>
                            {% else %}
                                <span class="text-muted"><i class="fas fa-equals"></i> 0%</span>
                            {% endif %}
                            <span class="text-muted">{% trans "مقارنة بالفترة السابقة" %}</span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-success h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "إجمالي المبلغ" %}</div>
                        <div class="stats-number">{{ total_amount|floatformat:2 }} {% trans "د.م." %}</div>
                        <div class="stats-subtitle">
                            {% if amount_trend > 0 %}
                                <span class="text-success"><i class="fas fa-arrow-up"></i> {{ amount_trend_percent|floatformat:1 }}%</span>
                            {% elif amount_trend < 0 %}
                                <span class="text-danger"><i class="fas fa-arrow-down"></i> {{ amount_trend_percent|floatformat:1 }}%</span>
                            {% else %}
                                <span class="text-muted"><i class="fas fa-equals"></i> 0%</span>
                            {% endif %}
                            <span class="text-muted">{% trans "مقارنة بالفترة السابقة" %}</span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-info h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "متوسط قيمة البيع" %}</div>
                        <div class="stats-number">
                            {% if total_sales > 0 %}
                                {{ total_amount|floatformat:2 }} {% trans "د.م." %}
                            {% else %}
                                0 {% trans "د.م." %}
                            {% endif %}
                        </div>
                        <div class="stats-subtitle">
                            <span class="text-primary">
                                <i class="fas fa-calculator me-1"></i>{% trans "متوسط لكل عملية بيع" %}
                            </span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-warning h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "إجمالي الأرباح" %}</div>
                        <div class="stats-number">{{ total_profit|floatformat:2 }} {% trans "د.م." %}</div>
                        <div class="stats-subtitle">
                            {% if profit_margin > 0 %}
                                <span class="text-success">
                                    <i class="fas fa-percentage me-1"></i>{{ profit_margin|floatformat:1 }}% هامش ربح
                                </span>
                            {% else %}
                                <span class="text-muted">
                                    <i class="fas fa-equals me-1"></i>لا يوجد ربح
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="row mb-5">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-primary h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "عدد الفواتير" %}</div>
                        <div class="stats-number">{{ total_invoices }}</div>
                        <div class="stats-subtitle">
                            <span class="text-info">
                                <i class="fas fa-file-invoice me-1"></i>{% trans "فاتورة" %}
                            </span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-success h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "عدد المنتجات المباعة" %}</div>
                        <div class="stats-number">{{ total_products_sold }}</div>
                        <div class="stats-subtitle">
                            <span class="text-primary">
                                <i class="fas fa-box me-1"></i>{% trans "قطعة" %}
                            </span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-box"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-info h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "المبيعات المدفوعة" %}</div>
                        <div class="stats-number">{{ paid_sales_amount|floatformat:2 }} {% trans "د.م." %}</div>
                        <div class="stats-subtitle">
                            <span class="text-success">
                                <i class="fas fa-check-circle me-1"></i>{{ paid_sales_count }} {% trans "فاتورة مدفوعة" %}
                            </span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-warning h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "المبيعات غير المدفوعة" %}</div>
                        <div class="stats-number">{{ unpaid_sales_amount|floatformat:2 }} {% trans "د.م." %}</div>
                        <div class="stats-subtitle">
                            <span class="text-danger">
                                <i class="fas fa-times-circle me-1"></i>{{ unpaid_sales_count }} {% trans "فاتورة غير مدفوعة" %}
                            </span>
                        </div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مقارنة الفترات الزمنية -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "مقارنة الفترات الزمنية" %}</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3" id="compare-form">
                <div class="col-md-5">
                    <label class="form-label">{% trans "الفترة الأولى" %}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="period1-range" name="period1_range">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-calendar"></i>
                        </button>
                    </div>
                    <input type="hidden" id="period1_start" name="period1_start">
                    <input type="hidden" id="period1_end" name="period1_end">
                </div>
                <div class="col-md-5">
                    <label class="form-label">{% trans "الفترة الثانية" %}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="period2-range" name="period2_range">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-calendar"></i>
                        </button>
                    </div>
                    <input type="hidden" id="period2_start" name="period2_start">
                    <input type="hidden" id="period2_end" name="period2_end">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" id="compare-periods" class="btn btn-primary w-100">
                        <i class="fas fa-chart-line me-1"></i> {% trans "مقارنة" %}
                    </button>
                </div>
            </form>
            
            <div class="mt-4" id="comparison-results" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{% trans "المؤشر" %}</th>
                                <th>{% trans "الفترة الأولى" %}</th>
                                <th>{% trans "الفترة الثانية" %}</th>
                                <th>{% trans "التغيير" %}</th>
                                <th>{% trans "نسبة التغيير" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{% trans "عدد المبيعات" %}</td>
                                <td id="period1-sales-count">0</td>
                                <td id="period2-sales-count">0</td>
                                <td id="sales-count-change">0</td>
                                <td id="sales-count-change-percent">0%</td>
                            </tr>
                            <tr>
                                <td>{% trans "إجمالي المبيعات" %}</td>
                                <td id="period1-sales-total">0 {% trans "د.م." %}</td>
                                <td id="period2-sales-total">0 {% trans "د.م." %}</td>
                                <td id="sales-total-change">0 {% trans "د.م." %}</td>
                                <td id="sales-total-change-percent">0%</td>
                            </tr>
                            <tr>
                                <td>{% trans "متوسط قيمة البيع" %}</td>
                                <td id="period1-avg-sale">0 {% trans "د.م." %}</td>
                                <td id="period2-avg-sale">0 {% trans "د.م." %}</td>
                                <td id="avg-sale-change">0 {% trans "د.م." %}</td>
                                <td id="avg-sale-change-percent">0%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="chart-container mt-4">
                    <canvas id="comparisonChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم التنبيهات والإشعارات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "التنبيهات والإشعارات" %}</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="alertsDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="alertsDropdown">
                    <div class="dropdown-header">{% trans "خيارات التنبيهات" %}:</div>
                    <a class="dropdown-item" href="#" id="configure-alerts">
                        <i class="fas fa-cog fa-sm fa-fw me-2 text-gray-400"></i>
                        {% trans "تكوين التنبيهات" %}
                    </a>
                    <a class="dropdown-item" href="#" id="email-report">
                        <i class="fas fa-envelope fa-sm fa-fw me-2 text-gray-400"></i>
                        {% trans "إرسال تقرير بالبريد الإلكتروني" %}
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="#" id="clear-alerts">
                        <i class="fas fa-trash-alt fa-sm fa-fw me-2 text-gray-400"></i>
                        {% trans "مسح جميع التنبيهات" %}
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="alerts-container">
                <!-- سيتم إضافة التنبيهات هنا ديناميكيًا -->
                {% if sales_alerts %}
                    {% for alert in sales_alerts %}
                    <div class="alert {% if alert.severity == 'high' %}alert-danger{% elif alert.severity == 'medium' %}alert-warning{% else %}alert-info{% endif %} alert-dismissible fade show" role="alert">
                        <strong>{{ alert.title }}</strong>: {{ alert.message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-bell-slash fa-3x text-gray-300 mb-3"></i>
                    <p class="text-muted">{% trans "لا توجد تنبيهات حاليًا" %}</p>
                </div>
                {% endif %}
            </div>
            
            <!-- نموذج تكوين التنبيهات -->
            <div class="modal fade" id="alertConfigModal" tabindex="-1" aria-labelledby="alertConfigModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="alertConfigModalLabel">{% trans "تكوين التنبيهات" %}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="alert-config-form">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "تنبيه عند انخفاض المبيعات بنسبة" %}</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="sales-drop-threshold" value="10" min="1" max="100">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable-email-alerts" checked>
                                        <label class="form-check-label" for="enable-email-alerts">{% trans "إرسال التنبيهات عبر البريد الإلكتروني" %}</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">{% trans "إرسال تقرير دوري" %}</label>
                                    <select class="form-select" id="report-frequency">
                                        <option value="never">{% trans "لا ترسل" %}</option>
                                        <option value="daily">{% trans "يوميًا" %}</option>
                                        <option value="weekly">{% trans "أسبوعيًا" %}</option>
                                        <option value="monthly" selected>{% trans "شهريًا" %}</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">{% trans "البريد الإلكتروني للمستلم" %}</label>
                                    <input type="email" class="form-control" id="recipient-email" placeholder="<EMAIL>">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                            <button type="button" class="btn btn-primary" id="save-alert-config">{% trans "حفظ التغييرات" %}</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نموذج إرسال تقرير بالبريد الإلكتروني -->
            <div class="modal fade" id="emailReportModal" tabindex="-1" aria-labelledby="emailReportModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="emailReportModalLabel">{% trans "إرسال تقرير بالبريد الإلكتروني" %}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="email-report-form">
                                <div class="mb-3">
                                    <label class="form-label">{% trans "البريد الإلكتروني للمستلم" %}</label>
                                    <input type="email" class="form-control" id="email-recipient" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">{% trans "عنوان التقرير" %}</label>
                                    <input type="text" class="form-control" id="email-subject" value="{% trans 'تقرير المبيعات' %}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">{% trans "تضمين" %}</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="include-charts" checked>
                                        <label class="form-check-label" for="include-charts">{% trans "الرسوم البيانية" %}</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="include-tables" checked>
                                        <label class="form-check-label" for="include-tables">{% trans "جداول البيانات" %}</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="include-summary" checked>
                                        <label class="form-check-label" for="include-summary">{% trans "ملخص المبيعات" %}</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">{% trans "تنسيق التقرير" %}</label>
                                    <select class="form-select" id="report-format">
                                        <option value="pdf">PDF</option>
                                        <option value="excel">Excel</option>
                                        <option value="both">{% trans "كلاهما" %}</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                            <button type="button" class="btn btn-primary" id="send-email-report">{% trans "إرسال التقرير" %}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sales Chart -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "المبيعات حسب اليوم" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Products -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "أفضل المنتجات مبيعًا" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{% trans "المنتج" %}</th>
                                    <th>{% trans "الكمية" %}</th>
                                    <th>{% trans "المبلغ" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                <tr>
                                    <td>{{ product.product__name }}</td>
                                    <td>{{ product.total_quantity }}</td>
                                    <td>{{ product.total_sales|floatformat:2 }} {% trans "د.م." %}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">{% trans "لا توجد بيانات" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الرسوم البيانية الإضافية -->
    <div class="row">
        <!-- Sales by Category Chart -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "المبيعات حسب الفئة" %}</h6>
                </div>
                <div class="card-body">
                    <div class="chart-bar">
                        <canvas id="categorySalesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- رسم بياني دائري لتوزيع المبيعات -->
<div class="col-lg-6">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "توزيع المبيعات" %}</h6>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-primary distribution-toggle active" data-type="category">{% trans "حسب الفئة" %}</button>
                <button type="button" class="btn btn-sm btn-primary distribution-toggle" data-type="customer">{% trans "حسب العميل" %}</button>
                <button type="button" class="btn btn-sm btn-primary distribution-toggle" data-type="payment">{% trans "حسب طريقة الدفع" %}</button>
            </div>
        </div>
        <div class="card-body">
            <div class="chart-container">
                <canvas id="salesDistributionChart"></canvas>
            </div>
            <div class="mt-4 text-center small">
                <span class="me-2">
                    <i class="fas fa-circle text-primary"></i> {% trans "حسب الفئات" %}
                </span>
                <span class="me-2">
                    <i class="fas fa-circle text-success"></i> {% trans "حسب العملاء" %}
                </span>
                <span class="me-2">
                    <i class="fas fa-circle text-info"></i> {% trans "حسب طرق الدفع" %}
                </span>
            </div>
        </div>
    </div>
</div>
    </div>

    <!-- Sales Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة المبيعات" %}</h6>
            <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                    <div class="dropdown-header">{% trans "خيارات العرض" %}:</div>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input toggle-column" type="checkbox" value="product" id="show-product" checked>
                            <label class="form-check-label" for="show-product">{% trans "المنتج" %}</label>
                        </div>
                    </div>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input toggle-column" type="checkbox" value="part_number" id="show-part-number" checked>
                            <label class="form-check-label" for="show-part-number">{% trans "رقم الجزء" %}</label>
                        </div>
                    </div>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input toggle-column" type="checkbox" value="category" id="show-category" checked>
                            <label class="form-check-label" for="show-category">{% trans "الفئة" %}</label>
                        </div>
                    </div>
                    <div class="dropdown-item">
                        <div class="form-check">
                            <input class="form-check-input toggle-column" type="checkbox" value="customer" id="show-customer" checked>
                            <label class="form-check-label" for="show-customer">{% trans "العميل" %}</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- فلاتر إضافية -->
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text">{% trans "نطاق السعر" %}</span>
                        <input type="number" class="form-control" id="price-min" placeholder="{% trans "الحد الأدنى" %}">
                        <input type="number" class="form-control" id="price-max" placeholder="{% trans "الحد الأقصى" %}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text">{% trans "بحث" %}</span>
                        <input type="text" class="form-control" id="table-search" placeholder="{% trans "اسم المنتج، رقم الجزء، العميل..." %}">
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="salesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <th>{% trans "تاريخ البيع" %}</th>
                            <th>{% trans "اسم العميل" %}</th>
                            <th>{% trans "إجمالي المبلغ" %}</th>
                            <th>{% trans "حالة الدفع" %}</th>
                            <th>{% trans "طريقة الدفع" %}</th>
                            <th>{% trans "اسم المنتج" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "الكمية المباعة" %}</th>
                            <th>{% trans "سعر الوحدة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales %}
                            <tr class="sale-row" data-sale-id="{{ sale.id }}">
                                <td>
                                    <strong>{{ sale.invoice_number }}</strong>
                                    <br><small class="text-muted">#{{ sale.id }}</small>
                                </td>
                                <td>{{ sale.date|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <strong>{{ sale.customer.name }}</strong>
                                    {% if sale.customer.phone %}
                                        <br><small class="text-muted">{{ sale.customer.phone }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ sale.total_amount|floatformat:2 }} د.م</strong>
                                    {% if sale.discount_amount > 0 %}
                                        <br><small class="text-success">خصم: {{ sale.discount_amount|floatformat:2 }} د.م</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if sale.is_paid %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>مدفوع
                                        </span>
                                        <br><small class="text-success">{{ sale.paid_amount|floatformat:2 }} د.م</small>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>غير مدفوع
                                        </span>
                                        {% if sale.paid_amount > 0 %}
                                            <br><small class="text-warning">مدفوع جزئياً: {{ sale.paid_amount|floatformat:2 }} د.م</small>
                                            <br><small class="text-danger">متبقي: {{ sale.remaining_amount|floatformat:2 }} د.م</small>
                                        {% else %}
                                            <br><small class="text-muted">لم يتم الدفع</small>
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if sale.payment_method == 'cash' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-money-bill me-1"></i>نقدي
                                        </span>
                                    {% elif sale.payment_method == 'card' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-credit-card me-1"></i>بطاقة
                                        </span>
                                    {% elif sale.payment_method == 'transfer' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-university me-1"></i>تحويل
                                        </span>
                                    {% elif sale.payment_method == 'check' %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-file-invoice me-1"></i>شيك
                                        </span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="products-list">
                                        {% for item in sale.items.all %}
                                            <div class="product-item mb-1">
                                                <strong>{{ item.product.name }}</strong>
                                                <small class="text-muted">({{ item.quantity }} × {{ item.unit_price|floatformat:2 }})</small>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </td>
                                <td>
                                    <div class="categories-list">
                                        {% for item in sale.items.all %}
                                            <span class="badge bg-light text-dark mb-1">{{ item.product.category.name }}</span>
                                        {% endfor %}
                                    </div>
                                </td>
                                <td>
                                    <strong>{{ sale.items.all|length }}</strong> منتج
                                    <br><small class="text-muted">
                                        {% with total_qty=sale.items.all|length %}
                                            {{ total_qty }} قطعة
                                        {% endwith %}
                                    </small>
                                </td>
                                <td>
                                    {% with avg_price=sale.total_amount|floatformat:2 %}
                                        {{ avg_price }} د.م
                                    {% endwith %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'sales:sale_detail' sale.id %}" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'sales:print_invoice' sale.id %}" class="btn btn-sm btn-outline-success" title="طباعة الفاتورة" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        {% if not sale.is_paid %}
                                            <button class="btn btn-sm btn-outline-warning mark-paid-btn" data-sale-id="{{ sale.id }}" title="تسجيل كمدفوع">
                                                <i class="fas fa-dollar-sign"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% empty %}
                        <tr>
                            <td colspan="11" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{% trans "لا توجد مبيعات في الفترة المحددة" %}</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/moment.min.js' %}"></script>
<script src="{% static 'js/daterangepicker.js' %}"></script>
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تسجيل الفاتورة كمدفوعة
        $('.mark-paid-btn').on('click', function() {
            const saleId = $(this).data('sale-id');
            const button = $(this);

            if (confirm('هل أنت متأكد من تسجيل هذه الفاتورة كمدفوعة؟')) {
                $.ajax({
                    url: '{% url "sales:mark_as_paid" 0 %}'.replace('0', saleId),
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // تحديث حالة الدفع في الجدول
                            const row = button.closest('tr');
                            row.find('.badge.bg-danger').removeClass('bg-danger').addClass('bg-success')
                               .html('<i class="fas fa-check-circle me-1"></i>مدفوع');
                            button.remove();

                            // إظهار رسالة نجاح
                            showAlert('success', 'تم تسجيل الفاتورة كمدفوعة بنجاح');
                        } else {
                            showAlert('error', response.message || 'حدث خطأ أثناء تحديث حالة الدفع');
                        }
                    },
                    error: function() {
                        showAlert('error', 'حدث خطأ في الاتصال بالخادم');
                    }
                });
            }
        });

        // دالة لإظهار التنبيهات
        function showAlert(type, message) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container-fluid').prepend(alertHtml);

            // إزالة التنبيه تلقائياً بعد 5 ثوانٍ
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }

        // Initialize DataTable with Advanced Features
    var salesTable = $('#salesTable').DataTable({
        "language": {
            "url": "{% static 'js/dataTables.arabic.json' %}"
        },
        "order": [[1, "desc"]],
        "responsive": true,
        "dom": '<"d-flex justify-content-between align-items-center mb-3"<"d-flex align-items-center"l><"d-flex"f>>t<"d-flex justify-content-between align-items-center mt-3"<"d-flex align-items-center"i><"d-flex"p>>',
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]]
    });
    
    // تفعيل البحث المتقدم
    $('#table-search').on('keyup', function() {
        salesTable.search(this.value).draw();
    });
    
    // تفعيل فلتر نطاق السعر
    $.fn.dataTable.ext.search.push(
        function(settings, data, dataIndex) {
            var min = parseFloat($('#price-min').val()) || 0;
            var max = parseFloat($('#price-max').val()) || Infinity;
            var price = parseFloat(data[7]) || 0; // عمود الإجمالي
            
            if ((min <= price && price <= max)) {
                return true;
            }
            return false;
        }
    );
    
    $('#price-min, #price-max').on('change', function() {
        salesTable.draw();
    });
    
    // تفعيل تخصيص الأعمدة
    $('.toggle-column').on('change', function() {
        var column = salesTable.column($(this).data('column'));
        column.visible($(this).is(':checked'));
    });
    
    // ربط الخانات بأرقام الأعمدة
    $('#show-product').data('column', 2);
    $('#show-part-number').data('column', 3);
    $('#show-category').data('column', 4);
    $('#show-customer').data('column', 8);
    
    // تفعيل التمرير الأفقي للجداول على الشاشات الصغيرة
    $(window).resize(function() {
        if ($(window).width() < 768) {
            $('.table-responsive').css('overflow-x', 'auto');
        }
    });

        // Initialize Date Range Picker
        $('#daterange').daterangepicker({
            opens: 'left',
            locale: {
                format: 'YYYY-MM-DD',
                applyLabel: '{% trans "تطبيق" %}',
                cancelLabel: '{% trans "إلغاء" %}',
                fromLabel: '{% trans "من" %}',
                toLabel: '{% trans "إلى" %}',
                customRangeLabel: '{% trans "مخصص" %}',
                daysOfWeek: ['{% trans "أحد" %}', '{% trans "إثنين" %}', '{% trans "ثلاثاء" %}', '{% trans "أربعاء" %}', '{% trans "خميس" %}', '{% trans "جمعة" %}', '{% trans "سبت" %}'],
                monthNames: ['{% trans "يناير" %}', '{% trans "فبراير" %}', '{% trans "مارس" %}', '{% trans "أبريل" %}', '{% trans "مايو" %}', '{% trans "يونيو" %}', '{% trans "يوليو" %}', '{% trans "أغسطس" %}', '{% trans "سبتمبر" %}', '{% trans "أكتوبر" %}', '{% trans "نوفمبر" %}', '{% trans "ديسمبر" %}'],
                firstDay: 0
            }
        }, function(start, end, label) {
            $('#start_date').val(start.format('YYYY-MM-DD'));
            $('#end_date').val(end.format('YYYY-MM-DD'));
        });

        // Sales Chart - المبيعات حسب اليوم
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [
                    {% for item in sales_by_day %}
                        '{{ item.date__date|date:"Y-m-d" }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "المبيعات" %}',
                    data: [
                        {% for item in sales_by_day %}
                            {{ item.total }},
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointBorderColor: '#fff',
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointHoverBorderColor: '#fff',
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        left: 10,
                        right: 25,
                        top: 25,
                        bottom: 0
                    }
                },
                scales: {
                    x: {
                        time: {
                            unit: 'day'
                        },
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            maxTicksLimit: 7
                        }
                    },
                    y: {
                        ticks: {
                            maxTicksLimit: 5,
                            padding: 10,
                            callback: function(value, index, values) {
                                return value + ' {% trans "د.م." %}';
                            }
                        },
                        grid: {
                            color: "rgb(234, 236, 244)",
                            zeroLineColor: "rgb(234, 236, 244)",
                            drawBorder: false,
                            borderDash: [2],
                            zeroLineBorderDash: [2]
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        titleMarginBottom: 10,
                        titleColor: '#6e707e',
                        titleFontSize: 14,
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        intersect: false,
                        mode: 'index',
                        caretPadding: 10,
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
        
        // Category Sales Chart - المبيعات حسب الفئة (رسم بياني شريطي)
        const categoryCtx = document.getElementById('categorySalesChart').getContext('2d');
        const categoryColors = [
            'rgba(78, 115, 223, 0.8)',
            'rgba(28, 200, 138, 0.8)',
            'rgba(54, 185, 204, 0.8)',
            'rgba(246, 194, 62, 0.8)',
            'rgba(231, 74, 59, 0.8)',
            'rgba(133, 135, 150, 0.8)',
            'rgba(105, 153, 93, 0.8)',
            'rgba(214, 69, 155, 0.8)',
            'rgba(255, 159, 64, 0.8)',
            'rgba(201, 203, 207, 0.8)'
        ];
        
        const categorySalesChart = new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for category in category_sales %}
                        '{{ category.category__name }}',
                    {% empty %}
                        '{% trans "لا توجد بيانات" %}'
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "المبيعات" %}',
                    data: [
                        {% for category in category_sales %}
                            {{ category.total_sales }},
                        {% empty %}
                            0
                        {% endfor %}
                    ],
                    backgroundColor: categoryColors,
                    borderColor: categoryColors.map(color => color.replace('0.8', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        left: 10,
                        right: 25,
                        top: 25,
                        bottom: 0
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        }
                    },
                    y: {
                        ticks: {
                            maxTicksLimit: 5,
                            padding: 10,
                            callback: function(value, index, values) {
                                return value + ' {% trans "د.م." %}';
                            }
                        },
                        grid: {
                            color: "rgb(234, 236, 244)",
                            zeroLineColor: "rgb(234, 236, 244)",
                            drawBorder: false,
                            borderDash: [2],
                            zeroLineBorderDash: [2]
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        titleMarginBottom: 10,
                        titleColor: '#6e707e',
                        titleFontSize: 14,
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
        
        // Sales Distribution Chart - توزيع المبيعات (رسم بياني دائري)
        const distributionCtx = document.getElementById('salesDistributionChart').getContext('2d');
        const distributionColors = [
            'rgba(78, 115, 223, 0.8)',
            'rgba(28, 200, 138, 0.8)',
            'rgba(54, 185, 204, 0.8)',
            'rgba(246, 194, 62, 0.8)',
            'rgba(231, 74, 59, 0.8)'
        ];
        
        // تبديل بين أنواع التوزيع المختلفة
        let currentDistribution = 'category'; // الافتراضي: توزيع حسب الفئات
        
        function updateDistributionChart(type) {
            currentDistribution = type;
            let labels = [];
            let data = [];
            let title = '';
            
            if (type === 'category') {
                title = '{% trans "توزيع المبيعات حسب الفئات" %}';
                labels = [
                    {% for category in category_sales %}
                        '{{ category.category__name }}',
                    {% empty %}
                        '{% trans "لا توجد بيانات" %}'
                    {% endfor %}
                ];
                data = [
                    {% for category in category_sales %}
                        {{ category.total_sales }},
                    {% empty %}
                        0
                    {% endfor %}
                ];
            } else if (type === 'customer') {
                title = '{% trans "توزيع المبيعات حسب العملاء" %}';
                labels = [
                    {% for customer in customer_sales %}
                        '{{ customer.customer__name }}',
                    {% empty %}
                        '{% trans "لا توجد بيانات" %}'
                    {% endfor %}
                ];
                data = [
                    {% for customer in customer_sales %}
                        {{ customer.total_sales }},
                    {% empty %}
                        0
                    {% endfor %}
                ];
            } else if (type === 'payment') {
                title = '{% trans "توزيع المبيعات حسب طريقة الدفع" %}';
                labels = [
                    {% for payment in payment_method_sales %}
                        '{{ payment.payment_method_display }}',
                    {% empty %}
                        '{% trans "لا توجد بيانات" %}'
                    {% endfor %}
                ];
                data = [
                    {% for payment in payment_method_sales %}
                        {{ payment.total_sales }},
                    {% empty %}
                        0
                    {% endfor %}
                ];
            }
            
            salesDistributionChart.data.labels = labels;
            salesDistributionChart.data.datasets[0].data = data;
            salesDistributionChart.options.plugins.title.text = title;
            salesDistributionChart.update();
        }
        
        const salesDistributionChart = new Chart(distributionCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for category in category_sales %}
                        '{{ category.category__name }}',
                    {% empty %}
                        '{% trans "لا توجد بيانات" %}'
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for category in category_sales %}
                            {{ category.total_sales }},
                        {% empty %}
                            0
                        {% endfor %}
                    ],
                    backgroundColor: distributionColors,
                    hoverBackgroundColor: distributionColors.map(color => color.replace('0.8', '1')),
                    hoverBorderColor: 'white'
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: '{% trans "توزيع المبيعات حسب الفئات" %}'
                    },
                    tooltip: {
                        backgroundColor: "rgb(255,255,255)",
                        bodyColor: "#858796",
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        xPadding: 15,
                        yPadding: 15,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed !== null) {
                                    label += context.parsed + ' {% trans "د.م." %}';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
        
        // تفعيل أزرار تبديل نوع التوزيع
        document.querySelectorAll('.distribution-toggle').forEach(button => {
            button.addEventListener('click', function() {
                const type = this.getAttribute('data-type');
                updateDistributionChart(type);
                
                // تحديث الزر النشط
                document.querySelectorAll('.distribution-toggle').forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');
            });
        });
        
        // تفعيل مقارنة الفترات الزمنية
        function initDateRangePicker(elementId) {
            $(elementId).daterangepicker({
                opens: 'rtl',
                autoUpdateInput: false, // التعامل باليد مع التحديث
                locale: {
                    format: 'YYYY-MM-DD',
                    applyLabel: '{% trans "تطبيق" %}',
                    cancelLabel: '{% trans "إلغاء" %}',
                    fromLabel: '{% trans "من" %}',
                    toLabel: '{% trans "إلى" %}',
                    customRangeLabel: '{% trans "مخصص" %}',
                    daysOfWeek: ['{% trans "أحد" %}', '{% trans "إثنين" %}', '{% trans "ثلاثاء" %}', '{% trans "أربعاء" %}', '{% trans "خميس" %}', '{% trans "جمعة" %}', '{% trans "سبت" %}'],
                    monthNames: ['{% trans "يناير" %}', '{% trans "فبراير" %}', '{% trans "مارس" %}', '{% trans "أبريل" %}', '{% trans "مايو" %}', '{% trans "يونيو" %}', '{% trans "يوليو" %}', '{% trans "أغسطس" %}', '{% trans "سبتمبر" %}', '{% trans "أكتوبر" %}', '{% trans "نوفمبر" %}', '{% trans "ديسمبر" %}'],
                    firstDay: 0
                },
                showDropdowns: true,
                drops: 'down',
                minYear: 2020,
                maxYear: parseInt(new Date().getFullYear(), 10)
            }, function(start, end, label) {
                // تحديث الحقول عند اختيار التاريخ
                $(elementId).val(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));

                // تحديث حقول التاريخ المخفية
                const startField = elementId.replace('-range', '_start');
                const endField = elementId.replace('-range', '_end');

                $(startField).val(start.format('YYYY-MM-DD'));
                $(endField).val(end.format('YYYY-MM-DD'));
            });

            // التعامل مع زر التقويم
            $(elementId + '-btn').on('click', function() {
                $(elementId).trigger('click');
            });

            // إظهار رسالة عند إلغاء الاختيار
            $(elementId).on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
                const startField = elementId.replace('-range', '_start');
                const endField = elementId.replace('-range', '_end');
                $(startField).val('');
                $(endField).val('');
            });
        }

        // تهيئة محدد التاريخ للفترة الأولى
        initDateRangePicker('#period1-range');

        // تهيئة محدد التاريخ للفترة الثانية
        initDateRangePicker('#period2-range');

        // تعيين قيم افتراضية للفترات الزمنية عند تحميل الصفحة
        function setDefaultDates() {
            const today = new Date();
            const lastMonth = new Date();
            lastMonth.setMonth(today.getMonth() - 1);

            const formatDate = function(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            // تعيين الفترة الأولى للشهر الحالي
            const currentMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            const currentMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            $('#period1_start').val(formatDate(currentMonthStart));
            $('#period1_end').val(formatDate(currentMonthEnd));
            $('#period1-range').val(`${formatDate(currentMonthStart)} - ${formatDate(currentMonthEnd)}`);

            // تعيين الفترة الثانية للشهر السابق
            const lastMonthStart = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);
            const lastMonthEnd = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);

            $('#period2_start').val(formatDate(lastMonthStart));
            $('#period2_end').val(formatDate(lastMonthEnd));
            $('#period2-range').val(`${formatDate(lastMonthStart)} - ${formatDate(lastMonthEnd)}`);
        }

        // تعيين القيم الافتراضية عند تحميل الصفحة
        setDefaultDates();
        
        // تعيين قيم افتراضية للفترات الزمنية
        const today = new Date();
        const lastMonth = new Date();
        lastMonth.setMonth(today.getMonth() - 1);
        
        const formatDate = function(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };
        
        // تعيين الفترة الأولى للشهر الحالي
        const currentMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const currentMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        
        // تعيين الفترة الثانية للشهر السابق
        const lastMonthStart = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1);
        const lastMonthEnd = new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 0);
        
        // تعيين القيم الافتراضية للحقول
        $('#period1_start').val(formatDate(currentMonthStart));
        $('#period1_end').val(formatDate(currentMonthEnd));
        $('#period1-range').val(`${formatDate(currentMonthStart)} - ${formatDate(currentMonthEnd)}`);
        
        $('#period2_start').val(formatDate(lastMonthStart));
        $('#period2_end').val(formatDate(lastMonthEnd));
        $('#period2-range').val(`${formatDate(lastMonthStart)} - ${formatDate(lastMonthEnd)}`);
        
        // تحديث الحقول عند اختيار تاريخ
        $('#period1-range').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            $('#period1_start').val(picker.startDate.format('YYYY-MM-DD'));
            $('#period1_end').val(picker.endDate.format('YYYY-MM-DD'));
        });
        
        $('#period2-range').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            $('#period2_start').val(picker.startDate.format('YYYY-MM-DD'));
            $('#period2_end').val(picker.endDate.format('YYYY-MM-DD'));
        });
        
        // إعادة تعيين الحقول عند إلغاء الاختيار
        $('#period1-range').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
            $('#period1_start').val('');
            $('#period1_end').val('');
        });
        
        $('#period2-range').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
            $('#period2_start').val('');
            $('#period2_end').val('');
        });
        
        // تنفيذ المقارنة بين الفترتين
        $('#compare-periods').on('click', function() {
            const period1Start = $('#period1_start').val();
            const period1End = $('#period1_end').val();
            const period2Start = $('#period2_start').val();
            const period2End = $('#period2_end').val();
            
            if (!period1Start || !period1End || !period2Start || !period2End) {
                alert('{% trans "يرجى تحديد الفترتين الزمنيتين للمقارنة" %}');
                return;
            }
            
            // عرض مؤشر التحميل
            $('#comparison-results').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">{% trans "جاري التحميل..." %}</span></div><p class="mt-2">{% trans "جاري تحليل البيانات..." %}</p></div>');
            $('#comparison-results').show();
            
            // إرسال طلب AJAX لجلب بيانات المقارنة
            $.ajax({
                url: '{% url "reports:compare_periods" %}',
                type: 'GET',
                data: {
                    period1_start: period1Start,
                    period1_end: period1End,
                    period2_start: period2Start,
                    period2_end: period2End,
                    category: $('#category').val(),
                    product: $('#product').val(),
                    customer: $('#customer').val(),
                    payment_method: $('#payment_method').val()
                },
                success: function(response) {
                    // عرض نتائج المقارنة
                    $('#comparison-results').html(`
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>{% trans "المؤشر" %}</th>
                                        <th>{% trans "الفترة الأولى" %}</th>
                                        <th>{% trans "الفترة الثانية" %}</th>
                                        <th>{% trans "التغيير" %}</th>
                                        <th>{% trans "نسبة التغيير" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{% trans "عدد المبيعات" %}</td>
                                        <td id="period1-sales-count">${response.period1.count}</td>
                                        <td id="period2-sales-count">${response.period2.count}</td>
                                        <td id="sales-count-change">${response.comparison.count_change}</td>
                                        <td id="sales-count-change-percent">${response.comparison.count_change_percent}%</td>
                                    </tr>
                                    <tr>
                                        <td>{% trans "إجمالي المبيعات" %}</td>
                                        <td id="period1-sales-total">${response.period1.total} {% trans "د.م." %}</td>
                                        <td id="period2-sales-total">${response.period2.total} {% trans "د.م." %}</td>
                                        <td id="sales-total-change">${response.comparison.total_change} {% trans "د.م." %}</td>
                                        <td id="sales-total-change-percent">${response.comparison.total_change_percent}%</td>
                                    </tr>
                                    <tr>
                                        <td>{% trans "متوسط قيمة البيع" %}</td>
                                        <td id="period1-avg-sale">${response.period1.average} {% trans "د.م." %}</td>
                                        <td id="period2-avg-sale">${response.period2.average} {% trans "د.م." %}</td>
                                        <td id="avg-sale-change">${response.comparison.average_change} {% trans "د.م." %}</td>
                                        <td id="avg-sale-change-percent">${response.comparison.average_change_percent}%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="chart-container mt-4">
                            <canvas id="comparisonChart"></canvas>
                        </div>
                    `);
                    
                    // إنشاء رسم بياني للمقارنة
                    if (window.comparisonChart) {
                        window.comparisonChart.destroy();
                    }
                    
                    const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
                    window.comparisonChart = new Chart(comparisonCtx, {
                        type: 'bar',
                        data: {
                            labels: ['{% trans "عدد المبيعات" %}', '{% trans "إجمالي المبيعات" %}', '{% trans "متوسط قيمة البيع" %}'],
                            datasets: [
                                {
                                    label: '{% trans "الفترة الأولى" %}',
                                    backgroundColor: 'rgba(78, 115, 223, 0.8)',
                                    borderColor: 'rgba(78, 115, 223, 1)',
                                    data: [response.period1.count, response.period1.total, response.period1.average]
                                },
                                {
                                    label: '{% trans "الفترة الثانية" %}',
                                    backgroundColor: 'rgba(28, 200, 138, 0.8)',
                                    borderColor: 'rgba(28, 200, 138, 1)',
                                    data: [response.period2.count, response.period2.total, response.period2.average]
                                }
                            ]
                        },
                        options: {
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            if (value >= 1000) {
                                                return (value / 1000).toFixed(1) + 'K';
                                            }
                                            return value;
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            let label = context.dataset.label || '';
                                            if (label) {
                                                label += ': ';
                                            }
                                            if (context.parsed.y !== null) {
                                                if (context.dataIndex === 0) {
                                                    label += context.parsed.y;
                                                } else {
                                                    label += context.parsed.y + ' {% trans "د.م." %}';
                                                }
                                            }
                                            return label;
                                        }
                                    }
                                }
                            }
                        }
                    });
                },
                error: function(xhr, status, error) {
                    // في حالة حدوث خطأ، استخدم بيانات وهمية للعرض
                    console.error('خطأ في جلب بيانات المقارنة:', error);
                    
                    // عرض نتائج المقارنة باستخدام بيانات وهمية
                    $('#comparison-results').html(`
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% trans "تعذر جلب البيانات من الخادم. يتم عرض بيانات توضيحية." %}
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>{% trans "المؤشر" %}</th>
                                        <th>{% trans "الفترة الأولى" %}</th>
                                        <th>{% trans "الفترة الثانية" %}</th>
                                        <th>{% trans "التغيير" %}</th>
                                        <th>{% trans "نسبة التغيير" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{% trans "عدد المبيعات" %}</td>
                                        <td id="period1-sales-count">25</td>
                                        <td id="period2-sales-count">30</td>
                                        <td id="sales-count-change">+5</td>
                                        <td id="sales-count-change-percent">+20%</td>
                                    </tr>
                                    <tr>
                                        <td>{% trans "إجمالي المبيعات" %}</td>
                                        <td id="period1-sales-total">15,000 {% trans "د.م." %}</td>
                                        <td id="period2-sales-total">18,000 {% trans "د.م." %}</td>
                                        <td id="sales-total-change">3,000 {% trans "د.م." %}</td>
                                        <td id="sales-total-change-percent">+20%</td>
                                    </tr>
                                    <tr>
                                        <td>{% trans "متوسط قيمة البيع" %}</td>
                                        <td id="period1-avg-sale">600 {% trans "د.م." %}</td>
                                        <td id="period2-avg-sale">600 {% trans "د.م." %}</td>
                                        <td id="avg-sale-change">0 {% trans "د.م." %}</td>
                                        <td id="avg-sale-change-percent">0%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="chart-container mt-4">
                            <canvas id="comparisonChart"></canvas>
                        </div>
                    `);
                    
                    // إنشاء رسم بياني للمقارنة باستخدام بيانات وهمية
                    if (window.comparisonChart) {
                        window.comparisonChart.destroy();
                    }
                    
                    const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
                    window.comparisonChart = new Chart(comparisonCtx, {
                        type: 'bar',
                        data: {
                            labels: ['{% trans "عدد المبيعات" %}', '{% trans "إجمالي المبيعات" %}', '{% trans "متوسط قيمة البيع" %}'],
                            datasets: [
                                {
                                    label: '{% trans "الفترة الأولى" %}',
                                    backgroundColor: 'rgba(78, 115, 223, 0.8)',
                                    borderColor: 'rgba(78, 115, 223, 1)',
                                    data: [25, 15000, 600]
                                },
                                {
                                    label: '{% trans "الفترة الثانية" %}',
                                    backgroundColor: 'rgba(28, 200, 138, 0.8)',
                                    borderColor: 'rgba(28, 200, 138, 1)',
                                    data: [30, 18000, 600]
                                }
                            ]
                        },
                        options: {
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            });
        });
        
        // تفعيل ميزات التنبيهات والإشعارات
        $('#configure-alerts').on('click', function() {
            $('#alertConfigModal').modal('show');
        });
        
        $('#email-report').on('click', function() {
            $('#emailReportModal').modal('show');
        });
        
        $('#save-alert-config').on('click', function() {
            // هنا يمكن إضافة كود لحفظ إعدادات التنبيهات
            const threshold = $('#sales-drop-threshold').val();
            const enableEmail = $('#enable-email-alerts').is(':checked');
            const frequency = $('#report-frequency').val();
            const email = $('#recipient-email').val();
            
            // عرض رسالة نجاح
            const alertHTML = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <strong>{% trans "تم الحفظ!" %}</strong> {% trans "تم حفظ إعدادات التنبيهات بنجاح." %}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            $('#alerts-container').prepend(alertHTML);
            
            $('#alertConfigModal').modal('hide');
        });
        
        $('#send-email-report').on('click', function() {
            // هنا يمكن إضافة كود لإرسال التقرير بالبريد الإلكتروني
            const recipient = $('#email-recipient').val();
            const subject = $('#email-subject').val();
            const includeCharts = $('#include-charts').is(':checked');
            const includeTables = $('#include-tables').is(':checked');
            const includeSummary = $('#include-summary').is(':checked');
            const format = $('#report-format').val();
            
            // عرض رسالة نجاح
            const alertHTML = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <strong>{% trans "تم الإرسال!" %}</strong> {% trans "تم إرسال التقرير بنجاح إلى" %} ${recipient}.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            $('#alerts-container').prepend(alertHTML);
            
            $('#emailReportModal').modal('hide');
        });
        
        $('#clear-alerts').on('click', function() {
            // مسح جميع التنبيهات
            $('#alerts-container').html(`
                <div class="text-center py-4">
                    <i class="fas fa-bell-slash fa-3x text-gray-300 mb-3"></i>
                    <p class="text-muted">{% trans "لا توجد تنبيهات حاليًا" %}</p>
                </div>
            `);
        });
        
        // إضافة تنبيه افتراضي للعرض
        if ($('#alerts-container .alert').length === 0 && !$('#alerts-container .text-center').length) {
            const demoAlertHTML = `
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <strong>{% trans "انخفاض في المبيعات!" %}</strong> {% trans "انخفضت مبيعات الفئة 'الإلكترونيات' بنسبة 15% مقارنة بالأسبوع الماضي." %}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            $('#alerts-container').html(demoAlertHTML);
        }
        
        // تفعيل تنبيهات انخفاض المبيعات تلقائيًا
        function checkSalesDrops() {
            // هذه وظيفة وهمية للعرض فقط
            // في التطبيق الحقيقي، ستقوم بفحص بيانات المبيعات وإنشاء تنبيهات عند اكتشاف انخفاضات
            console.log('فحص انخفاضات المبيعات...');
        }
        
        // تشغيل فحص انخفاضات المبيعات كل ساعة
        setInterval(checkSalesDrops, 3600000); // 3600000 مللي ثانية = ساعة واحدة

        // Global Search Functionality
        $('#globalSearch').on('keyup', function() {
            salesTable.search(this.value).draw();
            updateResultsCount();
        });

        // Clear Search
        $('#clearSearch').on('click', function() {
            $('#globalSearch').val('');
            salesTable.search('').draw();
            updateResultsCount();
        });

        // Update results count
        function updateResultsCount() {
            var info = salesTable.page.info();
            $('#resultsCount').text(info.recordsDisplay);
        }

        // Filter form submission
        $('#filterForm').on('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });

        // Apply filters function
        function applyFilters() {
            var formData = $('#filterForm').serialize();

            // Show loading
            showLoading();

            // Make AJAX request
            $.get(window.location.pathname, formData)
                .done(function(data) {
                    // Reload page with new data
                    window.location.search = formData;
                })
                .fail(function() {
                    hideLoading();
                    showAlert('error', 'حدث خطأ أثناء تطبيق الفلاتر');
                });
        }

        // Clear filters (old function)
        window.clearFilters = function() {
            $('#filterForm')[0].reset();
            window.location.href = window.location.pathname;
        };

        // Reset filters (new enhanced function)
        window.resetFilters = function() {
            // Reset form fields
            $('#filterForm')[0].reset();

            // Clear date range picker
            $('#daterange').val('');
            $('#start_date').val('');
            $('#end_date').val('');

            // Reset all select elements to first option
            $('#category').val('');
            $('#product').val('');
            $('#customer').val('');
            $('#payment_method').val('');
            $('#payment_status').val('');
            $('#sort_by').val('date');

            // Clear amount fields
            $('#min_amount').val('');
            $('#max_amount').val('');

            // Clear search field
            $('#globalSearch').val('');

            // Remove active class from quick filters
            $('.quick-filter').removeClass('active');

            // Clear localStorage saved filters
            localStorage.removeItem('salesReportFilters');

            // Redirect to clean URL
            window.location.href = window.location.pathname;
        };

        // Export functions
        window.exportReport = function(format) {
            var currentFilters = $('#filterForm').serialize();
            var exportUrl = '{% url "reports:export_sales_report" %}?format=' + format;
            if (currentFilters) {
                exportUrl += '&' + currentFilters;
            }
            window.open(exportUrl, '_blank');
        };

        // Loading and alert functions
        function showLoading() {
            $('body').append('<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;"><div class="spinner-border text-light" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
        }

        function hideLoading() {
            $('#loadingOverlay').remove();
        }

        function showAlert(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                message + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>';
            $('.container-fluid').prepend(alertHtml);

            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }
    });
</script>
{% endblock %}
