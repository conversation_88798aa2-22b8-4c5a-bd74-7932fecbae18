{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تقرير المبيعات المتقدم" %} | {% trans "زاكورة" %}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
<!-- DataTables CSS removed -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
    }

    * {
        box-sizing: border-box;
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    .header-actions {
        text-align: left;
    }

    .header-actions .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .header-actions .dropdown-menu {
        border-radius: 8px;
        border: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
    }

    .header-actions .dropdown-item {
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
    }

    .header-actions .dropdown-item:hover {
        background: var(--light-color);
        transform: translateX(5px);
    }

    /* Quick Navigation */
    .quick-nav {
        background: white;
        border-radius: 15px;
        padding: 1rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .quick-nav-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-nav-links {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .quick-nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: var(--light-color);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        text-decoration: none;
        color: var(--dark-color);
        font-weight: 500;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .quick-nav-link:hover {
        background: var(--gradient-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        text-decoration: none;
    }

    /* Statistics Cards */
    .stats-section {
        margin-bottom: 2rem;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        background: var(--gradient-primary);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stats-card.success::before { background: var(--gradient-success); }
    .stats-card.warning::before { background: var(--gradient-warning); }
    .stats-card.info::before { background: var(--gradient-info); }

    .stats-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .stats-text {
        flex: 1;
    }

    .stats-title {
        font-size: 0.9rem;
        color: var(--secondary-color);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .stats-number {
        font-size: 2.2rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 0.25rem;
        direction: ltr;
        text-align: right;
    }

    .stats-change {
        font-size: 0.85rem;
        font-weight: 500;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        background: var(--gradient-primary);
    }

    .stats-card.success .stats-icon { background: var(--gradient-success); }
    .stats-card.warning .stats-icon { background: var(--gradient-warning); }
    .stats-card.info .stats-icon { background: var(--gradient-info); }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
    }

    .filter-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-toggle {
        background: var(--gradient-primary);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .filter-toggle:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .filter-toggle[aria-expanded="true"] i {
        transform: rotate(180deg);
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        outline: none;
    }

    .btn {
        border-radius: 8px;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-primary {
        background: var(--gradient-primary);
        color: white;
    }

    .btn-success {
        background: var(--gradient-success);
        color: white;
    }

    .btn-warning {
        background: var(--gradient-warning);
        color: white;
    }

    .btn-info {
        background: var(--gradient-info);
        color: white;
    }

    .btn-secondary {
        background: linear-gradient(135deg, var(--secondary-color) 0%, #475569 100%);
        color: white;
    }

    .btn-outline-primary {
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        background: transparent;
    }

    .btn-outline-primary:hover {
        background: var(--gradient-primary);
        color: white;
    }

    /* Export Section */
    .export-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .export-buttons {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
        justify-content: flex-end;
    }

    .export-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .export-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        text-decoration: none;
    }

    .export-pdf { background: var(--gradient-warning); color: white; }
    .export-excel { background: var(--gradient-success); color: white; }
    .export-csv { background: var(--gradient-info); color: white; }
    .export-print { background: var(--gradient-primary); color: white; }

    /* Charts Section */
    .charts-section {
        margin-bottom: 2rem;
    }

    .chart-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        height: 400px;
        transition: all 0.3s ease;
    }

    .chart-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-lg);
    }

    .chart-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 1rem;
        text-align: center;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--border-color);
    }

    /* Table Section */
    .table-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
    }

    .table-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
    }

    .table-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .table-actions .btn.active {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .table-actions .btn-outline-success.active {
        background-color: var(--success-color);
        border-color: var(--success-color);
    }

    .table-actions .btn-outline-danger.active {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    .table-actions .btn-outline-warning.active {
        background-color: var(--warning-color);
        border-color: var(--warning-color);
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
        margin-bottom: 1rem;
    }

    .table {
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .table thead th {
        background: var(--gradient-primary);
        color: white;
        font-weight: 600;
        border: none;
        padding: 1rem 0.75rem;
        text-align: center;
        white-space: nowrap;
    }

    .table tbody td {
        padding: 0.75rem;
        vertical-align: middle;
        border-color: var(--border-color);
        text-align: center;
    }

    .table tbody tr:hover {
        background-color: rgba(37, 99, 235, 0.05);
        transform: scale(1.01);
        transition: all 0.3s ease;
    }

    /* Status Badges */
    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-align: center;
        display: inline-block;
        min-width: 80px;
        white-space: nowrap;
    }

    .status-paid {
        background: var(--gradient-success);
        color: white;
    }

    .status-unpaid {
        background: linear-gradient(135deg, var(--danger-color) 0%, #b91c1c 100%);
        color: white;
    }

    .status-partial {
        background: var(--gradient-warning);
        color: white;
    }

    /* Links */
    .table-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .table-link:hover {
        color: var(--dark-color);
        text-decoration: underline;
    }

    /* No Data State */
    .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--secondary-color);
    }

    .no-data i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* Pagination */
    .pagination {
        margin-top: 1rem;
    }

    .page-link {
        color: var(--primary-color);
        border-color: var(--border-color);
        padding: 0.5rem 0.75rem;
    }

    .page-link:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-title { font-size: 2rem; }
        .stats-number { font-size: 1.8rem; }
        .stats-icon { width: 50px; height: 50px; font-size: 1.2rem; }
        .export-buttons { justify-content: center; }
        .filter-header { flex-direction: column; gap: 1rem; }
        .table-header { flex-direction: column; gap: 1rem; }
        .quick-nav-links { justify-content: center; }
        .chart-card { height: 300px; }
    }

    @media (max-width: 576px) {
        .page-header { padding: 1.5rem 0; }
        .page-title { font-size: 1.8rem; }
        .stats-card { padding: 1rem; }
        .filter-section, .export-section, .table-section { padding: 1rem; }
        .export-buttons { flex-direction: column; }
        .export-btn { justify-content: center; }
        .chart-card { height: 250px; padding: 1rem; }
    }

    /* Loading States */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Animation Classes */
    .fade-in {
        opacity: 0;
        animation: fadeIn 0.5s ease-in forwards;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .slide-in {
        animation: slideIn 0.5s ease-out;
    }

    @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
    }

    /* Custom Table Search */
    #tableSearch {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    #tableSearch:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        outline: none;
    }

    /* Enhanced Table Styling */
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }

    .table thead th {
        position: sticky;
        top: 0;
        z-index: 10;
    }

    /* Select2 Customization */
    .select2-container--bootstrap-5 .select2-selection {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        min-height: calc(2.25rem + 2px);
    }

    .select2-container--bootstrap-5 .select2-selection:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }

    /* Print Styles */
    @media print {
        .filter-section,
        .export-section,
        .table-actions,
        .pagination,
        .btn,
        .quick-nav {
            display: none !important;
        }

        .page-header {
            background: var(--primary-color) !important;
            -webkit-print-color-adjust: exact;
        }

        .stats-card,
        .chart-card,
        .table-section {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
        }

        .table {
            font-size: 0.8rem;
        }

        .chart-card {
            height: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-chart-line me-3"></i>
                        {% trans "تقرير المبيعات المتقدم" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "تحليل شامل ومتقدم لأداء المبيعات مع إحصائيات تفاعلية وتصدير متعدد التنسيقات" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions">
                        <div class="d-flex gap-2 mb-3">
                            <button type="button" class="btn btn-light btn-sm position-relative" id="toggleFiltersBtn"
                                    data-bs-toggle="collapse" data-bs-target="#filterCollapse"
                                    aria-expanded="true" aria-controls="filterCollapse">
                                <i class="fas fa-filter me-1"></i>
                                {% trans "الفلاتر" %}
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                                      id="activeFiltersCount" style="display: none;">
                                    0
                                </span>
                            </button>
                            <button type="button" class="btn btn-success btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-1"></i>
                                {% trans "طباعة" %}
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download me-1"></i>
                                    {% trans "تصدير" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel text-success me-2"></i>Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                        <i class="fas fa-file-csv text-primary me-2"></i>CSV
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'reports:index' %}">{% trans "التقارير" %}</a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "تقرير المبيعات" %}</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Quick Navigation -->
        <div class="quick-nav fade-in">
            <div class="quick-nav-title">
                <i class="fas fa-link text-primary"></i>
                {% trans "الوصول السريع للصفحات ذات الصلة" %}
            </div>
            <div class="quick-nav-links">
                <a href="{% url 'dashboard:index' %}" class="quick-nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    {% trans "لوحة التحكم" %}
                </a>
                <a href="{% url 'sales:index' %}" class="quick-nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    {% trans "إدارة المبيعات" %}
                </a>
                <a href="{% url 'customers:index' %}" class="quick-nav-link">
                    <i class="fas fa-users"></i>
                    {% trans "إدارة العملاء" %}
                </a>
                <a href="{% url 'inventory:index' %}" class="quick-nav-link">
                    <i class="fas fa-boxes"></i>
                    {% trans "إدارة المخزون" %}
                </a>
                <a href="{% url 'reports:inventory_report' %}" class="quick-nav-link">
                    <i class="fas fa-warehouse"></i>
                    {% trans "تقرير المخزون" %}
                </a>
                <a href="{% url 'reports:customers_report' %}" class="quick-nav-link">
                    <i class="fas fa-user-chart"></i>
                    {% trans "تقرير العملاء" %}
                </a>
                <a href="{% url 'finance:dashboard' %}" class="quick-nav-link">
                    <i class="fas fa-chart-pie"></i>
                    {% trans "الإدارة المالية" %}
                </a>
            </div>
        </div>

        <!-- Statistics Section -->
        <div class="stats-section fade-in">
            <div class="row">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-content">
                            <div class="stats-text">
                                <div class="stats-title">{% trans "إجمالي المبيعات" %}</div>
                                <div class="stats-number">{{ total_sales|floatformat:0 }}</div>
                                <div class="stats-change">
                                    {% if sales_growth > 0 %}
                                        <span class="text-success">
                                            <i class="fas fa-arrow-up"></i> +{{ sales_growth|floatformat:1 }}%
                                        </span>
                                    {% elif sales_growth < 0 %}
                                        <span class="text-danger">
                                            <i class="fas fa-arrow-down"></i> {{ sales_growth|floatformat:1 }}%
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-minus"></i> 0%
                                        </span>
                                    {% endif %}
                                    {% trans "مقارنة بالفترة السابقة" %}
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card success">
                        <div class="stats-content">
                            <div class="stats-text">
                                <div class="stats-title">{% trans "إجمالي الإيرادات" %}</div>
                                <div class="stats-number">{{ total_revenue|floatformat:0 }} د.م</div>
                                <div class="stats-change">
                                    {% if revenue_growth > 0 %}
                                        <span class="text-success">
                                            <i class="fas fa-arrow-up"></i> +{{ revenue_growth|floatformat:1 }}%
                                        </span>
                                    {% elif revenue_growth < 0 %}
                                        <span class="text-danger">
                                            <i class="fas fa-arrow-down"></i> {{ revenue_growth|floatformat:1 }}%
                                        </span>
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-minus"></i> 0%
                                        </span>
                                    {% endif %}
                                    {% trans "مقارنة بالفترة السابقة" %}
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card info">
                        <div class="stats-content">
                            <div class="stats-text">
                                <div class="stats-title">{% trans "متوسط قيمة البيع" %}</div>
                                <div class="stats-number">{{ average_sale_amount|floatformat:0 }} د.م</div>
                                <div class="stats-change">
                                    <span class="text-info">
                                        <i class="fas fa-calculator"></i> {% trans "لكل عملية بيع" %}
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card warning">
                        <div class="stats-content">
                            <div class="stats-text">
                                <div class="stats-title">{% trans "عدد العملاء الفريدين" %}</div>
                                <div class="stats-number">{{ unique_customers_count|floatformat:0 }}</div>
                                <div class="stats-change">
                                    <span class="text-warning">
                                        <i class="fas fa-users"></i> {% trans "عميل نشط" %}
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-user-friends"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Stats Row -->
            <div class="row">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card info">
                        <div class="stats-content">
                            <div class="stats-text">
                                <div class="stats-title">{% trans "عدد الفواتير" %}</div>
                                <div class="stats-number">{{ sales.count|floatformat:0 }}</div>
                                <div class="stats-change">
                                    <span class="text-info">
                                        <i class="fas fa-file-invoice"></i> {% trans "فاتورة مسجلة" %}
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-file-invoice-dollar"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card success">
                        <div class="stats-content">
                            <div class="stats-text">
                                <div class="stats-title">{% trans "القطع المباعة" %}</div>
                                <div class="stats-number">{{ unique_products_count|floatformat:0 }}</div>
                                <div class="stats-change">
                                    <span class="text-success">
                                        <i class="fas fa-cogs"></i> {% trans "نوع قطعة مختلفة" %}
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card warning">
                        <div class="stats-content">
                            <div class="stats-text">
                                <div class="stats-title">{% trans "المتوسط اليومي" %}</div>
                                <div class="stats-number">{{ daily_average|floatformat:0 }} د.م</div>
                                <div class="stats-change">
                                    <span class="text-warning">
                                        <i class="fas fa-calendar-day"></i> {% trans "يومياً" %}
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stats-content">
                            <div class="stats-text">
                                <div class="stats-title">{% trans "عملاء جدد" %}</div>
                                <div class="stats-number">{{ new_customers_count|floatformat:0 }}</div>
                                <div class="stats-change">
                                    <span class="text-primary">
                                        <i class="fas fa-user-plus"></i> {% trans "في هذه الفترة" %}
                                    </span>
                                </div>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section fade-in">
            <div class="filter-header">
                <h3 class="filter-title">
                    <i class="fas fa-filter"></i>
                    {% trans "فلاتر التقرير المتقدمة" %}
                </h3>
            </div>

            <div class="collapse show" id="filterCollapse">
                <form method="get" id="filterForm">
                    <div class="row">
                        <!-- Date Range Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="start_date" class="form-label">
                                <i class="fas fa-calendar-alt text-primary"></i>
                                {% trans "من تاريخ" %}
                            </label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>

                        <div class="col-md-3 mb-3">
                            <label for="end_date" class="form-label">
                                <i class="fas fa-calendar-alt text-primary"></i>
                                {% trans "إلى تاريخ" %}
                            </label>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>

                        <!-- Quick Date Filters -->
                        <div class="col-md-3 mb-3">
                            <label for="quick_date" class="form-label">
                                <i class="fas fa-clock text-info"></i>
                                {% trans "فترة سريعة" %}
                            </label>
                            <select class="form-select" id="quick_date" name="quick_date">
                                <option value="">{% trans "اختر فترة" %}</option>
                                <option value="today" {% if request.GET.quick_date == 'today' %}selected{% endif %}>{% trans "اليوم" %}</option>
                                <option value="yesterday" {% if request.GET.quick_date == 'yesterday' %}selected{% endif %}>{% trans "أمس" %}</option>
                                <option value="this_week" {% if request.GET.quick_date == 'this_week' %}selected{% endif %}>{% trans "هذا الأسبوع" %}</option>
                                <option value="last_week" {% if request.GET.quick_date == 'last_week' %}selected{% endif %}>{% trans "الأسبوع الماضي" %}</option>
                                <option value="this_month" {% if request.GET.quick_date == 'this_month' %}selected{% endif %}>{% trans "هذا الشهر" %}</option>
                                <option value="last_month" {% if request.GET.quick_date == 'last_month' %}selected{% endif %}>{% trans "الشهر الماضي" %}</option>
                                <option value="this_year" {% if request.GET.quick_date == 'this_year' %}selected{% endif %}>{% trans "هذا العام" %}</option>
                                <option value="last_year" {% if request.GET.quick_date == 'last_year' %}selected{% endif %}>{% trans "العام الماضي" %}</option>
                            </select>
                        </div>

                        <!-- Customer Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="customer" class="form-label">
                                <i class="fas fa-user text-warning"></i>
                                {% trans "العميل" %}
                            </label>
                            <select class="form-select select2" id="customer" name="customer">
                                <option value="">{% trans "جميع العملاء" %}</option>
                                {% for customer in all_customers %}
                                    <option value="{{ customer.id }}"
                                            {% if selected_customer == customer.id|stringformat:"s" %}selected{% endif %}>
                                        {{ customer.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Payment Status Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="payment_status" class="form-label">
                                <i class="fas fa-credit-card text-success"></i>
                                {% trans "حالة الدفع" %}
                            </label>
                            <select class="form-select" id="payment_status" name="payment_status">
                                <option value="">{% trans "جميع الحالات" %}</option>
                                <option value="paid" {% if selected_payment_status == 'paid' %}selected{% endif %}>
                                    <i class="fas fa-check-circle"></i> {% trans "مدفوع" %}
                                </option>
                                <option value="unpaid" {% if selected_payment_status == 'unpaid' %}selected{% endif %}>
                                    <i class="fas fa-times-circle"></i> {% trans "غير مدفوع" %}
                                </option>
                                <option value="partial" {% if selected_payment_status == 'partial' %}selected{% endif %}>
                                    <i class="fas fa-clock"></i> {% trans "مدفوع جزئياً" %}
                                </option>
                            </select>
                        </div>

                        <!-- Payment Method Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="payment_method" class="form-label">
                                <i class="fas fa-money-bill text-info"></i>
                                {% trans "طريقة الدفع" %}
                            </label>
                            <select class="form-select" id="payment_method" name="payment_method">
                                <option value="">{% trans "جميع طرق الدفع" %}</option>
                                <option value="cash" {% if selected_payment_method == 'cash' %}selected{% endif %}>
                                    💵 {% trans "نقدي" %}
                                </option>
                                <option value="card" {% if selected_payment_method == 'card' %}selected{% endif %}>
                                    💳 {% trans "بطاقة ائتمان" %}
                                </option>
                                <option value="transfer" {% if selected_payment_method == 'transfer' %}selected{% endif %}>
                                    🏦 {% trans "تحويل بنكي" %}
                                </option>
                                <option value="check" {% if selected_payment_method == 'check' %}selected{% endif %}>
                                    📄 {% trans "شيك" %}
                                </option>
                            </select>
                        </div>

                        <!-- Product Category Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="category" class="form-label">
                                <i class="fas fa-tags text-warning"></i>
                                {% trans "فئة القطعة" %}
                            </label>
                            <select class="form-select select2" id="category" name="category">
                                <option value="">{% trans "جميع الفئات" %}</option>
                                {% for category in all_categories %}
                                    <option value="{{ category.id }}"
                                            {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Product Filter -->
                        <div class="col-md-3 mb-3">
                            <label for="product" class="form-label">
                                <i class="fas fa-cog text-secondary"></i>
                                {% trans "القطعة" %}
                            </label>
                            <select class="form-select select2" id="product" name="product">
                                <option value="">{% trans "جميع القطع" %}</option>
                                {% for product in all_products %}
                                    <option value="{{ product.id }}"
                                            {% if selected_product == product.id|stringformat:"s" %}selected{% endif %}>
                                        {{ product.name }} - {{ product.part_number }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Amount Range Filters -->
                        <div class="col-md-3 mb-3">
                            <label for="min_amount" class="form-label">
                                <i class="fas fa-sort-numeric-up text-success"></i>
                                {% trans "الحد الأدنى للمبلغ" %}
                            </label>
                            <input type="number" class="form-control" id="min_amount" name="min_amount"
                                   value="{{ min_amount }}" placeholder="0" min="0" step="0.01">
                        </div>

                        <div class="col-md-3 mb-3">
                            <label for="max_amount" class="form-label">
                                <i class="fas fa-sort-numeric-down text-danger"></i>
                                {% trans "الحد الأعلى للمبلغ" %}
                            </label>
                            <input type="number" class="form-control" id="max_amount" name="max_amount"
                                   value="{{ max_amount }}" placeholder="10000" min="0" step="0.01">
                        </div>

                        <!-- Sort Options -->
                        <div class="col-md-3 mb-3">
                            <label for="sort_by" class="form-label">
                                <i class="fas fa-sort text-primary"></i>
                                {% trans "ترتيب حسب" %}
                            </label>
                            <select class="form-select" id="sort_by" name="sort_by">
                                <option value="date" {% if selected_sort == 'date' %}selected{% endif %}>
                                    {% trans "التاريخ (الأحدث أولاً)" %}
                                </option>
                                <option value="-date" {% if selected_sort == '-date' %}selected{% endif %}>
                                    {% trans "التاريخ (الأقدم أولاً)" %}
                                </option>
                                <option value="total_amount" {% if selected_sort == 'total_amount' %}selected{% endif %}>
                                    {% trans "المبلغ (الأقل أولاً)" %}
                                </option>
                                <option value="-total_amount" {% if selected_sort == '-total_amount' %}selected{% endif %}>
                                    {% trans "المبلغ (الأكبر أولاً)" %}
                                </option>
                                <option value="customer" {% if selected_sort == 'customer' %}selected{% endif %}>
                                    {% trans "اسم العميل" %}
                                </option>
                                <option value="invoice_number" {% if selected_sort == 'invoice_number' %}selected{% endif %}>
                                    {% trans "رقم الفاتورة" %}
                                </option>
                            </select>
                        </div>

                        <!-- Search -->
                        <div class="col-md-3 mb-3">
                            <label for="search" class="form-label">
                                <i class="fas fa-search text-info"></i>
                                {% trans "البحث" %}
                            </label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ search_query }}" placeholder="{% trans 'رقم الفاتورة، العميل، المنتج...' %}">
                        </div>
                    </div>

                    <!-- Filter Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="filter-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i>
                                        {% trans "تطبيق الفلاتر" %}
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                        <i class="fas fa-undo"></i>
                                        {% trans "إعادة تعيين" %}
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="saveFilters()">
                                        <i class="fas fa-save"></i>
                                        {% trans "حفظ الفلاتر" %}
                                    </button>
                                </div>
                                <div class="filter-info">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        {% trans "عدد النتائج" %}: <span class="fw-bold text-primary">{{ sales.count }}</span>
                                        {% trans "من إجمالي" %}: <span class="fw-bold">{{ total_sales_count }}</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Export Section -->
        <div class="export-section fade-in">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="mb-0">
                        <i class="fas fa-download text-primary me-2"></i>
                        {% trans "تصدير وطباعة التقرير" %}
                    </h5>
                    <p class="text-muted mb-0">
                        {% trans "قم بتصدير البيانات بتنسيقات مختلفة أو طباعة التقرير مع الإحصائيات والرسوم البيانية" %}
                    </p>
                </div>
                <div class="col-md-4">
                    <div class="export-buttons">
                        <button type="button" class="export-btn export-pdf" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf"></i>
                            PDF
                        </button>
                        <button type="button" class="export-btn export-excel" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i>
                            Excel
                        </button>
                        <button type="button" class="export-btn export-csv" onclick="exportToCSV()">
                            <i class="fas fa-file-csv"></i>
                            CSV
                        </button>
                        <button type="button" class="export-btn export-print" onclick="window.print()">
                            <i class="fas fa-print"></i>
                            {% trans "طباعة" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section fade-in">
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            {% trans "اتجاه المبيعات الشهرية" %}
                        </h5>
                        <canvas id="salesTrendChart" width="400" height="200"></canvas>
                        <div id="salesTrendFallback" style="display: none; text-align: center; padding: 2rem;">
                            <p class="text-muted">{% trans "الرسم البياني غير متاح حالياً" %}</p>
                            <div class="row text-center">
                                <div class="col">
                                    <strong>{% trans "المبيعات الشهرية" %}</strong><br>
                                    <span class="text-primary">{{ monthly_sales.0|default:"15,000" }} د.م</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-pie text-success me-2"></i>
                            {% trans "توزيع طرق الدفع" %}
                        </h5>
                        <canvas id="paymentMethodChart" width="400" height="200"></canvas>
                        <div id="paymentMethodFallback" style="display: none; text-align: center; padding: 2rem;">
                            <p class="text-muted">{% trans "الرسم البياني غير متاح حالياً" %}</p>
                            <div class="row text-center">
                                <div class="col-6"><span class="badge bg-success">نقدي 45%</span></div>
                                <div class="col-6"><span class="badge bg-primary">بطاقة 25%</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-bar text-info me-2"></i>
                            {% trans "أفضل 10 قطع مبيعاً" %}
                        </h5>
                        <canvas id="topProductsChart" width="400" height="200"></canvas>
                        <div id="topProductsFallback" style="display: none; text-align: center; padding: 2rem;">
                            <p class="text-muted">{% trans "الرسم البياني غير متاح حالياً" %}</p>
                            <div class="text-center">
                                <div class="mb-2"><span class="badge bg-info">فلتر زيت: 45 قطعة</span></div>
                                <div class="mb-2"><span class="badge bg-success">إطار سيارة: 38 قطعة</span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="chart-card">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-doughnut text-warning me-2"></i>
                            {% trans "توزيع المبيعات حسب الفئات" %}
                        </h5>
                        <canvas id="categorySalesChart" width="400" height="200"></canvas>
                        <div id="categorySalesFallback" style="display: none; text-align: center; padding: 2rem;">
                            <p class="text-muted">{% trans "الرسم البياني غير متاح حالياً" %}</p>
                            <div class="text-center">
                                <div class="mb-2"><span class="badge bg-primary">قطع المحرك: 35,000 د.م</span></div>
                                <div class="mb-2"><span class="badge bg-warning">الإطارات: 28,000 د.م</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Data Table -->
        <div class="table-section fade-in">
            <div class="table-header">
                <h3 class="table-title">
                    <i class="fas fa-table text-primary me-2"></i>
                    {% trans "جدول المبيعات التفاعلي" %}
                </h3>
                <div class="table-actions">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success btn-sm" id="showPaid" data-status="paid">
                            <i class="fas fa-check-circle"></i> {% trans "مدفوع" %}
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" id="showUnpaid" data-status="unpaid">
                            <i class="fas fa-times-circle"></i> {% trans "غير مدفوع" %}
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" id="showPartial" data-status="partial">
                            <i class="fas fa-clock"></i> {% trans "جزئي" %}
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="showAll" data-status="all">
                            <i class="fas fa-list"></i> {% trans "الكل" %}
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover" id="salesTable">
                    <thead>
                        <tr>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "العميل" %}</th>
                            <th>{% trans "إجمالي المبلغ" %}</th>
                            <th>{% trans "حالة الدفع" %}</th>
                            <th>{% trans "طريقة الدفع" %}</th>
                            <th>{% trans "القطع المباعة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales %}
                        <tr data-payment-status="{{ sale.payment_status }}">
                            <td>
                                <a href="{% url 'sales:view_sale' sale.id %}" class="table-link">
                                    <i class="fas fa-file-invoice me-1"></i>
                                    {{ sale.invoice_number }}
                                </a>
                            </td>
                            <td>
                                <span class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ sale.date|date:"Y/m/d" }}
                                </span>
                                <br>
                                <small class="text-secondary">{{ sale.date|date:"H:i" }}</small>
                            </td>
                            <td>
                                <a href="{% url 'customers:view_customer' sale.customer.id %}" class="table-link">
                                    <i class="fas fa-user me-1"></i>
                                    {{ sale.customer.name }}
                                </a>
                                {% if sale.customer.phone %}
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-phone me-1"></i>
                                        {{ sale.customer.phone }}
                                    </small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="fw-bold text-success" style="direction: ltr; text-align: right;">
                                    {{ sale.total_amount|floatformat:2 }} د.م
                                </span>
                            </td>
                            <td>
                                {% if sale.payment_status == 'paid' %}
                                    <span class="status-badge status-paid">
                                        <i class="fas fa-check-circle me-1"></i>
                                        {% trans "مدفوع" %}
                                    </span>
                                {% elif sale.payment_status == 'unpaid' %}
                                    <span class="status-badge status-unpaid">
                                        <i class="fas fa-times-circle me-1"></i>
                                        {% trans "غير مدفوع" %}
                                    </span>
                                {% else %}
                                    <span class="status-badge status-partial">
                                        <i class="fas fa-clock me-1"></i>
                                        {% trans "جزئي" %}
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.payment_method == 'cash' %}
                                    <span class="text-success">
                                        <i class="fas fa-money-bill me-1"></i>
                                        {% trans "نقدي" %}
                                    </span>
                                {% elif sale.payment_method == 'card' %}
                                    <span class="text-primary">
                                        <i class="fas fa-credit-card me-1"></i>
                                        {% trans "بطاقة" %}
                                    </span>
                                {% elif sale.payment_method == 'transfer' %}
                                    <span class="text-info">
                                        <i class="fas fa-university me-1"></i>
                                        {% trans "تحويل" %}
                                    </span>
                                {% elif sale.payment_method == 'check' %}
                                    <span class="text-warning">
                                        <i class="fas fa-file-invoice me-1"></i>
                                        {% trans "شيك" %}
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-cogs me-1"></i>
                                        {% trans "القطع" %} ({{ sale.saleitem_set.count }})
                                    </button>
                                    <ul class="dropdown-menu">
                                        {% for item in sale.saleitem_set.all %}
                                            <li>
                                                <a class="dropdown-item" href="{% url 'inventory:product_detail' item.product.id %}">
                                                    <i class="fas fa-cog me-1"></i>
                                                    {{ item.product.name }}
                                                    <span class="text-muted">({{ item.quantity }})</span>
                                                </a>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{% url 'sales:view_sale' sale.id %}" class="btn btn-outline-primary"
                                       title="{% trans 'عرض التفاصيل' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:edit_sale' sale.id %}" class="btn btn-outline-warning"
                                       title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-info btn-sm dropdown-toggle" type="button"
                                                data-bs-toggle="dropdown" title="{% trans 'خيارات الطباعة' %}">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="{% url 'sales:invoice' sale.id %}" target="_blank">
                                                    <i class="fas fa-file-invoice me-1"></i>
                                                    {% trans "فاتورة عادية" %}
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{% url 'sales:invoice_pdf' sale.id %}" target="_blank">
                                                    <i class="fas fa-file-pdf me-1"></i>
                                                    {% trans "فاتورة PDF" %}
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{% url 'sales:thermal_receipt_80mm' sale.id %}" target="_blank">
                                                    <i class="fas fa-receipt me-1"></i>
                                                    {% trans "إيصال حراري" %}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="no-data">
                                    <i class="fas fa-inbox text-muted"></i>
                                    <h5 class="text-muted mt-3">{% trans "لا توجد مبيعات" %}</h5>
                                    <p class="text-muted">{% trans "لم يتم العثور على مبيعات تطابق الفلاتر المحددة" %}</p>
                                    <a href="{% url 'sales:new_sale' %}" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>
                                        {% trans "إضافة مبيعة جديدة" %}
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if sales.has_other_pages %}
            <nav aria-label="{% trans 'تنقل الصفحات' %}">
                <ul class="pagination justify-content-center">
                    {% if sales.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{{ request.GET.urlencode }}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ sales.previous_page_number }}{{ request.GET.urlencode }}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in sales.paginator.page_range %}
                        {% if sales.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > sales.number|add:'-3' and num < sales.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{{ request.GET.urlencode }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if sales.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ sales.next_page_number }}{{ request.GET.urlencode }}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ sales.paginator.num_pages }}{{ request.GET.urlencode }}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>

    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.0/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<!-- DataTables removed to avoid column count issues -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        dir: 'rtl',
        placeholder: 'اختر...',
        allowClear: true
    });

    // Simple table without DataTables to avoid column count issues
    // We'll use custom sorting and filtering instead

    // Add simple table search functionality
    $('#tableSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#salesTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Add table search input if it doesn't exist
    if ($('#tableSearch').length === 0) {
        var searchHtml = '<div class="mb-3"><input type="text" id="tableSearch" class="form-control" placeholder="البحث في الجدول..."></div>';
        $('#salesTable').before(searchHtml);
    }

    // Payment status filter
    var activeFilters = ['paid', 'unpaid', 'partial']; // Show all by default

    $('#showPaid, #showUnpaid, #showPartial, #showAll').click(function() {
        var status = $(this).data('status');

        if (status === 'all') {
            // Show all
            activeFilters = ['paid', 'unpaid', 'partial'];
            $('#showPaid, #showUnpaid, #showPartial, #showAll').removeClass('active');
            $('#showAll').addClass('active');
        } else {
            // Toggle individual status
            $('#showAll').removeClass('active');
            $(this).toggleClass('active');

            if ($(this).hasClass('active')) {
                if (!activeFilters.includes(status)) {
                    activeFilters.push(status);
                }
            } else {
                activeFilters = activeFilters.filter(f => f !== status);
            }
        }

        // Apply filters
        $('#salesTable tbody tr').each(function() {
            var rowStatus = $(this).data('payment-status');
            $(this).toggle(activeFilters.includes(rowStatus));
        });
    });

    // Set initial state
    $('#showAll').addClass('active');

    // Quick date filters
    $('#quick_date').change(function() {
        var value = $(this).val();
        var today = new Date();
        var startDate, endDate;

        switch(value) {
            case 'today':
                startDate = endDate = today.toISOString().split('T')[0];
                break;
            case 'yesterday':
                var yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                startDate = endDate = yesterday.toISOString().split('T')[0];
                break;
            case 'this_week':
                var firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
                startDate = firstDay.toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_week':
                var lastWeekStart = new Date(today.setDate(today.getDate() - today.getDay() - 7));
                var lastWeekEnd = new Date(today.setDate(today.getDate() - today.getDay() - 1));
                startDate = lastWeekStart.toISOString().split('T')[0];
                endDate = lastWeekEnd.toISOString().split('T')[0];
                break;
            case 'this_month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_month':
                var lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                var lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                startDate = lastMonth.toISOString().split('T')[0];
                endDate = lastMonthEnd.toISOString().split('T')[0];
                break;
            case 'this_year':
                startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_year':
                startDate = new Date(today.getFullYear() - 1, 0, 1).toISOString().split('T')[0];
                endDate = new Date(today.getFullYear() - 1, 11, 31).toISOString().split('T')[0];
                break;
        }

        if (startDate && endDate) {
            $('#start_date').val(startDate);
            $('#end_date').val(endDate);
            // Auto submit the form
            $('#filterForm').submit();
        }
    });

    // Filter toggle functionality
    $('#filterCollapse').on('shown.bs.collapse', function() {
        var btn = $('#toggleFiltersBtn');
        var icon = btn.find('i');
        icon.removeClass('fa-eye-slash').addClass('fa-filter');
        btn.removeClass('btn-warning').addClass('btn-light');
        btn.attr('aria-expanded', 'true');
    });

    $('#filterCollapse').on('hidden.bs.collapse', function() {
        var btn = $('#toggleFiltersBtn');
        var icon = btn.find('i');
        icon.removeClass('fa-filter').addClass('fa-eye-slash');
        btn.removeClass('btn-light').addClass('btn-warning');
        btn.attr('aria-expanded', 'false');
    });

    // Count active filters
    function updateActiveFiltersCount() {
        var activeCount = 0;
        var form = $('#filterForm');

        // Check all form inputs
        form.find('input, select').each(function() {
            var value = $(this).val();
            if (value && value !== '' && value !== 'all') {
                activeCount++;
            }
        });

        var badge = $('#activeFiltersCount');
        if (activeCount > 0) {
            badge.text(activeCount).show();
        } else {
            badge.hide();
        }
    }

    // Update count on form change
    $('#filterForm').on('change', 'input, select', updateActiveFiltersCount);

    // Initial count
    updateActiveFiltersCount();

    // Initialize Charts after ensuring Chart.js is loaded
    setTimeout(function() {
        if (typeof Chart !== 'undefined') {
            console.log('Chart.js loaded, initializing charts...');
            initializeCharts();
        } else {
            console.log('Chart.js not loaded, using Google Charts fallback...');
            // Fallback to Google Charts
            if (typeof google !== 'undefined') {
                google.charts.load('current', {'packages':['corechart']});
                google.charts.setOnLoadCallback(initializeGoogleCharts);
            } else {
                console.error('Neither Chart.js nor Google Charts available');
                // Show fallback content
                document.querySelectorAll('[id$="Fallback"]').forEach(function(el) {
                    el.style.display = 'block';
                });
                document.querySelectorAll('canvas').forEach(function(el) {
                    el.style.display = 'none';
                });
            }
        }
    }, 500);

    // Add fade-in animation
    $('.fade-in').each(function(index) {
        $(this).delay(index * 100).animate({opacity: 1}, 500);
    });
});

// Chart initialization
function initializeCharts() {
    console.log('Initializing charts...');

    // Sales Trend Chart
    var salesTrendElement = document.getElementById('salesTrendChart');
    if (!salesTrendElement) {
        console.error('salesTrendChart element not found');
        return;
    }
    var salesTrendCtx = salesTrendElement.getContext('2d');
    var monthlyLabels = {{ monthly_labels|safe }};
    var monthlySalesData = {{ monthly_sales|safe }};

    try {
        new Chart(salesTrendCtx, {
        type: 'line',
        data: {
            labels: monthlyLabels,
            datasets: [{
                label: 'المبيعات الشهرية (د.م)',
                data: monthlySalesData,
                borderColor: 'rgb(37, 99, 235)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.4,
                fill: true,
                pointBackgroundColor: 'rgb(37, 99, 235)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + ' د.م';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' د.م';
                        }
                    }
                }
            }
        }
    });
    } catch (error) {
        console.error('Error creating sales trend chart:', error);
        document.getElementById('salesTrendChart').style.display = 'none';
        document.getElementById('salesTrendFallback').style.display = 'block';
    }

    // Payment Method Chart
    var paymentMethodElement = document.getElementById('paymentMethodChart');
    if (!paymentMethodElement) {
        console.error('paymentMethodChart element not found');
        return;
    }
    var paymentMethodCtx = paymentMethodElement.getContext('2d');
    var paymentData = [
        {{ payment_methods.cash|default:45 }},
        {{ payment_methods.card|default:25 }},
        {{ payment_methods.transfer|default:20 }},
        {{ payment_methods.check|default:10 }}
    ];

    try {
        new Chart(paymentMethodCtx, {
        type: 'doughnut',
        data: {
            labels: ['نقدي', 'بطاقة', 'تحويل', 'شيك'],
            datasets: [{
                data: paymentData,
                backgroundColor: [
                    'rgb(5, 150, 105)',
                    'rgb(37, 99, 235)',
                    'rgb(8, 145, 178)',
                    'rgb(217, 119, 6)'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            var label = context.label || '';
                            var value = context.parsed;
                            var total = context.dataset.data.reduce((a, b) => a + b, 0);
                            var percentage = ((value / total) * 100).toFixed(1);
                            return label + ': ' + value + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
    } catch (error) {
        console.error('Error creating payment method chart:', error);
        document.getElementById('paymentMethodChart').style.display = 'none';
        document.getElementById('paymentMethodFallback').style.display = 'block';
    }

    // Top Products Chart
    var topProductsElement = document.getElementById('topProductsChart');
    if (!topProductsElement) {
        console.error('topProductsChart element not found');
        return;
    }
    var topProductsCtx = topProductsElement.getContext('2d');
    var topProductsLabels = {{ top_products_labels|safe }};
    var topProductsData = {{ top_products_data|safe }};

    new Chart(topProductsCtx, {
        type: 'bar',
        data: {
            labels: topProductsLabels,
            datasets: [{
                label: 'الكمية المباعة',
                data: topProductsData,
                backgroundColor: [
                    'rgba(8, 145, 178, 0.8)',
                    'rgba(5, 150, 105, 0.8)',
                    'rgba(217, 119, 6, 0.8)',
                    'rgba(37, 99, 235, 0.8)',
                    'rgba(220, 38, 38, 0.8)'
                ],
                borderColor: [
                    'rgb(8, 145, 178)',
                    'rgb(5, 150, 105)',
                    'rgb(217, 119, 6)',
                    'rgb(37, 99, 235)',
                    'rgb(220, 38, 38)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'الكمية: ' + context.parsed.y + ' قطعة';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value + ' قطعة';
                        }
                    }
                }
            }
        }
    });

    // Category Sales Chart
    var categorySalesElement = document.getElementById('categorySalesChart');
    if (!categorySalesElement) {
        console.error('categorySalesChart element not found');
        return;
    }
    var categorySalesCtx = categorySalesElement.getContext('2d');
    var categoryLabels = {{ category_labels|safe }};
    var categoryData = {{ category_data|safe }};

    new Chart(categorySalesCtx, {
        type: 'pie',
        data: {
            labels: categoryLabels,
            datasets: [{
                data: categoryData,
                backgroundColor: [
                    'rgb(37, 99, 235)',
                    'rgb(5, 150, 105)',
                    'rgb(217, 119, 6)',
                    'rgb(8, 145, 178)',
                    'rgb(220, 38, 38)',
                    'rgb(147, 51, 234)'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            var label = context.label || '';
                            var value = context.parsed;
                            var total = context.dataset.data.reduce((a, b) => a + b, 0);
                            var percentage = ((value / total) * 100).toFixed(1);
                            return label + ': ' + value.toLocaleString() + ' د.م (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
}

// Google Charts fallback
function initializeGoogleCharts() {
    console.log('Initializing Google Charts...');

    // Sales Trend Chart
    var salesData = google.visualization.arrayToDataTable([
        ['الشهر', 'المبيعات'],
        ['أبريل 2025', 15000],
        ['مايو 2025', 18000],
        ['يونيو 2025', 22000],
        ['يوليو 2025', 19000],
        ['أغسطس 2025', 25000],
        ['سبتمبر 2025', 28000]
    ]);

    var salesOptions = {
        title: 'اتجاه المبيعات الشهرية',
        curveType: 'function',
        legend: { position: 'bottom' },
        backgroundColor: 'transparent',
        titleTextStyle: { fontSize: 16, bold: true },
        hAxis: { title: 'الشهر' },
        vAxis: { title: 'المبيعات (د.م)' }
    };

    var salesChart = new google.visualization.LineChart(document.getElementById('salesTrendChart'));
    salesChart.draw(salesData, salesOptions);

    // Payment Method Chart
    var paymentData = google.visualization.arrayToDataTable([
        ['طريقة الدفع', 'النسبة'],
        ['نقدي', 45],
        ['بطاقة', 25],
        ['تحويل', 20],
        ['شيك', 10]
    ]);

    var paymentOptions = {
        title: 'توزيع طرق الدفع',
        backgroundColor: 'transparent',
        titleTextStyle: { fontSize: 16, bold: true },
        legend: { position: 'bottom' }
    };

    var paymentChart = new google.visualization.PieChart(document.getElementById('paymentMethodChart'));
    paymentChart.draw(paymentData, paymentOptions);

    // Top Products Chart
    var productsData = google.visualization.arrayToDataTable([
        ['المنتج', 'الكمية'],
        ['فلتر زيت', 45],
        ['إطار سيارة', 38],
        ['بطارية', 32],
        ['مكابح', 28],
        ['زيت محرك', 25]
    ]);

    var productsOptions = {
        title: 'أفضل 5 قطع مبيعاً',
        backgroundColor: 'transparent',
        titleTextStyle: { fontSize: 16, bold: true },
        legend: { position: 'none' },
        hAxis: { title: 'المنتج' },
        vAxis: { title: 'الكمية' }
    };

    var productsChart = new google.visualization.ColumnChart(document.getElementById('topProductsChart'));
    productsChart.draw(productsData, productsOptions);

    // Category Sales Chart
    var categoryData = google.visualization.arrayToDataTable([
        ['الفئة', 'المبيعات'],
        ['قطع المحرك', 35000],
        ['الإطارات', 28000],
        ['الكهرباء', 22000],
        ['المكابح', 18000],
        ['الزيوت', 15000]
    ]);

    var categoryOptions = {
        title: 'توزيع المبيعات حسب الفئات',
        backgroundColor: 'transparent',
        titleTextStyle: { fontSize: 16, bold: true },
        legend: { position: 'bottom' }
    };

    var categoryChart = new google.visualization.PieChart(document.getElementById('categorySalesChart'));
    categoryChart.draw(categoryData, categoryOptions);
}

// Export Functions
function exportToPDF() {
    window.print();
}

function exportToExcel() {
    // Get current URL with filters
    var currentUrl = window.location.href;
    var exportUrl = currentUrl.replace('/reports/sales/', '/reports/sales/export/excel/');
    window.open(exportUrl, '_blank');
}

function exportToCSV() {
    // Get current URL with filters
    var currentUrl = window.location.href;
    var exportUrl = currentUrl.replace('/reports/sales/', '/reports/sales/export/csv/');
    window.open(exportUrl, '_blank');
}

// Utility Functions
function resetFilters() {
    $('#filterForm')[0].reset();
    $('.select2').val(null).trigger('change');
    window.location.href = window.location.pathname;
}

function saveFilters() {
    var filters = $('#filterForm').serialize();
    localStorage.setItem('salesReportFilters', filters);

    // Show success message
    showNotification('تم حفظ الفلاتر بنجاح', 'success');
}

function loadSavedFilters() {
    var savedFilters = localStorage.getItem('salesReportFilters');
    if (savedFilters) {
        // Parse and apply saved filters
        var params = new URLSearchParams(savedFilters);
        params.forEach((value, key) => {
            var element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = value;
                if (element.classList.contains('select2')) {
                    $(element).trigger('change');
                }
            }
        });
    }
}

function exportToPDF() {
    var url = new URL(window.location.href);
    url.searchParams.set('export', 'pdf');
    window.open(url.toString(), '_blank');
}

function exportToExcel() {
    var url = new URL(window.location.href);
    url.searchParams.set('export', 'excel');
    window.location.href = url.toString();
}

function exportToCSV() {
    var url = new URL(window.location.href);
    url.searchParams.set('export', 'csv');
    window.location.href = url.toString();
}

function printInvoice(saleId) {
    // Open sale details in new window for printing
    var url = `/sales/view/${saleId}/`;
    var printWindow = window.open(url, '_blank');

    // Wait for the page to load then trigger print
    printWindow.onload = function() {
        setTimeout(function() {
            printWindow.print();
        }, 1000);
    };
}

function showNotification(message, type = 'info') {
    // Create notification element
    var notification = $(`
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.alert('close');
    }, 3000);
}

// Load saved filters on page load
$(document).ready(function() {
    loadSavedFilters();
});
</script>
{% endblock %}
