{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تقرير المبيعات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daterangepicker.css' %}">
<style>
    .chart-container {
        position: relative;
        height: 300px;
    }

    .filter-section {
        background: #f8f9fc;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e3e6f0;
    }

    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        border-left: 0.25rem solid #4e73df;
    }

    .stats-card.success {
        border-left-color: #1cc88a;
    }

    .stats-card.warning {
        border-left-color: #f6c23e;
    }

    .stats-card.info {
        border-left-color: #36b9cc;
    }

    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }

    .table thead th {
        position: sticky;
        top: 0;
        background: #f8f9fc;
        z-index: 10;
    }

    @media (max-width: 768px) {
        .filter-section {
            padding: 1rem;
        }
        
        .table-responsive {
            font-size: 0.85rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% csrf_token %}

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line me-2"></i>{% trans "تقرير المبيعات" %}
        </h1>
        <div>
            <a href="{% url 'reports:index' %}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i> {% trans "تصدير" %}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-2"></i>PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-2"></i>Excel
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي المبيعات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_sales|floatformat:2 }} {% trans "د.م" %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card success">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "عدد المبيعات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ sales_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card info">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "متوسط قيمة البيع" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ average_sale|floatformat:2 }} {% trans "د.م" %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card warning">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "عدد العملاء" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ customers_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters Section -->
    <div class="filter-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>{% trans "فلاتر التقرير" %}
            </h5>
            <button class="btn btn-info btn-sm" type="button" data-bs-toggle="collapse" 
                    data-bs-target="#filterCollapse" aria-expanded="true">
                <i class="fas fa-chevron-down me-1"></i>{% trans "إظهار/إخفاء الفلاتر" %}
            </button>
        </div>

        <div class="collapse show" id="filterCollapse">
            <form method="get" id="filterForm">
                <div class="row">
                    <!-- Date Range Filter -->
                    <div class="col-md-3 mb-3">
                        <label for="start_date" class="form-label">
                            <i class="fas fa-calendar-alt text-primary"></i>
                            {% trans "من تاريخ" %}
                        </label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ request.GET.start_date }}">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="end_date" class="form-label">
                            <i class="fas fa-calendar-alt text-primary"></i>
                            {% trans "إلى تاريخ" %}
                        </label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ request.GET.end_date }}">
                    </div>

                    <!-- Quick Date Selection -->
                    <div class="col-md-3 mb-3">
                        <label for="quick_date" class="form-label">
                            <i class="fas fa-clock text-info"></i>
                            {% trans "اختيار سريع" %}
                        </label>
                        <select class="form-select" id="quick_date" name="quick_date">
                            <option value="">{% trans "اختر فترة" %}</option>
                            <option value="today" {% if request.GET.quick_date == 'today' %}selected{% endif %}>
                                {% trans "اليوم" %}
                            </option>
                            <option value="yesterday" {% if request.GET.quick_date == 'yesterday' %}selected{% endif %}>
                                {% trans "أمس" %}
                            </option>
                            <option value="this_week" {% if request.GET.quick_date == 'this_week' %}selected{% endif %}>
                                {% trans "هذا الأسبوع" %}
                            </option>
                            <option value="last_week" {% if request.GET.quick_date == 'last_week' %}selected{% endif %}>
                                {% trans "الأسبوع الماضي" %}
                            </option>
                            <option value="this_month" {% if request.GET.quick_date == 'this_month' %}selected{% endif %}>
                                {% trans "هذا الشهر" %}
                            </option>
                            <option value="last_month" {% if request.GET.quick_date == 'last_month' %}selected{% endif %}>
                                {% trans "الشهر الماضي" %}
                            </option>
                            <option value="this_year" {% if request.GET.quick_date == 'this_year' %}selected{% endif %}>
                                {% trans "هذا العام" %}
                            </option>
                            <option value="last_year" {% if request.GET.quick_date == 'last_year' %}selected{% endif %}>
                                {% trans "العام الماضي" %}
                            </option>
                        </select>
                    </div>

                    <!-- Customer Filter -->
                    <div class="col-md-3 mb-3">
                        <label for="customer" class="form-label">
                            <i class="fas fa-user text-success"></i>
                            {% trans "العميل" %}
                        </label>
                        <select class="form-select" id="customer" name="customer">
                            <option value="">{% trans "جميع العملاء" %}</option>
                            {% for customer in customers %}
                                <option value="{{ customer.id }}" 
                                        {% if request.GET.customer == customer.id|stringformat:"s" %}selected{% endif %}>
                                    {{ customer.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="row">
                    <!-- Payment Status Filter -->
                    <div class="col-md-3 mb-3">
                        <label for="payment_status" class="form-label">
                            <i class="fas fa-credit-card text-warning"></i>
                            {% trans "حالة الدفع" %}
                        </label>
                        <select class="form-select" id="payment_status" name="payment_status">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="paid" {% if request.GET.payment_status == 'paid' %}selected{% endif %}>
                                {% trans "مدفوع" %}
                            </option>
                            <option value="unpaid" {% if request.GET.payment_status == 'unpaid' %}selected{% endif %}>
                                {% trans "غير مدفوع" %}
                            </option>
                            <option value="partial" {% if request.GET.payment_status == 'partial' %}selected{% endif %}>
                                {% trans "مدفوع جزئياً" %}
                            </option>
                        </select>
                    </div>

                    <!-- Payment Method Filter -->
                    <div class="col-md-3 mb-3">
                        <label for="payment_method" class="form-label">
                            <i class="fas fa-money-bill text-info"></i>
                            {% trans "طريقة الدفع" %}
                        </label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="">{% trans "جميع الطرق" %}</option>
                            <option value="cash" {% if request.GET.payment_method == 'cash' %}selected{% endif %}>
                                {% trans "نقدي" %}
                            </option>
                            <option value="card" {% if request.GET.payment_method == 'card' %}selected{% endif %}>
                                {% trans "بطاقة" %}
                            </option>
                            <option value="transfer" {% if request.GET.payment_method == 'transfer' %}selected{% endif %}>
                                {% trans "تحويل بنكي" %}
                            </option>
                            <option value="check" {% if request.GET.payment_method == 'check' %}selected{% endif %}>
                                {% trans "شيك" %}
                            </option>
                        </select>
                    </div>

                    <!-- Search -->
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">
                            <i class="fas fa-search text-primary"></i>
                            {% trans "البحث" %}
                        </label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request.GET.search }}" placeholder="{% trans 'رقم الفاتورة، اسم العميل...' %}">
                    </div>

                    <!-- Filter Actions -->
                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i>
                                {% trans "تطبيق" %}
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                <i class="fas fa-undo"></i>
                                {% trans "إعادة تعيين" %}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>{% trans "اتجاه المبيعات" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="salesTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>{% trans "طرق الدفع" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="paymentMethodChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Data Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table me-2"></i>{% trans "جدول المبيعات" %}
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="salesTable">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "رقم الفاتورة" %}</th>
                            <th>{% trans "التاريخ" %}</th>
                            <th>{% trans "العميل" %}</th>
                            <th>{% trans "المبلغ الإجمالي" %}</th>
                            <th>{% trans "حالة الدفع" %}</th>
                            <th>{% trans "طريقة الدفع" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales %}
                        <tr>
                            <td>
                                <strong class="text-primary">#{{ sale.invoice_number }}</strong>
                            </td>
                            <td>{{ sale.date|date:"Y-m-d" }}</td>
                            <td>{{ sale.customer.name }}</td>
                            <td>
                                <strong class="text-success">{{ sale.total_amount|floatformat:2 }} {% trans "د.م" %}</strong>
                            </td>
                            <td>
                                {% if sale.payment_status == 'paid' %}
                                    <span class="badge bg-success">{% trans "مدفوع" %}</span>
                                {% elif sale.payment_status == 'unpaid' %}
                                    <span class="badge bg-danger">{% trans "غير مدفوع" %}</span>
                                {% elif sale.payment_status == 'partial' %}
                                    <span class="badge bg-warning">{% trans "جزئي" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.payment_method == 'cash' %}
                                    <i class="fas fa-money-bill text-success me-1"></i>{% trans "نقدي" %}
                                {% elif sale.payment_method == 'card' %}
                                    <i class="fas fa-credit-card text-primary me-1"></i>{% trans "بطاقة" %}
                                {% elif sale.payment_method == 'transfer' %}
                                    <i class="fas fa-exchange-alt text-info me-1"></i>{% trans "تحويل" %}
                                {% elif sale.payment_method == 'check' %}
                                    <i class="fas fa-file-invoice text-warning me-1"></i>{% trans "شيك" %}
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'sales:detail' sale.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:edit' sale.id %}" class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <br>{% trans "لا توجد مبيعات تطابق المعايير المحددة" %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if sales.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if sales.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ sales.previous_page_number }}">
                                {% trans "السابق" %}
                            </a>
                        </li>
                    {% endif %}

                    {% for num in sales.paginator.page_range %}
                        {% if sales.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > sales.number|add:'-3' and num < sales.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if sales.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ sales.next_page_number }}">
                                {% trans "التالي" %}
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/moment.min.js' %}"></script>
<script src="{% static 'js/daterangepicker.min.js' %}"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // Quick date selection
    $('#quick_date').change(function() {
        var selectedPeriod = $(this).val();
        var today = new Date();
        var startDate, endDate;

        switch(selectedPeriod) {
            case 'today':
                startDate = endDate = today.toISOString().split('T')[0];
                break;
            case 'yesterday':
                var yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                startDate = endDate = yesterday.toISOString().split('T')[0];
                break;
            case 'this_week':
                var firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
                startDate = firstDay.toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_week':
                var lastWeekStart = new Date(today.setDate(today.getDate() - today.getDay() - 7));
                var lastWeekEnd = new Date(today.setDate(today.getDate() - today.getDay() - 1));
                startDate = lastWeekStart.toISOString().split('T')[0];
                endDate = lastWeekEnd.toISOString().split('T')[0];
                break;
            case 'this_month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_month':
                startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1).toISOString().split('T')[0];
                endDate = new Date(today.getFullYear(), today.getMonth(), 0).toISOString().split('T')[0];
                break;
            case 'this_year':
                startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
                endDate = new Date().toISOString().split('T')[0];
                break;
            case 'last_year':
                startDate = new Date(today.getFullYear() - 1, 0, 1).toISOString().split('T')[0];
                endDate = new Date(today.getFullYear() - 1, 11, 31).toISOString().split('T')[0];
                break;
        }

        if (startDate && endDate) {
            $('#start_date').val(startDate);
            $('#end_date').val(endDate);
        }
    });

    // Initialize Charts
    initializeCharts();
});

// Chart initialization
function initializeCharts() {
    // Sample data for charts
    var monthlyLabels = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
    var monthlySalesData = [15000, 18000, 22000, 19000, 25000, 28000];

    // Sales Trend Chart
    var salesTrendCtx = document.getElementById('salesTrendChart');
    if (salesTrendCtx) {
        new Chart(salesTrendCtx, {
            type: 'line',
            data: {
                labels: monthlyLabels,
                datasets: [{
                    label: 'المبيعات الشهرية (د.م)',
                    data: monthlySalesData,
                    borderColor: 'rgb(37, 99, 235)',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Payment Method Chart
    var paymentMethodCtx = document.getElementById('paymentMethodChart');
    if (paymentMethodCtx) {
        new Chart(paymentMethodCtx, {
            type: 'doughnut',
            data: {
                labels: ['نقدي', 'بطاقة', 'تحويل', 'شيك'],
                datasets: [{
                    data: [45, 25, 20, 10],
                    backgroundColor: [
                        'rgb(5, 150, 105)',
                        'rgb(37, 99, 235)',
                        'rgb(8, 145, 178)',
                        'rgb(217, 119, 6)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// Utility Functions
function resetFilters() {
    $('#filterForm')[0].reset();
    window.location.href = window.location.pathname;
}

function exportReport(format) {
    var currentUrl = window.location.href;
    var exportUrl = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'export=' + format;
    window.open(exportUrl, '_blank');
}
</script>
{% endblock %}
