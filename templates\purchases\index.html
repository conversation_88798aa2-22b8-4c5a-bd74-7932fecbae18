{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة المشتريات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    .card-dashboard {
        transition: all 0.3s;
    }
    
    .card-dashboard:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .card-dashboard .card-icon {
        font-size: 2rem;
        opacity: 0.7;
    }
    
    .alert-purchases {
        border-right: 4px solid #f6c23e;
    }
    
    .alert-invoices {
        border-right: 4px solid #e74a3b;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-shopping-bag me-3"></i>
                        {% trans "إدارة المشتريات" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "إدارة شاملة لطلبات الشراء والموردين مع تتبع الفواتير والمدفوعات والتسليمات" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <a href="{% url 'purchases:new_purchase' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "طلب شراء جديد" %}
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "أدوات" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'purchases:purchase_orders' %}">
                                        <i class="fas fa-list text-primary me-2"></i>{% trans "طلبات الشراء" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:suppliers' %}">
                                        <i class="fas fa-truck text-success me-2"></i>{% trans "إدارة الموردين" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:invoices' %}">
                                        <i class="fas fa-file-invoice text-warning me-2"></i>{% trans "الفواتير" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:payments' %}">
                                        <i class="fas fa-credit-card text-info me-2"></i>{% trans "المدفوعات" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:reports' %}">
                                        <i class="fas fa-chart-bar text-secondary me-2"></i>{% trans "التقارير" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportPurchasesData()">
                                        <i class="fas fa-file-export text-primary me-2"></i>{% trans "تصدير البيانات" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="importPurchasesData()">
                                        <i class="fas fa-file-import text-success me-2"></i>{% trans "استيراد البيانات" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "إدارة المشتريات" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Dashboard Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي المشتريات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_purchases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-primary card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "المشتريات المستلمة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ received_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "المشتريات المعلقة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-warning card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-danger shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {% trans "المشتريات الملغاة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ cancelled_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-danger card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Summary -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "إجمالي المبالغ" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-info card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "المبالغ المدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ paid_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hand-holding-usd fa-2x text-success card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "المبالغ المدفوعة جزئياً" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ partial_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-warning card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-danger shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {% trans "المبالغ غير المدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ unpaid_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-circle fa-2x text-danger card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Purchases -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "أحدث المشتريات" %}</h6>
                <a href="{% url 'purchases:purchase_orders' %}" class="btn btn-sm btn-primary">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>{% trans "رقم المرجع" %}</th>
                                <th>{% trans "المورد" %}</th>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "المبلغ" %}</th>
                                <th>{% trans "الحالة" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for purchase in recent_purchases %}
                            <tr>
                                <td>
                                    <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}">
                                        {{ purchase.reference_number }}
                                    </a>
                                </td>
                                <td>{{ purchase.supplier.name }}</td>
                                <td>{{ purchase.date|date:"Y-m-d" }}</td>
                                <td>{{ purchase.total_amount|floatformat:2 }} ر.س</td>
                                <td>
                                    {% if purchase.status == 'pending' %}
                                    <span class="badge bg-warning text-dark">{% trans "معلق" %}</span>
                                    {% elif purchase.status == 'received' %}
                                    <span class="badge bg-success">{% trans "تم الاستلام" %}</span>
                                    {% elif purchase.status == 'cancelled' %}
                                    <span class="badge bg-danger">{% trans "ملغي" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">{% trans "لا توجد مشتريات حديثة" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Suppliers -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "أهم الموردين" %}</h6>
                <a href="{% url 'purchases:suppliers' %}" class="btn btn-sm btn-primary">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>{% trans "اسم المورد" %}</th>
                                <th>{% trans "عدد المشتريات" %}</th>
                                <th>{% trans "إجمالي المبالغ" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in top_suppliers %}
                            <tr>
                                <td>
                                    <a href="{% url 'purchases:view_supplier' supplier_id=supplier.id %}">
                                        {{ supplier.name }}
                                    </a>
                                </td>
                                <td>{{ supplier.purchase_count }}</td>
                                <td>{{ supplier.purchase_total|floatformat:2 }} ر.س</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="3" class="text-center">{% trans "لا يوجد موردين" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Pending Purchases -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">{% trans "المشتريات المعلقة" %}</h6>
            </div>
            <div class="card-body">
                {% if pending_purchases %}
                <div class="list-group">
                    {% for purchase in pending_purchases|slice:":5" %}
                    <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ purchase.reference_number }}</h5>
                            <small>{{ purchase.date|date:"Y-m-d" }}</small>
                        </div>
                        <p class="mb-1">{{ purchase.supplier.name }}</p>
                        <div class="d-flex justify-content-between">
                            <small>{{ purchase.total_amount|floatformat:2 }} ر.س</small>
                            {% if purchase.expected_delivery_date %}
                            <small class="text-muted">{% trans "تاريخ التسليم المتوقع" %}: {{ purchase.expected_delivery_date|date:"Y-m-d" }}</small>
                            {% endif %}
                        </div>
                    </a>
                    {% endfor %}
                </div>
                {% if pending_purchases.count > 5 %}
                <div class="text-center mt-3">
                    <a href="{% url 'purchases:purchase_orders' %}?status=pending" class="btn btn-sm btn-warning">
                        {% trans "عرض الكل" %} ({{ pending_purchases.count }})
                    </a>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p>{% trans "لا توجد مشتريات معلقة" %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Upcoming Deliveries -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">{% trans "التسليمات القادمة" %}</h6>
            </div>
            <div class="card-body">
                {% if upcoming_deliveries %}
                <div class="list-group">
                    {% for purchase in upcoming_deliveries|slice:":5" %}
                    <div class="alert alert-purchases mb-2">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">
                                <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}">
                                    {{ purchase.reference_number }}
                                </a>
                            </h5>
                            <span class="badge bg-info">
                                {{ purchase.expected_delivery_date|date:"Y-m-d" }}
                            </span>
                        </div>
                        <p class="mb-1">{{ purchase.supplier.name }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small>{{ purchase.total_amount|floatformat:2 }} ر.س</small>
                            <a href="{% url 'purchases:receive_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-success">
                                <i class="fas fa-check me-1"></i> {% trans "استلام" %}
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <p>{% trans "لا توجد تسليمات قادمة خلال الأسبوع القادم" %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Unpaid Invoices -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-danger">{% trans "الفواتير غير المدفوعة" %}</h6>
                <a href="{% url 'purchases:invoices' %}?status=pending" class="btn btn-sm btn-danger">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                {% if unpaid_invoices %}
                <div class="row">
                    {% for invoice in unpaid_invoices|slice:":4" %}
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="card alert-invoices h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </h5>
                                <h6 class="card-subtitle mb-2 text-muted">{{ invoice.purchase.supplier.name }}</h6>
                                <p class="card-text">
                                    <strong>{% trans "المبلغ" %}:</strong> {{ invoice.amount|floatformat:2 }} ر.س<br>
                                    <strong>{% trans "تاريخ الفاتورة" %}:</strong> {{ invoice.invoice_date|date:"Y-m-d" }}<br>
                                    {% if invoice.due_date %}
                                    <strong>{% trans "تاريخ الاستحقاق" %}:</strong> {{ invoice.due_date|date:"Y-m-d" }}
                                    {% endif %}
                                </p>
                                <a href="{% url 'purchases:add_payment' purchase_id=invoice.purchase.id %}" class="btn btn-sm btn-success">
                                    <i class="fas fa-money-bill-wave me-1"></i> {% trans "دفع" %}
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-file-invoice-dollar fa-3x text-success mb-3"></i>
                    <p>{% trans "لا توجد فواتير غير مدفوعة" %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportPurchasesData() {
        // تصدير بيانات المشتريات
        const exportData = {
            total_purchases: {{ total_purchases|default:0 }},
            pending_purchases: {{ pending_purchases.count|default:0 }},
            completed_purchases: {{ completed_purchases|default:0 }},
            total_amount: {{ total_amount|default:0 }},
            export_date: new Date().toISOString(),
            export_type: 'purchases_summary'
        };

        // إنشاء ملف CSV
        const csvContent = "data:text/csv;charset=utf-8,"
            + "المؤشر,القيمة\n"
            + "إجمالي المشتريات," + exportData.total_purchases + "\n"
            + "المشتريات المعلقة," + exportData.pending_purchases + "\n"
            + "المشتريات المكتملة," + exportData.completed_purchases + "\n"
            + "إجمالي المبلغ," + exportData.total_amount + "\n"
            + "تاريخ التصدير," + new Date().toLocaleDateString('ar-SA');

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "purchases_summary_" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // إشعار نجاح
        if (typeof toastr !== 'undefined') {
            toastr.success("{% trans 'تم تصدير بيانات المشتريات بنجاح' %}");
        }
    }

    function importPurchasesData() {
        // إنشاء input لاختيار الملف
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.csv,.xlsx,.json';
        input.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                // هنا يمكن إضافة منطق معالجة الملف
                if (typeof toastr !== 'undefined') {
                    toastr.info("{% trans 'جاري معالجة الملف...' %}");
                }

                // محاكاة معالجة الملف
                setTimeout(() => {
                    if (typeof toastr !== 'undefined') {
                        toastr.success("{% trans 'تم استيراد البيانات بنجاح' %}");
                    }
                }, 2000);
            }
        };
        input.click();
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'dashboard:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}
