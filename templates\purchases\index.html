{% extends 'modern_base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة المشتريات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .card-dashboard {
        transition: all 0.3s;
    }
    
    .card-dashboard:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .card-dashboard .card-icon {
        font-size: 2rem;
        opacity: 0.7;
    }
    
    .alert-purchases {
        border-right: 4px solid #f6c23e;
    }
    
    .alert-invoices {
        border-right: 4px solid #e74a3b;
    }
</style>
{% endblock %}

{% block page_title %}{% trans "إدارة المشتريات" %}{% endblock %}
{% block page_subtitle %}{% trans "إدارة متقدمة لطلبات الشراء والموردين مع تتبع المدفوعات" %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active text-white">{% trans "المشتريات" %}</li>
{% endblock %}

{% block page_actions %}
<a href="{% url 'purchases:new_purchase' %}" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i> {% trans "طلب شراء جديد" %}
</a>
{% endblock %}

{% block content %}

<!-- Dashboard Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي المشتريات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_purchases }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-primary card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "المشتريات المستلمة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ received_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "المشتريات المعلقة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-warning card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-danger shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {% trans "المشتريات الملغاة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ cancelled_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-danger card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Summary -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "إجمالي المبالغ" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-info card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "المبالغ المدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ paid_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hand-holding-usd fa-2x text-success card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            {% trans "المبالغ المدفوعة جزئياً" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ partial_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-warning card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-danger shadow h-100 py-2 card-dashboard">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {% trans "المبالغ غير المدفوعة" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ unpaid_amount|floatformat:2 }} ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-circle fa-2x text-danger card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Purchases -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "أحدث المشتريات" %}</h6>
                <a href="{% url 'purchases:purchase_orders' %}" class="btn btn-sm btn-primary">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>{% trans "رقم المرجع" %}</th>
                                <th>{% trans "المورد" %}</th>
                                <th>{% trans "التاريخ" %}</th>
                                <th>{% trans "المبلغ" %}</th>
                                <th>{% trans "الحالة" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for purchase in recent_purchases %}
                            <tr>
                                <td>
                                    <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}">
                                        {{ purchase.reference_number }}
                                    </a>
                                </td>
                                <td>{{ purchase.supplier.name }}</td>
                                <td>{{ purchase.date|date:"Y-m-d" }}</td>
                                <td>{{ purchase.total_amount|floatformat:2 }} ر.س</td>
                                <td>
                                    {% if purchase.status == 'pending' %}
                                    <span class="badge bg-warning text-dark">{% trans "معلق" %}</span>
                                    {% elif purchase.status == 'received' %}
                                    <span class="badge bg-success">{% trans "تم الاستلام" %}</span>
                                    {% elif purchase.status == 'cancelled' %}
                                    <span class="badge bg-danger">{% trans "ملغي" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">{% trans "لا توجد مشتريات حديثة" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Suppliers -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "أهم الموردين" %}</h6>
                <a href="{% url 'purchases:suppliers' %}" class="btn btn-sm btn-primary">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>{% trans "اسم المورد" %}</th>
                                <th>{% trans "عدد المشتريات" %}</th>
                                <th>{% trans "إجمالي المبالغ" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in top_suppliers %}
                            <tr>
                                <td>
                                    <a href="{% url 'purchases:view_supplier' supplier_id=supplier.id %}">
                                        {{ supplier.name }}
                                    </a>
                                </td>
                                <td>{{ supplier.purchase_count }}</td>
                                <td>{{ supplier.purchase_total|floatformat:2 }} ر.س</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="3" class="text-center">{% trans "لا يوجد موردين" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Pending Purchases -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">{% trans "المشتريات المعلقة" %}</h6>
            </div>
            <div class="card-body">
                {% if pending_purchases %}
                <div class="list-group">
                    {% for purchase in pending_purchases|slice:":5" %}
                    <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ purchase.reference_number }}</h5>
                            <small>{{ purchase.date|date:"Y-m-d" }}</small>
                        </div>
                        <p class="mb-1">{{ purchase.supplier.name }}</p>
                        <div class="d-flex justify-content-between">
                            <small>{{ purchase.total_amount|floatformat:2 }} ر.س</small>
                            {% if purchase.expected_delivery_date %}
                            <small class="text-muted">{% trans "تاريخ التسليم المتوقع" %}: {{ purchase.expected_delivery_date|date:"Y-m-d" }}</small>
                            {% endif %}
                        </div>
                    </a>
                    {% endfor %}
                </div>
                {% if pending_purchases.count > 5 %}
                <div class="text-center mt-3">
                    <a href="{% url 'purchases:purchase_orders' %}?status=pending" class="btn btn-sm btn-warning">
                        {% trans "عرض الكل" %} ({{ pending_purchases.count }})
                    </a>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p>{% trans "لا توجد مشتريات معلقة" %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Upcoming Deliveries -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">{% trans "التسليمات القادمة" %}</h6>
            </div>
            <div class="card-body">
                {% if upcoming_deliveries %}
                <div class="list-group">
                    {% for purchase in upcoming_deliveries|slice:":5" %}
                    <div class="alert alert-purchases mb-2">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">
                                <a href="{% url 'purchases:view_purchase' purchase_id=purchase.id %}">
                                    {{ purchase.reference_number }}
                                </a>
                            </h5>
                            <span class="badge bg-info">
                                {{ purchase.expected_delivery_date|date:"Y-m-d" }}
                            </span>
                        </div>
                        <p class="mb-1">{{ purchase.supplier.name }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small>{{ purchase.total_amount|floatformat:2 }} ر.س</small>
                            <a href="{% url 'purchases:receive_purchase' purchase_id=purchase.id %}" class="btn btn-sm btn-success">
                                <i class="fas fa-check me-1"></i> {% trans "استلام" %}
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <p>{% trans "لا توجد تسليمات قادمة خلال الأسبوع القادم" %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Unpaid Invoices -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-danger">{% trans "الفواتير غير المدفوعة" %}</h6>
                <a href="{% url 'purchases:invoices' %}?status=pending" class="btn btn-sm btn-danger">
                    {% trans "عرض الكل" %}
                </a>
            </div>
            <div class="card-body">
                {% if unpaid_invoices %}
                <div class="row">
                    {% for invoice in unpaid_invoices|slice:":4" %}
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="card alert-invoices h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'purchases:view_invoice' invoice_id=invoice.id %}">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </h5>
                                <h6 class="card-subtitle mb-2 text-muted">{{ invoice.purchase.supplier.name }}</h6>
                                <p class="card-text">
                                    <strong>{% trans "المبلغ" %}:</strong> {{ invoice.amount|floatformat:2 }} ر.س<br>
                                    <strong>{% trans "تاريخ الفاتورة" %}:</strong> {{ invoice.invoice_date|date:"Y-m-d" }}<br>
                                    {% if invoice.due_date %}
                                    <strong>{% trans "تاريخ الاستحقاق" %}:</strong> {{ invoice.due_date|date:"Y-m-d" }}
                                    {% endif %}
                                </p>
                                <a href="{% url 'purchases:add_payment' purchase_id=invoice.purchase.id %}" class="btn btn-sm btn-success">
                                    <i class="fas fa-money-bill-wave me-1"></i> {% trans "دفع" %}
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-file-invoice-dollar fa-3x text-success mb-3"></i>
                    <p>{% trans "لا توجد فواتير غير مدفوعة" %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}
