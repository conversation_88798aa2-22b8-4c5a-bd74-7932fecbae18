{% extends 'base.html' %}
{% load i18n %}
{% load purchase_tags %}

{% block title %}{% trans "إنشاء طلب شراء جديد" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    .required-field::after {
        content: " *";
        color: red;
    }

    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .product-row {
        transition: all 0.3s;
    }

    .product-row:hover {
        background-color: #f8f9fa;
    }

    .product-image-small {
        width: 40px;
        height: 40px;
        object-fit: contain;
        border-radius: 4px;
    }

    .summary-card {
        position: sticky;
        top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-plus-circle me-3"></i>
                        {% trans "إنشاء طلب شراء جديد" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "قم بإنشاء طلب شراء جديد مع تحديد المورد والمنتجات والكميات المطلوبة" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <button type="button" class="btn btn-success btn-sm" onclick="saveDraft()">
                                <i class="fas fa-save me-1"></i>
                                {% trans "حفظ مسودة" %}
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "أدوات" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'purchases:suppliers' %}">
                                        <i class="fas fa-truck text-success me-2"></i>{% trans "إدارة الموردين" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:index' %}">
                                        <i class="fas fa-warehouse text-primary me-2"></i>{% trans "إدارة المخزون" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="loadTemplate()">
                                        <i class="fas fa-file-import text-info me-2"></i>{% trans "تحميل قالب" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="saveAsTemplate()">
                                        <i class="fas fa-file-export text-warning me-2"></i>{% trans "حفظ كقالب" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="clearForm()">
                                        <i class="fas fa-trash text-danger me-2"></i>{% trans "مسح النموذج" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:purchase_orders' %}">
                                        <i class="fas fa-list text-secondary me-2"></i>{% trans "طلبات الشراء" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'purchases:index' %}">
                                            <i class="fas fa-shopping-bag me-1"></i>{% trans "إدارة المشتريات" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "طلب شراء جديد" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<form method="post" id="purchaseForm">
    {% csrf_token %}

    <div class="row">
        <div class="col-md-8">
            <!-- Basic Information Section -->
            <div class="form-section">
                <h5 class="form-section-title">{% trans "المعلومات الأساسية" %}</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="supplier" class="form-label required-field">{% trans "المورد" %}</label>
                        <select class="form-select" id="supplier" name="supplier" required>
                            <option value="">{% trans "اختر المورد" %}</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="date" class="form-label required-field">{% trans "تاريخ الطلب" %}</label>
                        <input type="date" class="form-control" id="date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="expected_delivery_date" class="form-label">{% trans "تاريخ التسليم المتوقع" %}</label>
                        <input type="date" class="form-control" id="expected_delivery_date" name="expected_delivery_date">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="shipping_cost" class="form-label">{% trans "تكلفة الشحن" %}</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="shipping_cost" name="shipping_cost" value="0" inputmode="numeric" pattern="[0-9]+(\.[0-9]+)?">
                            <span class="input-group-text">ر.س</span>
                        </div>
                        <div class="form-text text-danger" id="shipping_cost_error" style="display: none;">{% trans "الرجاء إدخال رقم صحيح" %}</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">{% trans "ملاحظات" %}</label>
                    <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                </div>
            </div>

            <!-- Products Section -->
            <div class="form-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="form-section-title mb-0">{% trans "المنتجات" %}</h5>
                    <a href="#" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="fas fa-plus me-1"></i> {% trans "إضافة منتج" %}
                    </a>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="productsTable">
                        <thead>
                            <tr>
                                <th width="50px">{% trans "صورة" %}</th>
                                <th>{% trans "المنتج" %}</th>
                                <th width="100px">{% trans "الكمية" %}</th>
                                <th width="150px">{% trans "سعر الوحدة" %}</th>
                                <th width="150px">{% trans "المجموع" %}</th>
                                <th width="50px">{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            {% if initial_items %}
                                {% for item in initial_items %}
                                <tr class="product-row">
                                    <td>
                                        {% if item.product.image %}
                                        <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image-small">
                                        {% else %}
                                        <div class="text-center">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <input type="hidden" name="product_ids[]" value="{{ item.product.id }}">
                                        <strong>{{ item.product.code }}</strong> - {{ item.product.name }}
                                    </td>
                                    <td>
                                        <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="{{ item.quantity }}" min="1" required>
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="{{ item.unit_price }}" step="0.01" min="0" required>
                                            <span class="input-group-text">ر.س</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="subtotal">{{ item.quantity|mul:item.unit_price }}</span> ر.س
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger remove-product">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr id="noProductsRow">
                                    <td colspan="6" class="text-center">{% trans "لم يتم إضافة منتجات بعد" %}</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "يمكنك إضافة المنتجات باستخدام زر 'إضافة منتج' أعلاه. تأكد من إضافة منتج واحد على الأقل قبل حفظ طلب الشراء." %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Summary Section -->
            <div class="card shadow summary-card">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "ملخص الطلب" %}</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "المجموع الفرعي:" %}</span>
                            <span id="subtotalSummary">0.00</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "ضريبة القيمة المضافة (15%):" %}</span>
                            <span id="taxSummary">0.00</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "تكلفة الشحن:" %}</span>
                            <span id="shippingSummary">0.00</span>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <strong>{% trans "المجموع الكلي:" %}</strong>
                            <strong id="totalSummary">0.00</strong>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="fas fa-save me-1"></i> {% trans "حفظ طلب الشراء" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">{% trans "إضافة منتج" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="productSearch" class="form-label">{% trans "بحث عن منتج" %}</label>
                    <input type="text" class="form-control" id="productSearch" placeholder="{% trans 'اكتب اسم المنتج أو الكود...' %}">
                </div>

                <div class="table-responsive mt-3">
                    <table class="table table-bordered table-hover" id="productsSearchTable">
                        <thead>
                            <tr>
                                <th>{% trans "الكود" %}</th>
                                <th>{% trans "اسم المنتج" %}</th>
                                <th>{% trans "الفئة" %}</th>
                                <th>{% trans "الكمية المتوفرة" %}</th>
                                <th>{% trans "سعر الشراء" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>{{ product.code }}</td>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category.name }}</td>
                                <td>{{ product.quantity }}</td>
                                <td>{{ product.purchase_price }} ر.س</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary select-product"
                                            data-id="{{ product.id }}"
                                            data-code="{{ product.code }}"
                                            data-name="{{ product.name }}"
                                            data-price="{{ product.purchase_price }}"
                                            data-image="{% if product.image %}{{ product.image.url }}{% endif %}">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function saveDraft() {
        // حفظ النموذج كمسودة
        const formData = new FormData(document.getElementById('purchaseForm'));
        formData.append('save_as_draft', 'true');

        // إشعار المستخدم
        if (typeof toastr !== 'undefined') {
            toastr.info("{% trans 'جاري حفظ المسودة...' %}");
        }

        // محاكاة حفظ المسودة
        setTimeout(() => {
            if (typeof toastr !== 'undefined') {
                toastr.success("{% trans 'تم حفظ المسودة بنجاح' %}");
            }
        }, 1000);
    }

    function loadTemplate() {
        // تحميل قالب محفوظ
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const template = JSON.parse(e.target.result);

                        // تطبيق بيانات القالب
                        if (template.supplier) document.getElementById('supplier').value = template.supplier;
                        if (template.expected_delivery_date) document.getElementById('expected_delivery_date').value = template.expected_delivery_date;
                        if (template.notes) document.getElementById('notes').value = template.notes;

                        if (typeof toastr !== 'undefined') {
                            toastr.success("{% trans 'تم تحميل القالب بنجاح' %}");
                        }
                    } catch (error) {
                        if (typeof toastr !== 'undefined') {
                            toastr.error("{% trans 'خطأ في قراءة ملف القالب' %}");
                        }
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    function saveAsTemplate() {
        // حفظ النموذج الحالي كقالب
        const template = {
            supplier: document.getElementById('supplier').value,
            expected_delivery_date: document.getElementById('expected_delivery_date').value,
            notes: document.getElementById('notes').value,
            created_date: new Date().toISOString()
        };

        const dataStr = JSON.stringify(template, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'purchase_template_' + new Date().toISOString().split('T')[0] + '.json';
        link.click();
        URL.revokeObjectURL(url);

        if (typeof toastr !== 'undefined') {
            toastr.success("{% trans 'تم حفظ القالب بنجاح' %}");
        }
    }

    function clearForm() {
        if (confirm("{% trans 'هل أنت متأكد من مسح جميع البيانات؟' %}")) {
            document.getElementById('purchaseForm').reset();
            document.getElementById('productsTableBody').innerHTML = '<tr id="noProductsRow"><td colspan="6" class="text-center text-muted">{% trans "لم يتم إضافة أي منتجات بعد" %}</td></tr>';
            updateSummary();

            if (typeof toastr !== 'undefined') {
                toastr.success("{% trans 'تم مسح النموذج بنجاح' %}");
            }
        }
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'purchases:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // Initialize DataTable for products search
        var searchTable = $('#productsSearchTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "pageLength": 5
        });

        // Product search
        $('#productSearch').keyup(function() {
            searchTable.search($(this).val()).draw();
        });

        // Modal is now handled by data-bs-toggle and data-bs-target attributes

        // Select Product
        $(document).on('click', '.select-product', function() {
            var productId = $(this).data('id');
            var productCode = $(this).data('code');
            var productName = $(this).data('name');
            var productPrice = $(this).data('price');
            var productImage = $(this).data('image');

            // Remove "no products" row if exists
            $('#noProductsRow').remove();

            // Add product to table
            var newRow = `
                <tr class="product-row">
                    <td>
                        ${productImage ?
                            `<img src="${productImage}" alt="${productName}" class="product-image-small">` :
                            `<div class="text-center"><i class="fas fa-image text-muted"></i></div>`
                        }
                    </td>
                    <td>
                        <input type="hidden" name="product_ids[]" value="${productId}">
                        <strong>${productCode}</strong> - ${productName}
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm quantity-input" name="quantities[]" value="1" min="1" required>
                    </td>
                    <td>
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control form-control-sm unit-price-input" name="unit_prices[]" value="${productPrice}" step="0.01" min="0" required>
                            <span class="input-group-text">ر.س</span>
                        </div>
                    </td>
                    <td>
                        <span class="subtotal">${productPrice}</span> ر.س
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-product">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            $('#productsTableBody').append(newRow);

            // Close modal
            $('#addProductModal').modal('hide');

            // Update summary
            updateSummary();
        });

        // Remove Product
        $(document).on('click', '.remove-product', function() {
            $(this).closest('tr').remove();

            // If no products left, add "no products" row
            if ($('#productsTableBody tr').length === 0) {
                $('#productsTableBody').html('<tr id="noProductsRow"><td colspan="6" class="text-center">لم يتم إضافة منتجات بعد</td></tr>');
            }

            // Update summary
            updateSummary();
        });

        // Update subtotal when quantity or unit price changes
        $(document).on('input', '.quantity-input, .unit-price-input', function() {
            var row = $(this).closest('tr');
            var quantity = parseFloat(row.find('.quantity-input').val()) || 0;
            var unitPrice = parseFloat(row.find('.unit-price-input').val()) || 0;
            var subtotal = quantity * unitPrice;

            row.find('.subtotal').text(subtotal.toFixed(2));

            // Update summary
            updateSummary();
        });

        // Update shipping cost with better validation
        $('#shipping_cost').on('input', function() {
            var value = $(this).val();
            var isValid = /^\d*\.?\d*$/.test(value);

            if (isValid) {
                $('#shipping_cost_error').hide();
                updateSummary();
            } else {
                $('#shipping_cost_error').show();
            }
        });

        // Make sure shipping cost updates properly on change
        $('#shipping_cost').change(function() {
            var value = $(this).val();
            if (value === '' || isNaN(parseFloat(value))) {
                $(this).val('0');
            }
            updateSummary();
        });

        // Update summary
        function updateSummary() {
            var subtotal = 0;

            // Calculate subtotal
            $('.subtotal').each(function() {
                subtotal += parseFloat($(this).text()) || 0;
            });

            // Calculate tax
            var taxRate = 15; // 15% VAT
            var tax = subtotal * (taxRate / 100);

            // Get shipping cost - make sure to handle empty or invalid values
            var shippingInput = $('#shipping_cost').val();
            var shipping = (shippingInput && !isNaN(parseFloat(shippingInput))) ? parseFloat(shippingInput) : 0;

            // Calculate total
            var total = subtotal + tax + shipping;

            // Update summary
            $('#subtotalSummary').text(subtotal.toFixed(2));
            $('#taxSummary').text(tax.toFixed(2));
            $('#shippingSummary').text(shipping.toFixed(2));
            $('#totalSummary').text(total.toFixed(2));
        }

        // Form validation
        $('#purchaseForm').submit(function(e) {
            if ($('#productsTableBody tr').not('#noProductsRow').length === 0) {
                e.preventDefault();
                alert('{% trans "يرجى إضافة منتج واحد على الأقل" %}');
                return false;
            }

            return true;
        });

        // Initialize summary
        updateSummary();
    });
</script>
{% endblock %}
