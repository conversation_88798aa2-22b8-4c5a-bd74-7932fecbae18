{"classifiers": ["Operating System :: OS Independent", "Programming Language :: Python", "License :: OSI Approved :: BSD License", "Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "Topic :: Software Development :: Libraries :: Python Modules", "Topic :: Office/Business :: Financial :: Spreadsheet", "Topic :: Database", "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: CGI Tools/Libraries", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6"], "download_url": "https://pypi.python.org/pypi/xlwt", "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "http://www.python-excel.org/"}}}, "generator": "bdist_wheel (0.29.0)", "keywords": ["xls", "excel", "spreadsheet", "workbook", "worksheet", "pyExcelerator"], "license": "BSD", "metadata_version": "2.0", "name": "xlwt", "platform": "Platform Independent", "summary": "Library to create spreadsheet files compatible with MS Excel 97/2000/XP/2003 XLS files, on any platform, with Python 2.6, 2.7, 3.3+", "version": "1.3.0"}