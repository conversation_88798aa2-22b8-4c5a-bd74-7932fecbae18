// Custom JavaScript for Auto Parts POS System

$(document).ready(function() {
    // تعطيل ميزة التمرير للقوائم المنسدلة العادية
    $('.normal-select').on('focus mousedown click', function(e) {
        $(this).attr('size', '1');
        $(this).css('overflow', 'visible');
    });

    // Apply max-height and scrolling to select elements with more than 10 options
    $('select').not('.normal-select').each(function() {
        if ($(this).find('option').length > 10) {
            $(this).addClass('select-scrollable');

            // تطبيق ميزة التمرير عند التركيز - بعد العنصر رقم 10
            $(this).on('focus', function() {
                // تحديد عدد العناصر المرئية (10) ثم إضافة التمرير بعدها
                const visibleOptions = 10;
                const totalOptions = $(this).find('option').length;

                if (totalOptions > visibleOptions) {
                    $(this).attr('size', visibleOptions);

                    // تحديد موضع التمرير ليبدأ بعد العنصر رقم 10
                    const optionHeight = $(this).find('option').first().outerHeight();
                    if (optionHeight) {
                        // تعيين موضع التمرير بعد العنصر رقم 10
                        setTimeout(() => {
                            // حساب ارتفاع 10 عناصر وتعيين موضع التمرير بعدها
                            const scrollPosition = optionHeight * 10;
                            $(this).scrollTop(scrollPosition);
                        }, 10);
                    }
                } else {
                    $(this).attr('size', totalOptions);
                }

                // إزالة الإطار الأزرق عند التركيز
                $(this).css('outline', 'none');
            });

            // إعادة الحجم إلى الوضع الطبيعي عند فقدان التركيز أو التغيير
            $(this).on('blur change', function() {
                $(this).attr('size', '1');
            });
        }
    });
});
