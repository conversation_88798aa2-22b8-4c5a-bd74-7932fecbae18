<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال حراري - {{ sale.invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            font-size: 10px;
            line-height: 1.2;
            color: #000;
            direction: rtl;
            text-align: center;
            width: 72mm;
            margin: 0 auto;
            padding: 2mm;
            background: white;
        }

        .receipt-container {
            width: 72mm;
            max-width: 72mm;
            margin: 0 auto;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 8px;
            border-bottom: 1px dashed #000;
            padding-bottom: 4px;
        }

        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .company-info {
            font-size: 9px;
            margin-bottom: 1px;
        }

        .sale-type {
            font-size: 10px;
            font-weight: bold;
            margin-top: 2px;
        }

        .invoice-info {
            margin: 6px 0;
            font-size: 9px;
            text-align: right;
        }

        .invoice-info div {
            margin-bottom: 1px;
        }

        .items-section {
            margin: 6px 0;
        }

        .items-header {
            border-bottom: 1px solid #000;
            border-top: 1px solid #000;
            padding: 2px 0;
            font-weight: bold;
            font-size: 8px;
            text-align: center;
        }

        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-row {
            border-bottom: 1px dotted #666;
            padding: 2px 0;
            font-size: 9px;
            text-align: right;
        }

        .item-name {
            font-weight: bold;
            margin-bottom: 1px;
            font-size: 9px;
        }

        .item-line {
            display: flex;
            justify-content: space-between;
            font-size: 8px;
        }

        .summary {
            margin-top: 6px;
            border-top: 1px solid #000;
            padding-top: 3px;
            font-size: 9px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1px;
            text-align: right;
        }

        .total-section {
            margin: 4px 0;
            border: 1px solid #000;
            padding: 3px;
        }

        .total-table {
            width: 100%;
            font-size: 8px;
        }

        .total-table td {
            padding: 1px 2px;
            border: 1px solid #000;
            text-align: center;
        }

        .total-header {
            font-weight: bold;
            background-color: #f0f0f0;
        }

        .total-amount {
            font-weight: bold;
            font-size: 9px;
        }

        .footer {
            text-align: center;
            margin-top: 8px;
            border-top: 1px dashed #000;
            padding-top: 4px;
            font-size: 8px;
        }

        .thank-you {
            font-size: 9px;
            font-weight: bold;
            margin: 3px 0;
        }

        @media print {
            @page {
                size: 80mm auto;
                margin: 0mm;
            }

            body {
                width: 72mm;
                margin: 0;
                padding: 2mm;
                font-size: 9px;
            }

            .receipt-container {
                width: 72mm;
                max-width: 72mm;
            }

            table {
                font-size: 8px !important;
            }

            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="header">
            <div class="company-name">{{ company_info.name|default:"اسم المحل" }}</div>
            {% if company_info.phone %}
            <div class="company-info">{{ company_info.phone }}</div>
            {% endif %}
        </div>

        <!-- Invoice Info -->
        <div class="invoice-info">
            <div>فاتورة: {{ sale.invoice_number }}</div>
            <div>التاريخ: {{ sale.date|date:"d/m/Y H:i" }}</div>
            <div>العميل: {{ sale.customer.name }}</div>
        </div>

        <!-- Products Table -->
        <div style="margin: 8px 0; border-top: 1px solid #000; border-bottom: 1px solid #000;">
            <table style="width: 100%; border-collapse: collapse; font-size: 9px;">
                <thead>
                    <tr style="border-bottom: 1px solid #000;">
                        <th style="padding: 2px; text-align: center; width: 12%;">الكمية</th>
                        <th style="padding: 2px; text-align: right; width: 40%;">اسم المنتج</th>
                        <th style="padding: 2px; text-align: center; width: 24%;">سعر الوحدة</th>
                        <th style="padding: 2px; text-align: center; width: 24%;">الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in items %}
                    <tr style="border-bottom: 1px dotted #666;">
                        <td style="padding: 2px; text-align: center;">{{ item.quantity }}</td>
                        <td style="padding: 2px; text-align: right; font-size: 9px;">
                            {% if item.product.code %}
                            <strong>{{ item.product.code }}</strong> - 
                            {% endif %}
                            {{ item.product.name }}
                        </td>
                        <td style="padding: 2px; text-align: center;">{{ item.unit_price|floatformat:0 }}</td>
                        <td style="padding: 2px; text-align: center;">{{ item.subtotal|floatformat:0 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Summary -->
        <div style="margin: 8px 0; padding: 4px 0; border-top: 1px solid #000;">
            <div style="text-align: center; font-weight: bold; font-size: 12px; margin: 4px 0;">
                المجموع: {{ sale.total_amount|floatformat:2 }} درهم
            </div>

            {% if total_paid > 0 %}
            <div style="text-align: center; font-size: 10px; margin: 2px 0;">
                المدفوع: {{ total_paid|floatformat:2 }} درهم
            </div>
            {% endif %}

            {% if remaining_amount > 0 %}
            <div style="text-align: center; font-size: 10px; margin: 2px 0; font-weight: bold;">
                المتبقي: {{ remaining_amount|floatformat:2 }} درهم
            </div>
            {% endif %}
        </div>

        <!-- Footer -->
        <div class="footer">
            <div style="margin: 6px 0; border-top: 1px dashed #000; padding-top: 4px;">
                <div style="font-size: 10px; margin-bottom: 2px;">شكراً لتعاملكم معنا</div>
                <div style="font-size: 8px;">{{ sale.date|date:"d/m/Y H:i" }}</div>
            </div>
        </div>
    </div>
    
    <!-- Print Button (hidden in print) -->
    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; font-size: 14px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            طباعة الإيصال
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; font-size: 14px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
            إغلاق
        </button>
    </div>
</body>
</html>
