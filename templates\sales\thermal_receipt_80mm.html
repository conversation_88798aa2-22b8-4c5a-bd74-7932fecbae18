<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال حراري - {{ sale.invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
            color: #000;
            direction: rtl;
            text-align: right;
            width: 80mm;
            margin: 0 auto;
            padding: 2mm;
            background: white;
        }
        
        .receipt-container {
            width: 100%;
            max-width: 80mm;
        }
        
        .header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 5px;
        }
        
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .company-info {
            font-size: 10px;
            margin-bottom: 2px;
        }
        
        .invoice-info {
            margin: 8px 0;
            font-size: 11px;
        }
        
        .invoice-info div {
            margin-bottom: 2px;
        }
        
        .items-table {
            width: 100%;
            margin: 8px 0;
            border-collapse: collapse;
        }
        
        .items-header {
            border-bottom: 1px solid #000;
            border-top: 1px solid #000;
            padding: 3px 0;
            font-weight: bold;
            font-size: 10px;
        }
        
        .item-row {
            border-bottom: 1px dotted #666;
            padding: 2px 0;
            font-size: 10px;
        }
        
        .item-name {
            font-weight: bold;
            margin-bottom: 1px;
        }
        
        .item-details {
            display: flex;
            justify-content: space-between;
            font-size: 9px;
        }
        
        .summary {
            margin-top: 8px;
            border-top: 1px solid #000;
            padding-top: 5px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
            font-size: 11px;
        }
        
        .summary-row.total {
            font-weight: bold;
            font-size: 12px;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 3px 0;
            margin: 5px 0;
        }
        
        .payment-info {
            margin-top: 8px;
            border: 1px solid #000;
            padding: 5px;
        }
        
        .payment-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
            font-size: 10px;
        }
        
        .remaining-amount {
            font-weight: bold;
            font-size: 12px;
            color: #000;
        }
        
        .footer {
            text-align: center;
            margin-top: 10px;
            border-top: 1px dashed #000;
            padding-top: 5px;
            font-size: 9px;
        }
        
        .thank-you {
            font-size: 11px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        @media print {
            @page {
                size: 80mm auto;
                margin: 0mm;
            }
            
            body {
                width: 80mm;
                margin: 0;
                padding: 2mm;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="header">
            <div class="company-name">{{ company_info.name|default:"اسم الشركة" }}</div>
            {% if company_info.phone %}
            <div class="company-info">هاتف: {{ company_info.phone }}</div>
            {% endif %}
            {% if company_info.address %}
            <div class="company-info">{{ company_info.address }}</div>
            {% endif %}
            <div class="company-info">فاتورة مبيعات</div>
        </div>
        
        <!-- Invoice Info -->
        <div class="invoice-info">
            <div>رقم الفاتورة: {{ sale.invoice_number }}</div>
            <div>التاريخ: {{ sale.date|date:"Y/m/d" }}</div>
            <div>الوقت: {{ sale.date|date:"H:i" }}</div>
            <div>العميل: {{ sale.customer.name }}</div>
            {% if sale.customer.phone %}
            <div>هاتف: {{ sale.customer.phone }}</div>
            {% endif %}
        </div>
        
        <!-- Items Header -->
        <div class="items-header">
            <table style="width: 100%;">
                <tr>
                    <td style="text-align: right; width: 40%;">المنتج</td>
                    <td style="text-align: center; width: 15%;">الكمية</td>
                    <td style="text-align: center; width: 20%;">السعر</td>
                    <td style="text-align: left; width: 25%;">المجموع</td>
                </tr>
            </table>
        </div>
        
        <!-- Items -->
        {% for item in items %}
        <div class="item-row">
            <div class="item-name">{{ item.product.name }}</div>
            <div class="item-details">
                <span>{{ item.quantity }} × {{ item.price|floatformat:2 }}</span>
                <span>{{ item.total_price|floatformat:2 }} د.م</span>
            </div>
        </div>
        {% endfor %}
        
        <!-- Summary -->
        <div class="summary">
            <div class="summary-row">
                <span>المجموع الفرعي:</span>
                <span>{{ sale.subtotal|floatformat:2 }} د.م</span>
            </div>
            
            {% if sale.discount and sale.discount > 0 %}
            <div class="summary-row">
                <span>الخصم:</span>
                <span>{{ sale.discount|floatformat:2 }} د.م</span>
            </div>
            {% endif %}
            
            {% if sale.tax_amount and sale.tax_amount > 0 %}
            <div class="summary-row">
                <span>الضريبة:</span>
                <span>{{ sale.tax_amount|floatformat:2 }} د.م</span>
            </div>
            {% endif %}
            
            <div class="summary-row total">
                <span>المجموع الكلي:</span>
                <span>{{ sale.total_amount|floatformat:2 }} د.م</span>
            </div>
        </div>
        
        <!-- Payment Info -->
        <div class="payment-info">
            <div class="payment-row">
                <span>المبلغ المدفوع:</span>
                <span>{{ total_paid|floatformat:2 }} د.م</span>
            </div>
            <div class="payment-row">
                <span>الباقي الجديد:</span>
                <span class="remaining-amount">{{ remaining_amount|floatformat:2 }} د.م</span>
            </div>
            <div class="payment-row">
                <span>الباقي الإجمالي:</span>
                <span class="remaining-amount">{{ remaining_amount|floatformat:2 }} د.م</span>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="thank-you">شكراً لتعاملكم معنا</div>
            <div>{{ company_info.name|default:"الشركة" }}</div>
            <div>تاريخ الطباعة: {% now "Y/m/d H:i" %}</div>
        </div>
    </div>
    
    <!-- Print Button (hidden in print) -->
    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; font-size: 14px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            طباعة الإيصال
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; font-size: 14px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
            إغلاق
        </button>
    </div>
</body>
</html>
