// تهيئة القائمة العلوية
document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل Bootstrap بالكامل
    setTimeout(function() {
        initializeNavbarDropdowns();
    }, 100);
});

// تهيئة القوائم المنسدلة في الشريط العلوي
function initializeNavbarDropdowns() {
    // Re-initialize all Bootstrap dropdowns
    const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
    const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Ensure dropdowns work properly
    $('.dropdown-toggle').off('click.bs.dropdown').on('click', function(e) {
        e.preventDefault();
        const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
        dropdown.toggle();
    });

    // Fix navbar dropdown items layout
    fixNavbarDropdownLayout();

    // Fix admin dropdown position
    fixAdminDropdownPosition();
}

// إصلاح تخطيط عناصر القائمة المنسدلة
function fixNavbarDropdownLayout() {
    $('.navbar .dropdown-item').each(function() {
        const $item = $(this);
        const $icon = $item.find('i');
        const text = $item.text().trim();

        if ($icon.length > 0 && text) {
            // إعادة ترتيب المحتوى - الأيقونة أولاً ثم النص
            $item.html(`
                <i class="${$icon.attr('class')}"></i>
                <span class="dropdown-text">${text}</span>
            `);
        }
    });
}

// إصلاح موضع قائمة admin لمنع التمرير الأفقي
function fixAdminDropdownPosition() {
    const userDropdown = document.getElementById('userDropdown');
    if (userDropdown) {
        const dropdownMenu = userDropdown.nextElementSibling;
        if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
            // إضافة class خاص لقائمة المستخدم
            dropdownMenu.classList.add('dropdown-menu-end');

            // ضبط الموضع
            dropdownMenu.style.right = '0';
            dropdownMenu.style.left = 'auto';
            dropdownMenu.style.transform = 'none';
            dropdownMenu.style.minWidth = '160px';
            dropdownMenu.style.maxWidth = '180px';
        }
    }

    // منع التمرير الأفقي في الصفحة
    document.body.style.overflowX = 'hidden';
}

// معالج النقر على القوائم المنسدلة
function handleDropdownClick(event) {
    event.preventDefault();
    event.stopPropagation();

    const toggle = event.currentTarget;
    const dropdownInstance = bootstrap.Dropdown.getInstance(toggle);

    if (dropdownInstance) {
        dropdownInstance.toggle();
    } else {
        // إنشاء instance جديد إذا لم يكن موجوداً
        const newDropdown = new bootstrap.Dropdown(toggle);
        newDropdown.toggle();
    }
}

// إغلاق القوائم المنسدلة عند النقر خارجها
document.addEventListener('click', function(event) {
    const dropdownMenus = document.querySelectorAll('.dropdown-menu.show');
    dropdownMenus.forEach(function(menu) {
        const toggle = menu.previousElementSibling;
        if (toggle && !toggle.contains(event.target) && !menu.contains(event.target)) {
            const dropdownInstance = bootstrap.Dropdown.getInstance(toggle);
            if (dropdownInstance) {
                dropdownInstance.hide();
            }
        }
    });
});

// إعادة تهيئة القوائم المنسدلة عند الحاجة
window.reinitializeDropdowns = function() {
    initializeNavbarDropdowns();
};
