{% extends 'invoice_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "فاتورة" %} #{{ sale.invoice_number }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/invoice-pdf.css' %}">
<link rel="stylesheet" href="{% static 'fonts/arabic-fonts.css' %}">
<link rel="stylesheet" href="{% static 'css/mixed-text.css' %}">
<style>
    /* أنماط إضافية محددة لهذا القالب */

    /* تحسينات إضافية للنصوص العربية */
    body {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* تحسين المسافات بين الكلمات */
    p, td, th, span, div {
        word-spacing: 0.1em;
        letter-spacing: 0.02em;
    }

    /* تحسين عرض الأرقام */
    .number {
        font-family: 'Tajawal', 'Tahoma', monospace;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="invoice-container arabic-text smooth-text pdf-optimized">
    <!-- محتوى الفاتورة المحسن خصيصاً لـ PDF -->
    {% include 'sales/invoice_content_pdf.html' %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// معالجة النصوص المختلطة (عربي + فرنسي/إنجليزي) لتحسين العرض في PDF
document.addEventListener('DOMContentLoaded', function() {
    function processMixedText() {
        const mixedTextElements = document.querySelectorAll('.mixed-text-content');

        mixedTextElements.forEach(function(element) {
            let text = element.innerHTML;

            // تحسين عرض النصوص اللاتينية المضمنة في النص العربي
            // البحث عن النصوص اللاتينية وتطبيق التنسيق المناسب
            text = text.replace(/([a-zA-Z0-9\s\.,;:!?\-\(\)]+)/g, function(match) {
                // تجاهل النصوص القصيرة جداً (أقل من 3 أحرف)
                if (match.trim().length < 3) return match;

                // تطبيق تنسيق خاص للنصوص اللاتينية
                return '<span class="latin-text" dir="ltr">' + match + '</span>';
            });

            // تحسين عرض الأرقام
            text = text.replace(/(\d+)/g, '<span class="number">$1</span>');

            // تحسين عرض علامات الترقيم
            text = text.replace(/([.,;:!?])/g, '<span class="symbol">$1</span>');

            element.innerHTML = text;
        });
    }

    // تشغيل المعالجة
    processMixedText();

    // إضافة تحسينات إضافية للعرض
    const notesContainers = document.querySelectorAll('.notes-container');
    notesContainers.forEach(function(container) {
        container.style.textRendering = 'optimizeLegibility';
        container.style.webkitFontSmoothing = 'antialiased';
        container.style.mozOsxFontSmoothing = 'grayscale';
    });
});
</script>
{% endblock %}