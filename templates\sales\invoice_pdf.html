{% extends 'invoice_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "فاتورة" %} #{{ sale.invoice_number }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/pdf-fullsize.css' %}">
<link rel="stylesheet" href="{% static 'fonts/arabic-fonts.css' %}">
<link rel="stylesheet" href="{% static 'css/mixed-text.css' %}">
<style>
    /* أنماط إضافية محددة لهذا القالب - تحسين حجم PDF */

    /* إعادة تعيين الحجم لملء الصفحة بالكامل */
    html, body {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        font-size: 14pt !important;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* حاوي الفاتورة يملأ الصفحة */
    .invoice-container {
        width: 100% !important;
        max-width: none !important;
        min-height: 100vh !important;
        margin: 0 !important;
        padding: 20mm !important;
        background: white !important;
        box-sizing: border-box !important;
    }

    /* تحسين أحجام الخطوط */
    h1 { font-size: 24pt !important; }
    h2 { font-size: 20pt !important; }
    h3 { font-size: 18pt !important; }
    h4 { font-size: 16pt !important; }
    h5 { font-size: 14pt !important; }
    h6 { font-size: 12pt !important; }

    p, td, th, span, div {
        font-size: 12pt !important;
        word-spacing: 0.1em;
        letter-spacing: 0.02em;
        line-height: 1.6 !important;
    }

    /* تحسين الجداول */
    table {
        width: 100% !important;
        font-size: 12pt !important;
        margin-bottom: 20px !important;
    }

    th, td {
        padding: 10px !important;
        font-size: 12pt !important;
        border: 1px solid #333 !important;
    }

    th {
        background-color: #f0f0f0 !important;
        font-weight: bold !important;
    }

    /* تحسين عرض الأرقام */
    .number, .arabic-number {
        font-family: 'Tajawal', 'Tahoma', monospace;
        font-weight: 600;
        font-size: 12pt !important;
    }

    /* تحسين الهوامش والمسافات */
    .row {
        margin-bottom: 15px !important;
    }

    .col-md-6 {
        width: 50% !important;
        float: right !important;
        padding: 0 10px !important;
    }

    /* تحسين رأس الفاتورة */
    .invoice-header {
        border-bottom: 3px solid #333 !important;
        padding-bottom: 20px !important;
        margin-bottom: 25px !important;
    }

    .invoice-title {
        font-size: 24pt !important;
        font-weight: bold !important;
        margin-bottom: 10px !important;
    }

    /* تحسين قوائم التعريف */
    dl {
        margin-bottom: 15px !important;
    }

    dt {
        font-weight: bold !important;
        margin-bottom: 5px !important;
        font-size: 12pt !important;
    }

    dd {
        margin-bottom: 10px !important;
        font-size: 12pt !important;
    }

    /* إعدادات الطباعة المحسنة */
    @page {
        size: A4 !important;
        margin: 15mm !important;
    }

    @media print {
        html, body {
            width: 210mm !important;
            height: 297mm !important;
        }

        .invoice-container {
            width: 100% !important;
            height: 100% !important;
            padding: 15mm !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="invoice-container arabic-text smooth-text pdf-optimized">
    <!-- محتوى الفاتورة المحسن خصيصاً لـ PDF -->
    {% include 'sales/invoice_content_pdf.html' %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// معالجة النصوص المختلطة (عربي + فرنسي/إنجليزي) لتحسين العرض في PDF
document.addEventListener('DOMContentLoaded', function() {
    function processMixedText() {
        const mixedTextElements = document.querySelectorAll('.mixed-text-content');

        mixedTextElements.forEach(function(element) {
            let text = element.innerHTML;

            // تحسين عرض النصوص اللاتينية المضمنة في النص العربي
            // البحث عن النصوص اللاتينية وتطبيق التنسيق المناسب
            text = text.replace(/([a-zA-Z0-9\s\.,;:!?\-\(\)]+)/g, function(match) {
                // تجاهل النصوص القصيرة جداً (أقل من 3 أحرف)
                if (match.trim().length < 3) return match;

                // تطبيق تنسيق خاص للنصوص اللاتينية
                return '<span class="latin-text" dir="ltr">' + match + '</span>';
            });

            // تحسين عرض الأرقام
            text = text.replace(/(\d+)/g, '<span class="number">$1</span>');

            // تحسين عرض علامات الترقيم
            text = text.replace(/([.,;:!?])/g, '<span class="symbol">$1</span>');

            element.innerHTML = text;
        });
    }

    // تشغيل المعالجة
    processMixedText();

    // إضافة تحسينات إضافية للعرض
    const notesContainers = document.querySelectorAll('.notes-container');
    notesContainers.forEach(function(container) {
        container.style.textRendering = 'optimizeLegibility';
        container.style.webkitFontSmoothing = 'antialiased';
        container.style.mozOsxFontSmoothing = 'grayscale';
    });
});
</script>
{% endblock %}