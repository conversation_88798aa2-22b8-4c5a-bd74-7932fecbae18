{% extends 'invoice_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "فاتورة" %} #{{ sale.invoice_number }} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/invoice-pdf.css' %}">
<link rel="stylesheet" href="{% static 'fonts/arabic-fonts.css' %}">
<style>
    /* أنماط إضافية محددة لهذا القالب */

    /* تحسينات إضافية للنصوص العربية */
    body {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* تحسين المسافات بين الكلمات */
    p, td, th, span, div {
        word-spacing: 0.1em;
        letter-spacing: 0.02em;
    }

    /* تحسين عرض الأرقام */
    .number {
        font-family: 'Tajawal', 'Tahoma', monospace;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="invoice-container arabic-text smooth-text pdf-optimized">
    <!-- محتوى الفاتورة المحسن خصيصاً لـ PDF -->
    {% include 'sales/invoice_content_pdf.html' %}
</div>
{% endblock %}

{% block extra_js %}
<!-- Remove all JavaScript for PDF version -->
{% endblock %}