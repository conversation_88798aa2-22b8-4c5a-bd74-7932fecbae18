<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #{{ sale.invoice_number }} - للطباعة</title>
    {% load static %}
    {% load sales_extras %}
    
    <style>
        /* إعدادات أساسية للطباعة */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html {
            font-size: 16px;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', 'Tahoma', 'Arial Unicode MS', sans-serif;
            font-size: 14pt;
            line-height: 1.6;
            color: #000;
            background: white;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20mm;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 0;
        }
        
        /* العناوين */
        h1 { font-size: 28pt; font-weight: bold; margin-bottom: 15px; text-align: center; }
        h2 { font-size: 24pt; font-weight: bold; margin-bottom: 12px; text-align: center; }
        h3 { font-size: 20pt; font-weight: bold; margin-bottom: 10px; }
        h4 { font-size: 18pt; font-weight: bold; margin-bottom: 8px; }
        h5 { font-size: 16pt; font-weight: bold; margin-bottom: 8px; }
        
        /* الفقرات */
        p {
            font-size: 14pt;
            line-height: 1.6;
            margin-bottom: 12px;
        }
        
        /* الجداول */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            font-size: 13pt;
            border: 3px solid #007bff !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        th, td {
            padding: 12px 8px;
            border: 1px solid #007bff !important;
            text-align: center;
            vertical-align: middle;
            font-size: 13pt;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        th {
            background-color: #e3f2fd !important;
            color: #1976d2 !important;
            font-weight: bold;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        /* الصفوف */
        .row {
            width: 100%;
            margin-bottom: 20px;
            display: table;
            clear: both;
        }
        
        .col-md-6 {
            width: 50%;
            float: right;
            padding: 0 15px;
            display: table-cell;
            vertical-align: top;
        }
        
        /* رأس الفاتورة */
        .invoice-header {
            border-bottom: 4px solid #007bff !important;
            padding-bottom: 25px;
            margin-bottom: 30px;
            text-align: center;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
            padding: 25px !important;
            border-radius: 10px 10px 0 0 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .invoice-title {
            font-size: 28pt;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        /* قوائم التعريف */
        dt {
            font-weight: bold;
            font-size: 14pt;
            margin-bottom: 8px;
            display: inline-block;
            width: 40%;
            vertical-align: top;
        }
        
        dd {
            font-size: 14pt;
            margin-bottom: 12px;
            display: inline-block;
            width: 58%;
            vertical-align: top;
            padding-right: 10px;
        }
        
        /* الأرقام */
        .arabic-number {
            font-family: 'Tajawal', 'Tahoma', monospace;
            font-weight: 600;
            direction: ltr;
            display: inline-block;
        }
        
        /* معلومات الدفع */
        .payment-info {
            background-color: #f8f9fa;
            border: 2px solid #007bff;
            padding: 20px;
            margin-bottom: 25px;
            border-radius: 8px;
        }
        
        /* ملخص الفاتورة */
        .invoice-summary {
            background-color: #f8f9fa;
            border: 2px solid #007bff;
            padding: 25px;
            margin-bottom: 30px;
            border-radius: 8px;
        }
        
        .invoice-summary .d-flex {
            display: table;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .invoice-summary .d-flex span:first-child {
            display: table-cell;
            width: 70%;
            font-weight: 600;
            text-align: right;
        }
        
        .invoice-summary .d-flex span:last-child {
            display: table-cell;
            width: 30%;
            text-align: left;
            font-weight: 700;
        }
        
        .total-amount {
            font-size: 18pt;
            font-weight: 700;
            color: #007bff !important;
            background-color: rgba(0, 123, 255, 0.1) !important;
            padding: 5px 10px !important;
            border-radius: 5px !important;
            border: 2px solid #007bff !important;
        }
        
        /* منطقة التوقيع */
        .signature-area {
            margin-top: 40px;
            display: table;
            width: 100%;
        }
        
        .signature-box {
            display: table-cell;
            width: 33.333%;
            text-align: center;
            padding: 0 20px;
            vertical-align: top;
        }
        
        .signature-line {
            border-top: 2px solid #000;
            margin-top: 60px;
            margin-bottom: 10px;
        }
        
        /* QR Code */
        .qr-code {
            text-align: center;
            margin: 25px auto;
        }
        
        .qr-code img {
            max-width: 120px;
            max-height: 120px;
            border: 1px solid #ddd;
            padding: 5px;
        }
        
        /* أزرار الطباعة */
        .print-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .print-controls button {
            margin: 5px;
            padding: 8px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12pt;
        }
        
        .btn-print {
            background-color: #007bff;
            color: white;
        }
        
        .btn-close {
            background-color: #6c757d;
            color: white;
        }
        
        /* إعدادات الطباعة */
        @page {
            size: A4;
            margin: 15mm;
        }
        
        @media print {
            body {
                padding: 0;
                margin: 0;
            }

            .print-controls {
                display: none !important;
            }

            .invoice-container {
                max-width: none;
                width: 100%;
            }

            /* الحفاظ على الألوان المهمة في الطباعة */
            .total-amount {
                color: #007bff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .payment-info {
                background-color: #f8f9fa !important;
                border: 2px solid #007bff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .invoice-summary {
                background-color: #f8f9fa !important;
                border: 2px solid #007bff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* الحفاظ على ألوان الجداول */
            th {
                background-color: #f0f0f0 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* الحفاظ على ألوان الملاحظات */
            .mixed-text-content {
                background-color: #fff3cd !important;
                border: 1px solid #ffeaa7 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <!-- أزرار التحكم -->
    <div class="print-controls">
        <button class="btn-print" onclick="printWithColors()">🖨️ طباعة بالألوان</button>
        <button class="btn-close" onclick="window.close()">❌ إغلاق</button>
    </div>

    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            {% if company_info %}
                <h1>{{ company_info.name }}</h1>
                {% if company_info.address %}
                    <p>{{ company_info.address }}</p>
                {% endif %}
                {% if company_info.phone %}
                    <p>هاتف: {{ company_info.phone }}</p>
                {% endif %}
            {% else %}
                <h1>فاتورة مبيعات</h1>
            {% endif %}
            <h2>فاتورة #{{ sale.invoice_number }}</h2>
            <p><strong>التاريخ:</strong> {{ sale.date|date:"Y-m-d H:i" }}</p>
        </div>

        <!-- معلومات البيع والعميل -->
        <div class="row">
            <div class="col-md-6">
                <h5>معلومات العميل</h5>
                <dl>
                    <dt>الاسم:</dt>
                    <dd>{{ sale.customer.name }}</dd>
                    {% if sale.customer.phone %}
                    <dt>الهاتف:</dt>
                    <dd class="arabic-number">{{ sale.customer.phone }}</dd>
                    {% endif %}
                    {% if sale.customer.email %}
                    <dt>البريد:</dt>
                    <dd>{{ sale.customer.email }}</dd>
                    {% endif %}
                </dl>
            </div>
            <div class="col-md-6">
                <h5>معلومات البيع</h5>
                <dl>
                    <dt>الموظف:</dt>
                    <dd>{{ sale.employee.get_full_name }}</dd>
                    <dt>طريقة الدفع:</dt>
                    <dd>{{ sale.get_payment_method_display }}</dd>
                    <dt>الحالة:</dt>
                    <dd>{{ sale.get_status_display }}</dd>
                </dl>
            </div>
        </div>

        <!-- عناصر البيع -->
        <h4>المنتجات</h4>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.product.name }}</td>
                    <td class="arabic-number">{{ item.quantity }}</td>
                    <td class="arabic-number">{{ item.unit_price|floatformat:2 }} د.م</td>
                    <td class="arabic-number">{{ item.subtotal|floatformat:2 }} د.م</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- الملخص المالي -->
        <div class="row">
            <div class="col-md-6">
                <div class="payment-info">
                    <h5>معلومات الدفع</h5>
                    <dl>
                        <dt>المجموع المدفوع:</dt>
                        <dd class="arabic-number">{{ total_paid|floatformat:2 }} د.م</dd>
                        <dt>المبلغ المتبقي:</dt>
                        <dd class="arabic-number">{{ remaining_amount|floatformat:2 }} د.م</dd>
                    </dl>
                </div>
            </div>
            <div class="col-md-6">
                <div class="invoice-summary">
                    <div class="d-flex">
                        <span>المبلغ الفرعي:</span>
                        <span class="arabic-number">{{ sale.subtotal|floatformat:2 }} د.م</span>
                    </div>
                    {% if sale.tax_amount %}
                    <div class="d-flex">
                        <span>الضريبة:</span>
                        <span class="arabic-number">{{ sale.tax_amount|floatformat:2 }} د.م</span>
                    </div>
                    {% endif %}
                    {% if sale.discount %}
                    <div class="d-flex">
                        <span>الخصم:</span>
                        <span class="arabic-number">{{ sale.discount|floatformat:2 }} د.م</span>
                    </div>
                    {% endif %}
                    <hr>
                    <div class="d-flex">
                        <strong>المجموع الكلي:</strong>
                        <strong class="total-amount arabic-number">{{ sale.total_amount|floatformat:2 }} د.م</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- الملاحظات -->
        {% if sale.notes %}
        <h5>ملاحظات</h5>
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <div class="mixed-text-content">{{ sale.notes|format_mixed_text }}</div>
        </div>
        {% endif %}

        <!-- منطقة التوقيع -->
        <div class="signature-area">
            <div class="signature-box">
                <p><strong>توقيع المستلم</strong></p>
                <div class="signature-line"></div>
                <small>الاسم والتاريخ</small>
            </div>
            <div class="signature-box">
                <p><strong>ختم الشركة</strong></p>
                <div class="signature-line"></div>
                <small>ختم الشركة</small>
            </div>
            <div class="signature-box">
                <p><strong>توقيع البائع</strong></p>
                <div class="signature-line"></div>
                <small>{{ sale.employee.get_full_name }}</small>
            </div>
        </div>

        <!-- ذيل الفاتورة -->
        <div style="text-align: center; margin-top: 30px; font-size: 12pt; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
            <p>شكراً لتعاملكم معنا</p>
            <p><small>تم إنشاء هذه الفاتورة في {{ "now"|date:"Y-m-d H:i" }}</small></p>
        </div>
    </div>

    <script>
        // فتح نافذة الطباعة تلقائياً مع إعدادات الألوان
        window.onload = function() {
            // تطبيق إعدادات طباعة الألوان
            document.body.style.webkitPrintColorAdjust = 'exact';
            document.body.style.printColorAdjust = 'exact';

            // تطبيق الإعدادات على جميع العناصر
            var elements = document.querySelectorAll('*');
            elements.forEach(function(element) {
                element.style.webkitPrintColorAdjust = 'exact';
                element.style.printColorAdjust = 'exact';
            });

            // إضافة تأخير قصير للتأكد من تحميل الصفحة بالكامل
            setTimeout(function() {
                window.print();
            }, 800);
        };

        // دالة للطباعة مع الألوان
        function printWithColors() {
            // تأكيد إعدادات الألوان قبل الطباعة
            var style = document.createElement('style');
            style.innerHTML = `
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }
                }
            `;
            document.head.appendChild(style);

            window.print();
        }
    </script>
</body>
</html>
