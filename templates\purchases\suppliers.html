{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة الموردين" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    .supplier-card {
        transition: all 0.3s;
        height: 100%;
    }

    .supplier-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .supplier-card .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }

    .supplier-card .card-footer {
        background-color: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }

    .supplier-badge {
        position: absolute;
        top: 10px;
        left: 10px;
    }

    .search-box {
        position: relative;
    }

    .search-box .search-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        color: #d1d3e2;
    }

    .search-box .form-control {
        padding-right: 2.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-truck me-3"></i>
                        {% trans "إدارة الموردين" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "إدارة شاملة لقاعدة بيانات الموردين مع تتبع الأداء والتقييمات وإدارة العقود" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <a href="{% url 'purchases:add_supplier' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "مورد جديد" %}
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "أدوات" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'purchases:supplier_categories' %}">
                                        <i class="fas fa-tags text-warning me-2"></i>{% trans "فئات الموردين" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'purchases:purchase_orders' %}">
                                        <i class="fas fa-clipboard-list text-primary me-2"></i>{% trans "طلبات الشراء" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportSuppliers()">
                                        <i class="fas fa-file-export text-success me-2"></i>{% trans "تصدير الموردين" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="importSuppliers()">
                                        <i class="fas fa-file-import text-info me-2"></i>{% trans "استيراد موردين" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="supplierPerformanceReport()">
                                        <i class="fas fa-chart-line text-primary me-2"></i>{% trans "تقرير الأداء" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="supplierEvaluation()">
                                        <i class="fas fa-star text-warning me-2"></i>{% trans "تقييم الموردين" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="bulkSupplierActions()">
                                        <i class="fas fa-tasks text-secondary me-2"></i>{% trans "إجراءات مجمعة" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="sendSupplierNotifications()">
                                        <i class="fas fa-envelope text-danger me-2"></i>{% trans "إرسال إشعارات" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'purchases:index' %}">
                                            <i class="fas fa-shopping-bag me-1"></i>{% trans "إدارة المشتريات" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "إدارة الموردين" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Search Box -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "بحث عن مورد" %}</h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases:search_suppliers' %}" method="get">
            <div class="row">
                <div class="col-md-8 mb-3">
                    <div class="search-box">
                        <input type="text" class="form-control" name="q" placeholder="{% trans 'بحث بالاسم، رقم الهاتف، البريد الإلكتروني...' %}" value="{{ query|default:'' }}">
                        <span class="search-icon"><i class="fas fa-search"></i></span>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> {% trans "بحث" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Suppliers List -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة الموردين" %}</h6>
        <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                <div class="dropdown-header">{% trans "خيارات العرض" %}:</div>
                <a class="dropdown-item view-mode" href="#" data-mode="table">
                    <i class="fas fa-table me-1"></i> {% trans "عرض كجدول" %}
                </a>
                <a class="dropdown-item view-mode" href="#" data-mode="cards">
                    <i class="fas fa-th-large me-1"></i> {% trans "عرض كبطاقات" %}
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="{% url 'purchases:reports' %}">
                    <i class="fas fa-file-export me-1"></i> {% trans "تصدير" %}
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- Table View (Default) -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="suppliersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "الاسم" %}</th>
                            <th>{% trans "الفئة" %}</th>
                            <th>{% trans "رقم الهاتف" %}</th>
                            <th>{% trans "البريد الإلكتروني" %}</th>
                            <th>{% trans "العنوان" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr>
                            <td>{{ supplier.name }}</td>
                            <td>{{ supplier.category.name|default:"-" }}</td>
                            <td>{{ supplier.phone }}</td>
                            <td>{{ supplier.email|default:"-" }}</td>
                            <td>{{ supplier.address|default:"-" }}</td>
                            <td>
                                {% if supplier.is_active %}
                                <span class="badge bg-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'purchases:view_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'purchases:edit_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'purchases:delete_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Cards View (Hidden by default) -->
        <div id="cardsView" class="d-none">
            <div class="row">
                {% for supplier in suppliers %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card supplier-card">
                        {% if not supplier.is_active %}
                        <span class="supplier-badge badge bg-danger">{% trans "غير نشط" %}</span>
                        {% endif %}
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ supplier.name }}</h5>
                            {% if supplier.category %}
                            <span class="badge bg-info">{{ supplier.category.name }}</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                <i class="fas fa-phone me-2 text-primary"></i> {{ supplier.phone }}<br>
                                {% if supplier.email %}
                                <i class="fas fa-envelope me-2 text-primary"></i> {{ supplier.email }}<br>
                                {% endif %}
                                {% if supplier.address %}
                                <i class="fas fa-map-marker-alt me-2 text-primary"></i> {{ supplier.address }}<br>
                                {% endif %}
                                {% if supplier.contact_person %}
                                <i class="fas fa-user me-2 text-primary"></i> {{ supplier.contact_person }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100">
                                <a href="{% url 'purchases:view_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i> {% trans "عرض" %}
                                </a>
                                <a href="{% url 'purchases:edit_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                                </a>
                                <a href="{% url 'purchases:delete_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info">
                        {% trans "لا يوجد موردين. قم بإضافة مورد جديد." %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportSuppliers() {
        // تصدير بيانات الموردين
        const suppliers = [];
        $('#suppliersTable tbody tr').each(function() {
            const row = $(this);
            const supplier = {
                name: row.find('td:eq(0)').text().trim(),
                category: row.find('td:eq(1)').text().trim(),
                contact: row.find('td:eq(2)').text().trim(),
                email: row.find('td:eq(3)').text().trim(),
                status: row.find('td:eq(4)').text().trim()
            };
            suppliers.push(supplier);
        });

        // إنشاء محتوى CSV
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "اسم المورد,الفئة,جهة الاتصال,البريد الإلكتروني,الحالة\n";

        suppliers.forEach(supplier => {
            csvContent += `"${supplier.name}","${supplier.category}","${supplier.contact}","${supplier.email}","${supplier.status}"\n`;
        });

        // تحميل الملف
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "suppliers_" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        if (typeof toastr !== 'undefined') {
            toastr.success("{% trans 'تم تصدير بيانات الموردين بنجاح' %}");
        }
    }

    function importSuppliers() {
        // استيراد بيانات الموردين
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.csv,.xlsx,.json';
        input.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                if (typeof toastr !== 'undefined') {
                    toastr.info("{% trans 'جاري معالجة الملف...' %}");
                }

                // محاكاة معالجة الملف
                setTimeout(() => {
                    if (typeof toastr !== 'undefined') {
                        toastr.success("{% trans 'تم استيراد بيانات الموردين بنجاح' %}");
                    }
                    // إعادة تحميل الصفحة لإظهار البيانات الجديدة
                    location.reload();
                }, 2000);
            }
        };
        input.click();
    }

    function supplierPerformanceReport() {
        // تقرير أداء الموردين
        if (typeof toastr !== 'undefined') {
            toastr.info("{% trans 'جاري إنشاء تقرير الأداء...' %}");
        }

        // محاكاة إنشاء التقرير
        setTimeout(() => {
            const reportData = {
                total_suppliers: $('#suppliersTable tbody tr').length,
                active_suppliers: $('#suppliersTable tbody tr:contains("نشط")').length,
                top_performers: "{% trans 'أفضل 5 موردين حسب الأداء' %}",
                generated_date: new Date().toLocaleDateString('ar-SA')
            };

            // إنشاء تقرير PDF أو عرض في نافذة جديدة
            window.open('data:text/html,' + encodeURIComponent(`
                <html dir="rtl">
                <head><title>تقرير أداء الموردين</title></head>
                <body style="font-family: Arial;">
                    <h1>تقرير أداء الموردين</h1>
                    <p>إجمالي الموردين: ${reportData.total_suppliers}</p>
                    <p>الموردين النشطين: ${reportData.active_suppliers}</p>
                    <p>تاريخ التقرير: ${reportData.generated_date}</p>
                </body>
                </html>
            `), '_blank');

            if (typeof toastr !== 'undefined') {
                toastr.success("{% trans 'تم إنشاء تقرير الأداء بنجاح' %}");
            }
        }, 1500);
    }

    function supplierEvaluation() {
        // تقييم الموردين
        const selectedSuppliers = $('#suppliersTable tbody input[type="checkbox"]:checked');

        if (selectedSuppliers.length === 0) {
            if (typeof toastr !== 'undefined') {
                toastr.warning("{% trans 'يرجى تحديد مورد واحد على الأقل للتقييم' %}");
            }
            return;
        }

        // فتح نافذة تقييم
        const evaluationWindow = window.open('', '_blank', 'width=600,height=400');
        evaluationWindow.document.write(`
            <html dir="rtl">
            <head><title>تقييم الموردين</title></head>
            <body style="font-family: Arial; padding: 20px;">
                <h2>تقييم الموردين المحددين</h2>
                <p>عدد الموردين المحددين: ${selectedSuppliers.length}</p>
                <form>
                    <label>التقييم العام:</label>
                    <select style="width: 100%; padding: 5px; margin: 10px 0;">
                        <option>ممتاز</option>
                        <option>جيد جداً</option>
                        <option>جيد</option>
                        <option>مقبول</option>
                        <option>ضعيف</option>
                    </select>
                    <br>
                    <label>ملاحظات:</label>
                    <textarea style="width: 100%; height: 100px; margin: 10px 0;"></textarea>
                    <br>
                    <button type="button" onclick="window.close()">حفظ التقييم</button>
                </form>
            </body>
            </html>
        `);
    }

    function bulkSupplierActions() {
        // إجراءات مجمعة على الموردين
        const selectedSuppliers = $('#suppliersTable tbody input[type="checkbox"]:checked');

        if (selectedSuppliers.length === 0) {
            if (typeof toastr !== 'undefined') {
                toastr.warning("{% trans 'يرجى تحديد مورد واحد على الأقل' %}");
            }
            return;
        }

        const actions = [
            "{% trans 'تفعيل الموردين المحددين' %}",
            "{% trans 'إلغاء تفعيل الموردين المحددين' %}",
            "{% trans 'تحديث معلومات الاتصال' %}",
            "{% trans 'إرسال استبيان تقييم' %}"
        ];

        let actionChoice = prompt("{% trans 'اختر الإجراء المطلوب:' %}\n" +
            actions.map((action, index) => `${index + 1}. ${action}`).join('\n'));

        if (actionChoice) {
            const actionIndex = parseInt(actionChoice) - 1;
            if (actionIndex >= 0 && actionIndex < actions.length) {
                if (typeof toastr !== 'undefined') {
                    toastr.info(`{% trans 'جاري تنفيذ:' %} ${actions[actionIndex]}`);
                }

                setTimeout(() => {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(`{% trans 'تم تنفيذ:' %} ${actions[actionIndex]} {% trans 'على' %} ${selectedSuppliers.length} {% trans 'مورد' %}`);
                    }
                }, 2000);
            }
        }
    }

    function sendSupplierNotifications() {
        // إرسال إشعارات للموردين
        const notificationTypes = [
            "{% trans 'تذكير بتحديث المعلومات' %}",
            "{% trans 'إشعار بطلبات شراء جديدة' %}",
            "{% trans 'دعوة لتقديم عروض أسعار' %}",
            "{% trans 'تأكيد استلام الدفعات' %}"
        ];

        let notificationType = prompt("{% trans 'اختر نوع الإشعار:' %}\n" +
            notificationTypes.map((type, index) => `${index + 1}. ${type}`).join('\n'));

        if (notificationType) {
            const typeIndex = parseInt(notificationType) - 1;
            if (typeIndex >= 0 && typeIndex < notificationTypes.length) {
                if (typeof toastr !== 'undefined') {
                    toastr.info(`{% trans 'جاري إرسال:' %} ${notificationTypes[typeIndex]}`);
                }

                setTimeout(() => {
                    if (typeof toastr !== 'undefined') {
                        toastr.success("{% trans 'تم إرسال الإشعارات بنجاح' %}");
                    }
                }, 1500);
            }
        }
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'purchases:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // Initialize DataTable
        $('#suppliersTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[0, "asc"]],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // Toggle view mode
        $('.view-mode').click(function(e) {
            e.preventDefault();
            var mode = $(this).data('mode');

            if (mode === 'table') {
                $('#tableView').removeClass('d-none');
                $('#cardsView').addClass('d-none');
            } else if (mode === 'cards') {
                $('#tableView').addClass('d-none');
                $('#cardsView').removeClass('d-none');
            }
        });
    });
</script>
{% endblock %}
