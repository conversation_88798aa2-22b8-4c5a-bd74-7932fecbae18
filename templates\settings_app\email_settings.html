{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* تطبيق خط Cairo على الصفحة */
    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    /* تصحيح حجم الخط في شريط التنقل */
    .nav-link {
        font-size: 1.1rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
    
    .navbar-brand {
        font-size: 1.2rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
</style>
{% endblock %}

{% block title %}{% trans "إعدادات البريد الإلكتروني" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إعدادات البريد الإلكتروني" %}</h1>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "إعدادات خادم البريد الإلكتروني" %}</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'settings_app:email_settings' %}">
                        {% csrf_token %}
                        
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="is_enabled" name="is_enabled" {% if email_setting.is_enabled %}checked{% endif %}>
                            <label class="form-check-label" for="is_enabled">
                                {% trans "تفعيل إرسال البريد الإلكتروني" %}
                            </label>
                            <div class="form-text">{% trans "عند تعطيل هذا الخيار، لن يتم إرسال أي بريد إلكتروني من النظام." %}</div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="text-gray-800 mb-3">{% trans "إعدادات SMTP" %}</h5>
                                <div class="mb-3">
                                    <label for="smtp_host" class="form-label">{% trans "مضيف SMTP" %}</label>
                                    <input type="text" class="form-control" id="smtp_host" name="smtp_host" value="{{ email_setting.smtp_host }}" required>
                                    <div class="form-text">{% trans "مثال: smtp.gmail.com، smtp.office365.com" %}</div>
                                </div>
                                <div class="mb-3">
                                    <label for="smtp_port" class="form-label">{% trans "منفذ SMTP" %}</label>
                                    <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="{{ email_setting.smtp_port }}" required>
                                    <div class="form-text">{% trans "عادة 587 (TLS) أو 465 (SSL)" %}</div>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="smtp_use_tls" name="smtp_use_tls" {% if email_setting.smtp_use_tls %}checked{% endif %}>
                                    <label class="form-check-label" for="smtp_use_tls">
                                        {% trans "استخدام TLS" %}
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-gray-800 mb-3">{% trans "بيانات الاعتماد" %}</h5>
                                <div class="mb-3">
                                    <label for="smtp_username" class="form-label">{% trans "اسم المستخدم" %}</label>
                                    <input type="text" class="form-control" id="smtp_username" name="smtp_username" value="{{ email_setting.smtp_username }}" required>
                                    <div class="form-text">{% trans "عادة عنوان البريد الإلكتروني الكامل" %}</div>
                                </div>
                                <div class="mb-3">
                                    <label for="smtp_password" class="form-label">{% trans "كلمة المرور" %}</label>
                                    <input type="password" class="form-control" id="smtp_password" name="smtp_password" placeholder="{% if email_setting.smtp_password %}••••••••{% endif %}">
                                    <div class="form-text">{% trans "اترك فارغًا إذا لم ترغب في تغيير كلمة المرور الحالية" %}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h5 class="text-gray-800 mb-3">{% trans "إعدادات المرسل" %}</h5>
                            <div class="mb-3">
                                <label for="from_email" class="form-label">{% trans "البريد الإلكتروني المرسل" %}</label>
                                <input type="email" class="form-control" id="from_email" name="from_email" value="{{ email_setting.from_email }}" required>
                                <div class="form-text">{% trans "عنوان البريد الإلكتروني الذي سيظهر في حقل 'من'" %}</div>
                            </div>
                            <div class="mb-3">
                                <label for="from_name" class="form-label">{% trans "اسم المرسل" %}</label>
                                <input type="text" class="form-control" id="from_name" name="from_name" value="{{ email_setting.from_name }}" required>
                                <div class="form-text">{% trans "الاسم الذي سيظهر كمرسل البريد الإلكتروني" %}</div>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="button" class="btn btn-info me-2" id="testEmailBtn">
                                <i class="fas fa-paper-plane me-1"></i> {% trans "اختبار الإعدادات" %}
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> {% trans "حفظ الإعدادات" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات مساعدة" %}</h6>
                </div>
                <div class="card-body">
                    <h5 class="text-gray-800 mb-3">{% trans "إعدادات مزودي البريد الشائعة" %}</h5>
                    
                    <div class="accordion" id="emailProvidersAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="gmailHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gmailCollapse" aria-expanded="false" aria-controls="gmailCollapse">
                                    Gmail
                                </button>
                            </h2>
                            <div id="gmailCollapse" class="accordion-collapse collapse" aria-labelledby="gmailHeading" data-bs-parent="#emailProvidersAccordion">
                                <div class="accordion-body">
                                    <ul class="list-unstyled">
                                        <li><strong>{% trans "مضيف SMTP:" %}</strong> smtp.gmail.com</li>
                                        <li><strong>{% trans "منفذ SMTP:" %}</strong> 587</li>
                                        <li><strong>{% trans "استخدام TLS:" %}</strong> {% trans "نعم" %}</li>
                                        <li><strong>{% trans "اسم المستخدم:" %}</strong> {% trans "بريدك الإلكتروني الكامل" %}</li>
                                        <li><strong>{% trans "كلمة المرور:" %}</strong> {% trans "كلمة مرور التطبيق" %}</li>
                                    </ul>
                                    <div class="alert alert-info small">
                                        {% trans "ملاحظة: يجب تفعيل المصادقة الثنائية وإنشاء كلمة مرور للتطبيق في إعدادات حساب Google." %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="outlookHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#outlookCollapse" aria-expanded="false" aria-controls="outlookCollapse">
                                    Outlook / Office 365
                                </button>
                            </h2>
                            <div id="outlookCollapse" class="accordion-collapse collapse" aria-labelledby="outlookHeading" data-bs-parent="#emailProvidersAccordion">
                                <div class="accordion-body">
                                    <ul class="list-unstyled">
                                        <li><strong>{% trans "مضيف SMTP:" %}</strong> smtp.office365.com</li>
                                        <li><strong>{% trans "منفذ SMTP:" %}</strong> 587</li>
                                        <li><strong>{% trans "استخدام TLS:" %}</strong> {% trans "نعم" %}</li>
                                        <li><strong>{% trans "اسم المستخدم:" %}</strong> {% trans "بريدك الإلكتروني الكامل" %}</li>
                                        <li><strong>{% trans "كلمة المرور:" %}</strong> {% trans "كلمة مرور حسابك" %}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="yahooHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#yahooCollapse" aria-expanded="false" aria-controls="yahooCollapse">
                                    Yahoo Mail
                                </button>
                            </h2>
                            <div id="yahooCollapse" class="accordion-collapse collapse" aria-labelledby="yahooHeading" data-bs-parent="#emailProvidersAccordion">
                                <div class="accordion-body">
                                    <ul class="list-unstyled">
                                        <li><strong>{% trans "مضيف SMTP:" %}</strong> smtp.mail.yahoo.com</li>
                                        <li><strong>{% trans "منفذ SMTP:" %}</strong> 587</li>
                                        <li><strong>{% trans "استخدام TLS:" %}</strong> {% trans "نعم" %}</li>
                                        <li><strong>{% trans "اسم المستخدم:" %}</strong> {% trans "بريدك الإلكتروني الكامل" %}</li>
                                        <li><strong>{% trans "كلمة المرور:" %}</strong> {% trans "كلمة مرور التطبيق" %}</li>
                                    </ul>
                                    <div class="alert alert-info small">
                                        {% trans "ملاحظة: يجب تفعيل المصادقة الثنائية وإنشاء كلمة مرور للتطبيق في إعدادات حساب Yahoo." %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h5 class="text-gray-800 mb-3">{% trans "استكشاف الأخطاء وإصلاحها" %}</h5>
                        <ul class="small">
                            <li>{% trans "تأكد من صحة بيانات الاعتماد (اسم المستخدم وكلمة المرور)." %}</li>
                            <li>{% trans "تحقق من إعدادات الأمان لحساب البريد الإلكتروني الخاص بك." %}</li>
                            <li>{% trans "قد تحتاج إلى تفعيل 'وصول التطبيقات الأقل أمانًا' في بعض مزودي البريد الإلكتروني." %}</li>
                            <li>{% trans "استخدم زر 'اختبار الإعدادات' للتحقق من صحة الإعدادات." %}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Email Modal -->
<div class="modal fade" id="testEmailModal" tabindex="-1" aria-labelledby="testEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testEmailModalLabel">{% trans "اختبار إعدادات البريد الإلكتروني" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="test_email" class="form-label">{% trans "إرسال بريد إلكتروني اختباري إلى" %}</label>
                    <input type="email" class="form-control" id="test_email" required>
                </div>
                <div id="testEmailResult"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                <button type="button" class="btn btn-primary" id="sendTestEmailBtn">{% trans "إرسال" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Test Email Button
        const testEmailBtn = document.getElementById('testEmailBtn');
        const testEmailModal = new bootstrap.Modal(document.getElementById('testEmailModal'));
        const sendTestEmailBtn = document.getElementById('sendTestEmailBtn');
        const testEmailResult = document.getElementById('testEmailResult');
        
        testEmailBtn.addEventListener('click', function() {
            testEmailModal.show();
        });
        
        sendTestEmailBtn.addEventListener('click', function() {
            const testEmail = document.getElementById('test_email').value;
            if (!testEmail) {
                testEmailResult.innerHTML = '<div class="alert alert-danger">{% trans "يرجى إدخال عنوان بريد إلكتروني صالح" %}</div>';
                return;
            }
            
            // Collect current form data
            const formData = new FormData();
            formData.append('smtp_host', document.getElementById('smtp_host').value);
            formData.append('smtp_port', document.getElementById('smtp_port').value);
            formData.append('smtp_username', document.getElementById('smtp_username').value);
            formData.append('smtp_use_tls', document.getElementById('smtp_use_tls').checked ? 'on' : 'off');
            formData.append('from_email', document.getElementById('from_email').value);
            formData.append('from_name', document.getElementById('from_name').value);
            formData.append('test_email', testEmail);
            
            // If password field has a value, include it
            const password = document.getElementById('smtp_password').value;
            if (password) {
                formData.append('smtp_password', password);
            }
            
            // Show loading message
            testEmailResult.innerHTML = '<div class="alert alert-info">{% trans "جاري إرسال البريد الإلكتروني الاختباري..." %}</div>';
            sendTestEmailBtn.disabled = true;
            
            // In a real implementation, you would send this data to the server
            // For this demo, we'll simulate a response after a delay
            setTimeout(function() {
                // Simulate success
                testEmailResult.innerHTML = '<div class="alert alert-success">{% trans "تم إرسال البريد الإلكتروني الاختباري بنجاح!" %}</div>';
                sendTestEmailBtn.disabled = false;
                
                // In a real implementation, you would handle success/error from the server
                // If there's an error, display it in testEmailResult
            }, 2000);
        });
        
        // Provider Quick Setup
        const providerButtons = document.querySelectorAll('.email-provider-btn');
        providerButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const provider = this.getAttribute('data-provider');
                
                // Set values based on provider
                if (provider === 'gmail') {
                    document.getElementById('smtp_host').value = 'smtp.gmail.com';
                    document.getElementById('smtp_port').value = '587';
                    document.getElementById('smtp_use_tls').checked = true;
                } else if (provider === 'outlook') {
                    document.getElementById('smtp_host').value = 'smtp.office365.com';
                    document.getElementById('smtp_port').value = '587';
                    document.getElementById('smtp_use_tls').checked = true;
                } else if (provider === 'yahoo') {
                    document.getElementById('smtp_host').value = 'smtp.mail.yahoo.com';
                    document.getElementById('smtp_port').value = '587';
                    document.getElementById('smtp_use_tls').checked = true;
                }
            });
        });
    });
</script>
{% endblock %}
