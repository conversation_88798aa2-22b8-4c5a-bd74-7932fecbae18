/* تنسيقات خاصة بصفحات الموظفين */

/* تنسيق أزرار الإجراءات في جداول الأقسام والمناصب */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 5px;
}

.action-buttons .btn {
    width: 35px;
    height: 35px;
    padding: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* تنسيق الجداول */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

/* تنسيق صفحة الأدوار والصلاحيات */
.role-item.active {
    background-color: #e9f0ff;
    border-color: #007bff;
}

.permission-checkbox:checked + label {
    font-weight: bold;
}

/* تنسيق بطاقات الإحصائيات */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* تنسيق صفحة عرض الموظف */
.employee-profile-image {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid #f8f9fa;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.employee-info-card {
    border-radius: 10px;
    overflow: hidden;
}

/* تنسيق نموذج البحث */
.search-form {
    position: relative;
}

.search-form .form-control {
    padding-right: 40px;
}

.search-form .search-icon {
    position: absolute;
    left: 10px;
    top: 10px;
    color: #6c757d;
}
