{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تقرير الموردين" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    .card {
        border: none;
        border-radius: 10px;
        box-shadow: var(--shadow-md);
        margin-bottom: 20px;
    }

    .card-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
        color: white;
        border-radius: 10px 10px 0 0 !important;
        padding: 1rem;
    }

    .card-title {
        margin-bottom: 0;
        font-weight: 600;
    }

    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: #1d4ed8;
        border-color: #1d4ed8;
    }

    .table-responsive {
        border-radius: 0 0 10px 10px;
    }

    .table th {
        border-top: none;
        background-color: #f8fafc;
        font-weight: 600;
        color: var(--dark-color);
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .stats-card {
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
        box-shadow: var(--shadow-md);
        transition: transform 0.2s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-card i {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .stats-card h3 {
        margin-bottom: 5px;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 30px;
    }

    .supplier-card {
        transition: all 0.3s;
        height: 100%;
    }

    .supplier-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .supplier-card .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }

    .supplier-card .card-footer {
        background-color: #f8f9fc;
        border-top: 1px solid #e3e6f0;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تقرير الموردين" %}</h1>
    <div>
        <a href="{% url 'purchases:export_report' report_type='suppliers' %}" class="btn btn-success">
            <i class="fas fa-file-export me-1"></i> {% trans "تصدير التقرير" %}
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <i class="fas fa-users text-primary"></i>
            <h3>{{ total_suppliers }}</h3>
            <p>{% trans "إجمالي الموردين" %}</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <i class="fas fa-user-check text-success"></i>
            <h3>{{ active_suppliers }}</h3>
            <p>{% trans "موردين نشطين" %}</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <i class="fas fa-user-times text-danger"></i>
            <h3>{{ inactive_suppliers }}</h3>
            <p>{% trans "موردين غير نشطين" %}</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <i class="fas fa-handshake text-info"></i>
            <h3>{{ total_purchases }}</h3>
            <p>{% trans "إجمالي المشتريات" %}</p>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card">
    <div class="card-header">
        <h4 class="card-title">{% trans "تصفية النتائج" %}</h4>
    </div>
    <div class="card-body">
        <form method="get" id="filterForm">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="category">{% trans "فئة المورد" %}</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">{% trans "الكل" %}</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="status">{% trans "الحالة" %}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{% trans "الكل" %}</option>
                        <option value="active" {% if selected_status == 'active' %}selected{% endif %}>{% trans "نشط" %}</option>
                        <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>{% trans "غير نشط" %}</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="region">{% trans "المنطقة" %}</label>
                    <select class="form-select" id="region" name="region">
                        <option value="">{% trans "الكل" %}</option>
                        {% for region in regions %}
                        <option value="{{ region.id }}" {% if selected_region == region.id %}selected{% endif %}>{{ region.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="rating">{% trans "التقييم" %}</label>
                    <select class="form-select" id="rating" name="rating">
                        <option value="">{% trans "الكل" %}</option>
                        <option value="5" {% if selected_rating == '5' %}selected{% endif %}>★★★★★</option>
                        <option value="4" {% if selected_rating == '4' %}selected{% endif %}>★★★★☆</option>
                        <option value="3" {% if selected_rating == '3' %}selected{% endif %}>★★★☆☆</option>
                        <option value="2" {% if selected_rating == '2' %}selected{% endif %}>★★☆☆☆</option>
                        <option value="1" {% if selected_rating == '1' %}selected{% endif %}>★☆☆☆☆</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="date_from">{% trans "من تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="date_to">{% trans "إلى تاريخ" %}</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
            </div>
            <div class="text-center">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i> {% trans "بحث" %}
                </button>
                <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                    <i class="fas fa-redo me-1"></i> {% trans "إعادة تعيين" %}
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Suppliers List -->
<div class="card">
    <div class="card-header">
        <h4 class="card-title">{% trans "قائمة الموردين" %}</h4>
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <span class="badge bg-primary">{{ suppliers|length }} {% trans "مورد" %}</span>
            </div>
            <div>
                <button class="btn btn-sm btn-secondary view-mode" data-mode="table">
                    <i class="fas fa-table"></i> {% trans "جدول" %}
                </button>
                <button class="btn btn-sm btn-secondary view-mode" data-mode="cards">
                    <i class="fas fa-th-large"></i> {% trans "بطاقات" %}
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="suppliersTable">
                    <thead>
                        <tr>
                            <th>{% trans "الاسم" %}</th>
                            <th>{% trans "التصنيف" %}</th>
                            <th>{% trans "الحالة" %}</th>
                            <th>{% trans "المدينة" %}</th>
                            <th>{% trans "التقييم" %}</th>
                            <th>{% trans "عدد المشتريات" %}</th>
                            <th>{% trans "إجمالي المبلغ" %}</th>
                            <th>{% trans "آخر نشاط" %}</th>
                            <th>{% trans "الإجراءات" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if supplier.logo %}
                                    <img src="{{ supplier.logo.url }}" alt="{{ supplier.name }}" class="rounded-circle me-2" width="40" height="40">
                                    {% else %}
                                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ supplier.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ supplier.contact_person }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ supplier.category.name }}</td>
                            <td>
                                {% if supplier.is_active %}
                                <span class="badge bg-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </td>
                            <td>{{ supplier.city }}</td>
                            <td>
                                {% for i in "12345"|make_list %}
                                {% if forloop.counter <= supplier.rating %}
                                <i class="fas fa-star text-warning"></i>
                                {% else %}
                                <i class="far fa-star text-warning"></i>
                                {% endif %}
                                {% endfor %}
                            </td>
                            <td>{{ supplier.purchase_count }}</td>
                            <td>{{ supplier.total_amount|floatformat:2 }} ر.س</td>
                            <td>{{ supplier.last_activity|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'purchases:view_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'purchases:edit_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center text-muted">{% trans "لا توجد بيانات مطابقة للعبارات المحددة." %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <div id="cardsView" class="d-none">
            <div class="row">
                {% for supplier in suppliers %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card supplier-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ supplier.name }}</h5>
                            {% if supplier.is_active %}
                            <span class="badge bg-success">{% trans "نشط" %}</span>
                            {% else %}
                            <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                {% if supplier.logo %}
                                <img src="{{ supplier.logo.url }}" alt="{{ supplier.name }}" class="rounded-circle me-2" width="60" height="60">
                                {% else %}
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-2" style="width: 60px; height: 60px;">
                                    <i class="fas fa-user text-muted fa-2x"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ supplier.contact_person }}</strong>
                                    <br>
                                    <small class="text-muted">{{ supplier.phone }}</small>
                                </div>
                            </div>

                            <p class="mb-1"><strong>{% trans "التصنيف" %}:</strong> {{ supplier.category.name }}</p>
                            <p class="mb-1"><strong>{% trans "المدينة" %}:</strong> {{ supplier.city }}</p>
                            <p class="mb-1"><strong>{% trans "التقييم" %}:</strong>
                                {% for i in "12345"|make_list %}
                                {% if forloop.counter <= supplier.rating %}
                                <i class="fas fa-star text-warning"></i>
                                {% else %}
                                <i class="far fa-star text-warning"></i>
                                {% endif %}
                                {% endfor %}
                            </p>
                            <p class="mb-1"><strong>{% trans "عدد المشتريات" %}:</strong> {{ supplier.purchase_count }}</p>
                            <p class="mb-1"><strong>{% trans "إجمالي المبلغ" %}:</strong> {{ supplier.total_amount|floatformat:2 }} ر.س</p>
                            <p class="mb-0"><strong>{% trans "آخر نشاط" %}:</strong> {{ supplier.last_activity|date:"Y-m-d" }}</p>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100">
                                <a href="{% url 'purchases:view_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> {% trans "عرض" %}
                                </a>
                                <a href="{% url 'purchases:edit_supplier' supplier_id=supplier.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> {% trans "تعديل" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info">
                        {% trans "لا توجد بيانات مطابقة للعبارات المحددة." %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Top Suppliers Chart -->
<div class="card">
    <div class="card-header">
        <h4 class="card-title">{% trans "أفضل 10 موردين حسب المبلغ الإجمالي" %}</h4>
    </div>
    <div class="card-body">
        <div class="chart-container">
            <canvas id="suppliersChart"></canvas>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // استدعاء دالة تهيئة القوائم المنسدلة من navbar-fix.js
        if (typeof initializeNavbarDropdowns === 'function') {
            initializeNavbarDropdowns();
        }
    });

    $(document).ready(function() {
        // Initialize DataTable
        $('#suppliersTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "order": [[0, "asc"]],
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip'
        });

        // Toggle view mode
        $('.view-mode').click(function(e) {
            e.preventDefault();
            var mode = $(this).data('mode');

            if (mode === 'table') {
                $('#tableView').removeClass('d-none');
                $('#cardsView').addClass('d-none');
            } else if (mode === 'cards') {
                $('#tableView').addClass('d-none');
                $('#cardsView').removeClass('d-none');
            }
        });

        // Initialize Chart
        var ctx = document.getElementById('suppliersChart').getContext('2d');
        var suppliersChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [
                    {% for supplier in top_suppliers %}
                    "{{ supplier.name }}"{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "إجمالي المبلغ (ر.س)" %}',
                    data: [
                    {% for supplier in top_suppliers %}
                    {{ supplier.total_amount }}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                    ],
                    backgroundColor: 'rgba(37, 99, 235, 0.7)',
                    borderColor: 'rgba(37, 99, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                }
            }
        });
    });

    function resetFilters() {
        document.getElementById('filterForm').reset();
        document.getElementById('filterForm').submit();
    }
</script>
{% endblock %}
