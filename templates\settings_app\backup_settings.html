{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* تطبيق خط Cairo على الصفحة */
    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    /* تصحيح حجم الخط في شريط التنقل */
    .nav-link {
        font-size: 1.1rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
    
    .navbar-brand {
        font-size: 1.2rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
</style>
{% endblock %}

{% block title %}{% trans "إعدادات النسخ الاحتياطي" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "إعدادات النسخ الاحتياطي" %}</h1>
        <div>
            <a href="{% url 'settings_app:backup_logs' %}" class="btn btn-info me-2">
                <i class="fas fa-history me-1"></i> {% trans "سجل العمليات" %}
            </a>
            <button type="button" class="btn btn-success me-2" id="createBackupBtn">
                <i class="fas fa-download me-1"></i> {% trans "إنشاء نسخة احتياطية الآن" %}
            </button>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "إعدادات النسخ الاحتياطي التلقائي" %}</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'settings_app:backup_settings' %}">
                        {% csrf_token %}

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="is_auto_backup" name="is_auto_backup" {% if backup_setting.is_auto_backup %}checked{% endif %}>
                            <label class="form-check-label" for="is_auto_backup">
                                {% trans "تفعيل النسخ الاحتياطي التلقائي" %}
                            </label>
                            <div class="form-text">{% trans "عند تفعيل هذا الخيار، سيقوم النظام بإنشاء نسخ احتياطية تلقائية حسب الجدول المحدد." %}</div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="text-gray-800 mb-3">{% trans "جدولة النسخ الاحتياطي" %}</h5>
                                <div class="mb-3">
                                    <label for="frequency" class="form-label">{% trans "تكرار النسخ الاحتياطي" %}</label>
                                    <select class="form-select" id="frequency" name="frequency" required>
                                        <option value="daily" {% if backup_setting.frequency == 'daily' %}selected{% endif %}>{% trans "يومي" %}</option>
                                        <option value="weekly" {% if backup_setting.frequency == 'weekly' %}selected{% endif %}>{% trans "أسبوعي" %}</option>
                                        <option value="monthly" {% if backup_setting.frequency == 'monthly' %}selected{% endif %}>{% trans "شهري" %}</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="backup_time" class="form-label">{% trans "وقت النسخ الاحتياطي" %}</label>
                                    <input type="time" class="form-control" id="backup_time" name="backup_time" value="{{ backup_setting.backup_time|time:'H:i' }}" required>
                                    <div class="form-text">{% trans "الوقت بالتنسيق 24 ساعة. يفضل اختيار وقت خارج ساعات العمل." %}</div>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="include_media" name="include_media" {% if backup_setting.include_media %}checked{% endif %}>
                                    <label class="form-check-label" for="include_media">
                                        {% trans "تضمين ملفات الوسائط" %}
                                    </label>
                                    <div class="form-text">{% trans "تضمين الصور وملفات الوسائط الأخرى في النسخة الاحتياطية. قد يزيد هذا من حجم النسخة الاحتياطية بشكل كبير." %}</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5 class="text-gray-800 mb-3">{% trans "إعدادات الاحتفاظ" %}</h5>
                                <div class="mb-3">
                                    <label for="retention_days" class="form-label">{% trans "الاحتفاظ بالنسخ الاحتياطية لمدة (أيام)" %}</label>
                                    <input type="number" class="form-control" id="retention_days" name="retention_days" value="{{ backup_setting.retention_days }}" min="1" required>
                                    <div class="form-text">{% trans "سيتم حذف النسخ الاحتياطية الأقدم من هذه المدة تلقائيًا." %}</div>
                                </div>
                                <div class="mb-3">
                                    <label for="keep_min_backups" class="form-label">{% trans "الحد الأدنى للنسخ الاحتياطية" %}</label>
                                    <input type="number" class="form-control" id="keep_min_backups" name="keep_min_backups" value="{{ backup_setting.keep_min_backups|default:5 }}" min="1" required>
                                    <div class="form-text">{% trans "الحد الأدنى لعدد النسخ الاحتياطية التي سيتم الاحتفاظ بها دائمًا." %}</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5 class="text-gray-800 mb-3">{% trans "إعدادات التشفير" %}</h5>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="encrypt_backups" name="encrypt_backups" {% if backup_setting.encrypt_backups %}checked{% endif %}>
                                <label class="form-check-label" for="encrypt_backups">
                                    {% trans "تشفير النسخ الاحتياطية" %}
                                </label>
                                <div class="form-text">{% trans "تشفير النسخ الاحتياطية لحماية البيانات الحساسة. ملاحظة: سيتم تخزين مفتاح التشفير في قاعدة البيانات." %}</div>
                            </div>
                            <div class="mb-3" id="encryption_algorithm_container" {% if not backup_setting.encrypt_backups %}style="display: none;"{% endif %}>
                                <label for="encryption_algorithm" class="form-label">{% trans "خوارزمية التشفير" %}</label>
                                <select class="form-select" id="encryption_algorithm" name="encryption_algorithm">
                                    <option value="aes256" {% if backup_setting.encryption_algorithm == 'aes256' %}selected{% endif %}>AES-256 ({% trans "موصى به" %})</option>
                                    <option value="aes192" {% if backup_setting.encryption_algorithm == 'aes192' %}selected{% endif %}>AES-192</option>
                                    <option value="aes128" {% if backup_setting.encryption_algorithm == 'aes128' %}selected{% endif %}>AES-128</option>
                                </select>
                                <div class="form-text">{% trans "خوارزمية التشفير المستخدمة لتشفير النسخ الاحتياطية. AES-256 هي الأكثر أمانًا." %}</div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5 class="text-gray-800 mb-3">{% trans "إعدادات الإشعارات" %}</h5>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="notify_on_success" name="notify_on_success" {% if backup_setting.notify_on_success %}checked{% endif %}>
                                <label class="form-check-label" for="notify_on_success">
                                    {% trans "إشعار عند نجاح النسخ الاحتياطي" %}
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="notify_on_failure" name="notify_on_failure" {% if backup_setting.notify_on_failure %}checked{% endif %}>
                                <label class="form-check-label" for="notify_on_failure">
                                    {% trans "إشعار عند فشل النسخ الاحتياطي" %}
                                </label>
                            </div>
                            <div class="mb-3">
                                <label for="notification_email" class="form-label">{% trans "البريد الإلكتروني للإشعارات" %}</label>
                                <input type="email" class="form-control" id="notification_email" name="notification_email" value="{{ backup_setting.notification_email|default:'' }}">
                                <div class="form-text">{% trans "سيتم إرسال إشعارات النسخ الاحتياطي إلى هذا البريد الإلكتروني. اتركه فارغًا لاستخدام بريد المسؤول الافتراضي." %}</div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5 class="text-gray-800 mb-3">{% trans "موقع التخزين" %}</h5>
                            <div class="mb-3">
                                <label for="storage_type" class="form-label">{% trans "نوع التخزين" %}</label>
                                <select class="form-select" id="storage_type" name="storage_type" required>
                                    <option value="local" {% if backup_setting.storage_type == 'local' %}selected{% endif %}>{% trans "محلي (على الخادم)" %}</option>
                                    <option value="google_drive" {% if backup_setting.storage_type == 'google_drive' %}selected{% endif %}>Google Drive</option>
                                    <option value="dropbox" {% if backup_setting.storage_type == 'dropbox' %}selected{% endif %}>Dropbox</option>
                                </select>
                            </div>
                            <div class="mb-3" id="storage_path_container" {% if backup_setting.storage_type != 'local' %}style="display: none;"{% endif %}>
                                <label for="storage_path" class="form-label">{% trans "مسار التخزين" %}</label>
                                <input type="text" class="form-control" id="storage_path" name="storage_path" value="{{ backup_setting.storage_path|default:'' }}">
                                <div class="form-text">{% trans "المسار المطلق للمجلد الذي سيتم تخزين النسخ الاحتياطية فيه. اتركه فارغًا لاستخدام المجلد الافتراضي." %}</div>
                            </div>
                            <div class="mb-3" id="cloud_auth_container" {% if backup_setting.storage_type == 'local' %}style="display: none;"{% endif %}>
                                <button type="button" class="btn btn-outline-primary" id="cloudAuthBtn">
                                    <i class="fas fa-link me-1"></i> {% trans "ربط حساب" %} <span id="cloud_service_name">{% if backup_setting.storage_type == 'google_drive' %}Google Drive{% elif backup_setting.storage_type == 'dropbox' %}Dropbox{% endif %}</span>
                                </button>
                                <div class="form-text">{% trans "يجب ربط حساب التخزين السحابي قبل استخدامه لتخزين النسخ الاحتياطية." %}</div>
                            </div>
                        </div>

                        <div class="text-end mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-1"></i> {% trans "حفظ الإعدادات" %}
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-lg ms-2" id="cleanupBackupsBtn">
                                <i class="fas fa-broom me-1"></i> {% trans "تنظيف النسخ القديمة" %}
                            </button>
                        </div>
                        <!-- مسافة إضافية لضمان عدم تداخل الشريط السفلي مع الأزرار -->
                        <div style="height: 30px;"></div>

                        <form id="cleanupBackupsForm" method="post" action="{% url 'settings_app:backup_settings' %}" style="display: none;">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="cleanup">
                        </form>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "آخر النسخ الاحتياطية" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead>
                                <tr>
                                    <th>{% trans "التاريخ" %}</th>
                                    <th>{% trans "الحجم" %}</th>
                                    <th>{% trans "النوع" %}</th>
                                    <th>{% trans "الحالة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in backup_logs %}
                                <tr>
                                    <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>{{ log.file_size|filesizeformat }}</td>
                                    <td>
                                        {% if log.backup_type == 'auto' %}
                                        <span class="badge bg-info">{% trans "تلقائي" %}</span>
                                        {% else %}
                                        <span class="badge bg-primary">{% trans "يدوي" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.status == 'success' %}
                                        <span class="badge bg-success">{% trans "ناجح" %}</span>
                                        {% else %}
                                        <span class="badge bg-danger">{% trans "فاشل" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">{% trans "لا توجد نسخ احتياطية" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <a href="{% url 'settings_app:backup' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-history me-1"></i> {% trans "عرض جميع النسخ الاحتياطية" %}
                        </a>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات مساعدة" %}</h6>
                </div>
                <div class="card-body">
                    <h5 class="text-gray-800 mb-3">{% trans "أفضل الممارسات" %}</h5>
                    <ul class="small">
                        <li>{% trans "قم بإنشاء نسخ احتياطية بشكل منتظم، يفضل يوميًا." %}</li>
                        <li>{% trans "احتفظ بنسخ احتياطية في مواقع مختلفة (محلي وسحابي)." %}</li>
                        <li>{% trans "اختبر استعادة النسخ الاحتياطية بشكل دوري للتأكد من صحتها." %}</li>
                        <li>{% trans "قم بتخزين النسخ الاحتياطية في مكان آمن ومحمي." %}</li>
                    </ul>

                    <div class="alert alert-warning mt-3 small">
                        <i class="fas fa-exclamation-triangle me-1"></i> {% trans "تنبيه: قد يستغرق إنشاء النسخ الاحتياطية وقتًا طويلاً اعتمادًا على حجم البيانات. تجنب إيقاف النظام أثناء عملية النسخ الاحتياطي." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Backup Modal -->
<div class="modal fade" id="createBackupModal" tabindex="-1" aria-labelledby="createBackupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createBackupModalLabel">{% trans "إنشاء نسخة احتياطية" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createBackupForm" method="post" action="{% url 'settings_app:backup_settings' %}">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="create">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_media_manual" name="include_media_manual" checked>
                            <label class="form-check-label" for="include_media_manual">
                                {% trans "تضمين ملفات الوسائط" %}
                            </label>
                            <div class="form-text">{% trans "تضمين الصور وملفات الوسائط الأخرى في النسخة الاحتياطية. قد يزيد هذا من حجم النسخة الاحتياطية بشكل كبير." %}</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="encrypt_backup_manual" name="encrypt_backup_manual" {% if backup_setting.encrypt_backups %}checked{% endif %}>
                            <label class="form-check-label" for="encrypt_backup_manual">
                                {% trans "تشفير النسخة الاحتياطية" %}
                            </label>
                            <div class="form-text">{% trans "تشفير النسخة الاحتياطية لحماية البيانات الحساسة." %}</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="storage_type_manual" class="form-label">{% trans "موقع التخزين" %}</label>
                        <select class="form-select" id="storage_type_manual" name="storage_type_manual">
                            <option value="local" {% if backup_setting.storage_type == 'local' %}selected{% endif %}>{% trans "محلي (على الخادم)" %}</option>
                            <option value="google_drive" {% if backup_setting.storage_type == 'google_drive' %}selected{% endif %}>Google Drive</option>
                            <option value="dropbox" {% if backup_setting.storage_type == 'dropbox' %}selected{% endif %}>Dropbox</option>
                        </select>
                        <div class="form-text">{% trans "موقع تخزين النسخة الاحتياطية. تأكد من ربط الحساب السحابي أولاً إذا اخترت خيارًا سحابيًا." %}</div>
                    </div>
                    <div class="mb-3">
                        <label for="notes_manual" class="form-label">{% trans "ملاحظات" %}</label>
                        <textarea class="form-control" id="notes_manual" name="notes_manual" rows="2"></textarea>
                        <div class="form-text">{% trans "ملاحظات اختيارية حول هذه النسخة الاحتياطية." %}</div>
                    </div>
                </form>
                <div id="backupProgress" style="display: none;">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <p id="backupStatus">{% trans "جاري التحضير للنسخ الاحتياطي..." %}</p>
                </div>
                <div id="backupResult" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
                <button type="submit" form="createBackupForm" class="btn btn-primary" id="startBackupBtn">{% trans "بدء النسخ الاحتياطي" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Cloud Auth Modal -->
<div class="modal fade" id="cloudAuthModal" tabindex="-1" aria-labelledby="cloudAuthModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cloudAuthModalLabel">{% trans "ربط حساب" %} <span id="cloud_service_name_modal"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "لربط حساب" %} <span id="cloud_service_name_text"></span>، {% trans "اتبع الخطوات التالية:" %}</p>

                <ol id="google_drive_steps" style="display: none;">
                    <li>{% trans "انقر على زر 'المصادقة مع Google' أدناه." %}</li>
                    <li>{% trans "قم بتسجيل الدخول إلى حساب Google الخاص بك." %}</li>
                    <li>{% trans "امنح الإذن للتطبيق للوصول إلى Google Drive." %}</li>
                    <li>{% trans "سيتم إعادة توجيهك إلى هذه الصفحة بعد المصادقة." %}</li>
                </ol>

                <ol id="dropbox_steps" style="display: none;">
                    <li>{% trans "انقر على زر 'المصادقة مع Dropbox' أدناه." %}</li>
                    <li>{% trans "قم بتسجيل الدخول إلى حساب Dropbox الخاص بك." %}</li>
                    <li>{% trans "امنح الإذن للتطبيق للوصول إلى Dropbox." %}</li>
                    <li>{% trans "سيتم إعادة توجيهك إلى هذه الصفحة بعد المصادقة." %}</li>
                </ol>

                <div class="text-center mt-4">
                    <button type="button" class="btn btn-primary" id="googleAuthBtn" style="display: none;">
                        <i class="fab fa-google me-1"></i> {% trans "المصادقة مع Google" %}
                    </button>
                    <button type="button" class="btn btn-primary" id="dropboxAuthBtn" style="display: none;">
                        <i class="fab fa-dropbox me-1"></i> {% trans "المصادقة مع Dropbox" %}
                    </button>
                </div>

                <div id="cloudAuthStatus" class="mt-3" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إغلاق" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Storage Type Change
        const storageTypeSelect = document.getElementById('storage_type');
        const storagePathContainer = document.getElementById('storage_path_container');
        const cloudAuthContainer = document.getElementById('cloud_auth_container');
        const cloudServiceName = document.getElementById('cloud_service_name');

        storageTypeSelect.addEventListener('change', function() {
            if (this.value === 'local') {
                storagePathContainer.style.display = 'block';
                cloudAuthContainer.style.display = 'none';
            } else {
                storagePathContainer.style.display = 'none';
                cloudAuthContainer.style.display = 'block';
                cloudServiceName.textContent = this.value === 'google_drive' ? 'Google Drive' : 'Dropbox';
            }
        });

        // Encryption Settings Toggle
        const encryptBackupsCheckbox = document.getElementById('encrypt_backups');
        const encryptionAlgorithmContainer = document.getElementById('encryption_algorithm_container');

        encryptBackupsCheckbox.addEventListener('change', function() {
            encryptionAlgorithmContainer.style.display = this.checked ? 'block' : 'none';
        });

        // Cloud Auth Button
        const cloudAuthBtn = document.getElementById('cloudAuthBtn');
        const cloudAuthModal = new bootstrap.Modal(document.getElementById('cloudAuthModal'));
        const cloudServiceNameModal = document.getElementById('cloud_service_name_modal');
        const cloudServiceNameText = document.getElementById('cloud_service_name_text');
        const googleDriveSteps = document.getElementById('google_drive_steps');
        const dropboxSteps = document.getElementById('dropbox_steps');
        const googleAuthBtn = document.getElementById('googleAuthBtn');
        const dropboxAuthBtn = document.getElementById('dropboxAuthBtn');

        cloudAuthBtn.addEventListener('click', function() {
            const storageType = storageTypeSelect.value;

            cloudServiceNameModal.textContent = storageType === 'google_drive' ? 'Google Drive' : 'Dropbox';
            cloudServiceNameText.textContent = storageType === 'google_drive' ? 'Google Drive' : 'Dropbox';

            if (storageType === 'google_drive') {
                googleDriveSteps.style.display = 'block';
                dropboxSteps.style.display = 'none';
                googleAuthBtn.style.display = 'inline-block';
                dropboxAuthBtn.style.display = 'none';
            } else {
                googleDriveSteps.style.display = 'none';
                dropboxSteps.style.display = 'block';
                googleAuthBtn.style.display = 'none';
                dropboxAuthBtn.style.display = 'inline-block';
            }

            cloudAuthModal.show();
        });

        // Create Backup Button
        const createBackupBtn = document.getElementById('createBackupBtn');
        const createBackupModal = new bootstrap.Modal(document.getElementById('createBackupModal'));
        const startBackupBtn = document.getElementById('startBackupBtn');
        const backupProgress = document.getElementById('backupProgress');
        const backupResult = document.getElementById('backupResult');
        const progressBar = document.querySelector('.progress-bar');
        const backupStatus = document.getElementById('backupStatus');

        // Storage Type Manual Change
        const storageTypeManualSelect = document.getElementById('storage_type_manual');

        // Sync storage type between settings and manual backup
        storageTypeSelect.addEventListener('change', function() {
            storageTypeManualSelect.value = this.value;
        });

        createBackupBtn.addEventListener('click', function() {
            backupProgress.style.display = 'none';
            backupResult.style.display = 'none';
            progressBar.style.width = '0%';
            createBackupModal.show();
        });

        startBackupBtn.addEventListener('click', function(e) {
            e.preventDefault();
            backupProgress.style.display = 'block';
            progressBar.style.width = '100%';
            backupStatus.textContent = '{% trans "جاري إنشاء النسخة الاحتياطية..." %}';

            // Submit the form
            document.getElementById('createBackupForm').submit();
        });

        // Cleanup Backups Button
        const cleanupBackupsBtn = document.getElementById('cleanupBackupsBtn');
        const cleanupBackupsForm = document.getElementById('cleanupBackupsForm');

        if (cleanupBackupsBtn) {
            cleanupBackupsBtn.addEventListener('click', function() {
                if (confirm('{% trans "هل أنت متأكد من رغبتك في حذف النسخ الاحتياطية القديمة؟ سيتم حذف النسخ الأقدم من فترة الاحتفاظ المحددة." %}')) {
                    cleanupBackupsForm.submit();
                }
            });
        }

        // Google Auth Button
        googleAuthBtn.addEventListener('click', function() {
            const cloudAuthStatus = document.getElementById('cloudAuthStatus');
            cloudAuthStatus.innerHTML = '<div class="alert alert-info">{% trans "جاري فتح صفحة المصادقة..." %}</div>';
            cloudAuthStatus.style.display = 'block';

            // In a real implementation, you would redirect to Google OAuth page
            // For this demo, we'll simulate a successful authentication after a delay
            setTimeout(function() {
                cloudAuthStatus.innerHTML = '<div class="alert alert-success">{% trans "تم ربط حساب Google Drive بنجاح!" %}</div>';
            }, 2000);
        });

        // Dropbox Auth Button
        dropboxAuthBtn.addEventListener('click', function() {
            const cloudAuthStatus = document.getElementById('cloudAuthStatus');
            cloudAuthStatus.innerHTML = '<div class="alert alert-info">{% trans "جاري فتح صفحة المصادقة..." %}</div>';
            cloudAuthStatus.style.display = 'block';

            // In a real implementation, you would redirect to Dropbox OAuth page
            // For this demo, we'll simulate a successful authentication after a delay
            setTimeout(function() {
                cloudAuthStatus.innerHTML = '<div class="alert alert-success">{% trans "تم ربط حساب Dropbox بنجاح!" %}</div>';
            }, 2000);
        });
    });
</script>
{% endblock %}
