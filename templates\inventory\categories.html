{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة فئات المنتجات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes - مطابق لصفحة لوحة التحكم */
    body .navbar .dropdown-menu {
        max-height: none !important;
        overflow: visible !important;
        border: 1px solid rgba(0,0,0,.15) !important;
        border-radius: 0.375rem !important;
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15) !important;
        background-color: white !important;
        min-width: 10rem !important;
        margin-top: 0.5rem !important;
    }

    body .navbar .dropdown-item {
        padding: 0.5rem 1rem !important;
        white-space: nowrap !important;
        color: #374151 !important;
        background-color: transparent !important;
        font-weight: normal !important;
    }

    body .navbar .dropdown-item:hover,
    body .navbar .dropdown-item:focus {
        background-color: #f8f9fa !important;
        color: #0d6efd !important;
        text-decoration: none !important;
    }

    body .navbar .dropdown-item i {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    .category-card {
        transition: all 0.3s;
        height: 100%;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .category-count {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: #0d6efd;
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .pagination-numbers {
        display: flex;
        gap: 5px;
    }

    .page-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #dee2e6;
        cursor: pointer;
        transition: all 0.2s;
    }

    .page-number:hover {
        background-color: #e9ecef;
    }

    .page-number.active {
        background-color: #0d6efd;
        color: white;
        border-color: #0d6efd;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-tags me-3"></i>
                        {% trans "إدارة فئات المنتجات" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "تنظيم وإدارة فئات المنتجات لتصنيف أفضل وإدارة محسنة للمخزون" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "فئة جديدة" %}
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "إدارة" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportCategories()">
                                        <i class="fas fa-file-excel text-success me-2"></i>{% trans "تصدير الفئات" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="importCategories()">
                                        <i class="fas fa-file-import text-primary me-2"></i>{% trans "استيراد فئات" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="bulkDeleteCategories()">
                                        <i class="fas fa-trash text-danger me-2"></i>{% trans "حذف مجمع" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="mergeCategories()">
                                        <i class="fas fa-code-branch text-warning me-2"></i>{% trans "دمج فئات" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="viewCategoryStats()">
                                        <i class="fas fa-chart-bar text-info me-2"></i>{% trans "إحصائيات الفئات" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="generateCategoryReport()">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>{% trans "تقرير الفئات" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'inventory:index' %}">
                                            <i class="fas fa-warehouse me-1"></i>{% trans "المخزون" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "الفئات" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid">

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Search and Filter Bar -->
<div class="card shadow mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <form method="get" action="{% url 'inventory:categories' %}" id="searchForm">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="{% trans 'بحث عن فئة...' %}" name="search" value="{{ search_query }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        {% if search_query %}
                        <a href="{% url 'inventory:categories' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
            <div class="col-md-6 text-md-end mt-3 mt-md-0">
                <div class="d-flex justify-content-md-end align-items-center">
                    <span class="me-2">{% trans "عرض" %}</span>
                    <select id="itemsPerPage" class="form-select form-select-sm" style="width: auto;">
                        <option value="4">4</option>
                        <option value="8" selected>8</option>
                        <option value="12">12</option>
                        <option value="16">16</option>
                        <option value="20">20</option>
                        <option value="all">{% trans "الكل" %}</option>
                    </select>
                    <span class="ms-2">{% trans "فئة" %}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Categories Stats -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <span class="text-muted">{% trans "إجمالي الفئات:" %} <strong>{{ total_categories }}</strong></span>
    </div>
    <div id="paginationInfo" class="text-muted">
        {% trans "عرض" %} <span id="showingFrom">1</span>-<span id="showingTo">8</span> {% trans "من" %} {{ total_categories }}
    </div>
</div>

<!-- Categories Grid -->
<div id="categoriesContainer">
    <div class="row" id="categoriesGrid">
        {% for category in categories %}
        <div class="col-xl-3 col-md-6 mb-4 category-item">
            <div class="card shadow category-card">
                <div class="card-body">
                    <div class="category-count">{{ category.products.count }}</div>
                    <h5 class="card-title text-primary">{{ category.name }}</h5>
                    <p class="card-text text-muted">
                        {% if category.description %}
                        {{ category.description|truncatechars:100 }}
                        {% else %}
                        <span class="text-muted">{% trans "لا يوجد وصف" %}</span>
                        {% endif %}
                    </p>
                    <div class="text-muted small mb-3">
                        <i class="fas fa-calendar-alt me-1"></i> {% trans "تاريخ الإنشاء:" %} {{ category.created_at|date:"Y-m-d" }}
                    </div>
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-sm btn-primary edit-category"
                                data-id="{{ category.id }}"
                                data-name="{{ category.name }}"
                                data-description="{{ category.description|default:'' }}"
                                data-bs-toggle="modal"
                                data-bs-target="#editCategoryModal">
                            <i class="fas fa-edit me-1"></i> {% trans "تعديل" %}
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-category"
                                data-id="{{ category.id }}"
                                data-name="{{ category.name }}"
                                data-count="{{ category.products.count }}"
                                data-bs-toggle="modal"
                                data-bs-target="#deleteCategoryModal">
                            <i class="fas fa-trash me-1"></i> {% trans "حذف" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-1"></i> {% trans "لا توجد فئات حاليًا. قم بإضافة فئة جديدة للبدء." %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination Controls -->
    <div class="d-flex justify-content-between align-items-center mt-4" id="paginationControls">
        <div>
            <button id="prevPage" class="btn btn-sm btn-outline-primary" disabled>
                <i class="fas fa-chevron-right me-1"></i> {% trans "السابق" %}
            </button>
        </div>
        <div id="pageNumbers" class="pagination-numbers">
            <!-- Page numbers will be inserted here by JavaScript -->
        </div>
        <div>
            <button id="nextPage" class="btn btn-sm btn-outline-primary">
                {% trans "التالي" %} <i class="fas fa-chevron-left ms-1"></i>
            </button>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">{% trans "إضافة فئة جديدة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'inventory:add_category' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">{% trans "اسم الفئة" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">{% trans "وصف الفئة" %}</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "إضافة" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel">{% trans "تعديل الفئة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'inventory:edit_category' 0 %}" id="editCategoryForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">{% trans "اسم الفئة" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">{% trans "وصف الفئة" %}</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ التغييرات" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCategoryModalLabel">{% trans "حذف الفئة" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{% trans "هل أنت متأكد من رغبتك في حذف الفئة:" %} <strong id="delete_category_name"></strong>؟</p>
                <div id="category_has_products" class="alert alert-warning d-none">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <span id="products_count_message"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                <form method="post" action="{% url 'inventory:delete_category' 0 %}" id="deleteCategoryForm">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">{% trans "حذف" %}</button>
                </form>
            </div>
        </div>
    </div>
</div>
</div>
</div>
{% endblock %}



{% block extra_js %}
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportCategories() {
        // تصدير جميع الفئات إلى Excel
        const categories = [];
        document.querySelectorAll('.category-card').forEach(card => {
            const name = card.querySelector('.card-title').textContent.trim();
            const description = card.querySelector('.card-text').textContent.trim();
            const productsCount = card.querySelector('.category-count').textContent.trim();
            const createdAt = card.querySelector('.text-muted.small').textContent.replace('تاريخ الإنشاء:', '').trim();

            categories.push({
                name: name,
                description: description,
                products_count: productsCount,
                created_at: createdAt
            });
        });

        if (categories.length === 0) {
            alert('{% trans "لا توجد فئات للتصدير" %}');
            return;
        }

        // تحويل إلى CSV وتحميل
        const csvContent = convertToCSV(categories, ['اسم الفئة', 'الوصف', 'عدد المنتجات', 'تاريخ الإنشاء']);
        downloadCSV(csvContent, 'categories_export.csv');
    }

    function importCategories() {
        // إنشاء input file مخفي لاستيراد الفئات
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.csv,.xlsx,.json';
        input.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        let data;
                        if (file.name.endsWith('.json')) {
                            data = JSON.parse(e.target.result);
                        } else {
                            // معالجة ملفات CSV
                            data = parseCSV(e.target.result);
                        }
                        alert('{% trans "تم استيراد" %} ' + data.length + ' {% trans "فئة بنجاح" %}');
                        console.log('Imported categories:', data);
                    } catch (error) {
                        alert('{% trans "خطأ في قراءة الملف" %}');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    function bulkDeleteCategories() {
        // حذف مجمع للفئات المختارة
        const selectedCategories = document.querySelectorAll('.category-select:checked');
        if (selectedCategories.length === 0) {
            alert('{% trans "يرجى اختيار فئة واحدة على الأقل للحذف" %}');
            return;
        }

        if (confirm('{% trans "هل أنت متأكد من حذف" %} ' + selectedCategories.length + ' {% trans "فئة؟" %}')) {
            // تنفيذ عملية الحذف المجمع
            alert('{% trans "سيتم حذف" %} ' + selectedCategories.length + ' {% trans "فئة" %}');
        }
    }

    function mergeCategories() {
        // دمج فئات متعددة في فئة واحدة
        const selectedCategories = document.querySelectorAll('.category-select:checked');
        if (selectedCategories.length < 2) {
            alert('{% trans "يرجى اختيار فئتين على الأقل للدمج" %}');
            return;
        }

        const targetCategory = prompt('{% trans "أدخل اسم الفئة الجديدة بعد الدمج:" %}');
        if (targetCategory) {
            alert('{% trans "سيتم دمج" %} ' + selectedCategories.length + ' {% trans "فئة في فئة واحدة:" %} ' + targetCategory);
        }
    }

    function viewCategoryStats() {
        // عرض إحصائيات الفئات
        const totalCategories = document.querySelectorAll('.category-card').length;
        const totalProducts = Array.from(document.querySelectorAll('.category-count'))
            .reduce((sum, el) => sum + parseInt(el.textContent), 0);

        alert('{% trans "إحصائيات الفئات:" %}\n' +
              '{% trans "إجمالي الفئات:" %} ' + totalCategories + '\n' +
              '{% trans "إجمالي المنتجات:" %} ' + totalProducts);
    }

    function generateCategoryReport() {
        // إنشاء تقرير PDF للفئات
        window.print();
    }

    function convertToCSV(data, headers) {
        const csvRows = [headers.join(',')];

        data.forEach(row => {
            const values = headers.map(header => {
                switch(header) {
                    case 'اسم الفئة': return row.name;
                    case 'الوصف': return row.description;
                    case 'عدد المنتجات': return row.products_count;
                    case 'تاريخ الإنشاء': return row.created_at;
                    default: return '';
                }
            });
            csvRows.push(values.join(','));
        });

        return csvRows.join('\n');
    }

    function downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
    }

    function parseCSV(csvText) {
        const lines = csvText.split('\n');
        const headers = lines[0].split(',');
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
                const values = lines[i].split(',');
                const row = {};
                headers.forEach((header, index) => {
                    row[header.trim()] = values[index] ? values[index].trim() : '';
                });
                data.push(row);
            }
        }

        return data;
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'inventory:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // Edit Category
        $('.edit-category').click(function() {
            const categoryId = $(this).data('id');
            const categoryName = $(this).data('name');
            const categoryDescription = $(this).data('description');

            $('#edit_name').val(categoryName);
            $('#edit_description').val(categoryDescription);

            // Update form action URL
            const formAction = "{% url 'inventory:edit_category' 0 %}".replace('0', categoryId);
            $('#editCategoryForm').attr('action', formAction);
        });

        // Delete Category
        $('.delete-category').click(function() {
            const categoryId = $(this).data('id');
            const categoryName = $(this).data('name');
            const productsCount = $(this).data('count');

            $('#delete_category_name').text(categoryName);

            // Show warning if category has products
            if (productsCount > 0) {
                $('#category_has_products').removeClass('d-none');
                $('#products_count_message').text(
                    `{% trans "هذه الفئة تحتوي على" %} ${productsCount} {% trans "منتج. حذف الفئة سيؤدي إلى حذف جميع المنتجات المرتبطة بها." %}`
                );
            } else {
                $('#category_has_products').addClass('d-none');
            }

            // Update form action URL
            const formAction = "{% url 'inventory:delete_category' 0 %}".replace('0', categoryId);
            $('#deleteCategoryForm').attr('action', formAction);
        });

        // Pagination and Items Per Page functionality
        const categoryItems = $('.category-item');
        const totalItems = categoryItems.length;
        let itemsPerPage = 8; // Default value
        let currentPage = 1;

        // Initialize pagination
        function initPagination() {
            // Hide all items initially
            categoryItems.hide();

            // Show items for the current page
            updatePageDisplay();

            // Create page number buttons
            updatePaginationControls();

            // Update pagination info
            updatePaginationInfo();
        }

        // Update which items are displayed based on current page and items per page
        function updatePageDisplay() {
            categoryItems.hide();

            if (itemsPerPage === 'all') {
                categoryItems.show();
                $('#paginationControls').hide();
            } else {
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + parseInt(itemsPerPage), totalItems);

                for (let i = startIndex; i < endIndex; i++) {
                    $(categoryItems[i]).show();
                }

                $('#paginationControls').show();
            }
        }

        // Update pagination controls (page numbers)
        function updatePaginationControls() {
            if (itemsPerPage === 'all') return;

            const totalPages = Math.ceil(totalItems / itemsPerPage);
            $('#pageNumbers').empty();

            // Create page number buttons
            for (let i = 1; i <= totalPages; i++) {
                const pageButton = $('<div class="page-number">' + i + '</div>');

                if (i === currentPage) {
                    pageButton.addClass('active');
                }

                pageButton.click(function() {
                    currentPage = i;
                    updatePageDisplay();
                    updatePaginationControls();
                    updatePaginationInfo();
                });

                $('#pageNumbers').append(pageButton);
            }

            // Update previous/next buttons
            $('#prevPage').prop('disabled', currentPage === 1);
            $('#nextPage').prop('disabled', currentPage === totalPages);
        }

        // Update pagination info text
        function updatePaginationInfo() {
            if (itemsPerPage === 'all') {
                $('#showingFrom').text('1');
                $('#showingTo').text(totalItems);
            } else {
                const startIndex = (currentPage - 1) * itemsPerPage + 1;
                const endIndex = Math.min(startIndex + parseInt(itemsPerPage) - 1, totalItems);

                $('#showingFrom').text(startIndex);
                $('#showingTo').text(endIndex);
            }
        }

        // Items per page change handler
        $('#itemsPerPage').change(function() {
            itemsPerPage = $(this).val();
            currentPage = 1;
            initPagination();
        });

        // Previous page button handler
        $('#prevPage').click(function() {
            if (currentPage > 1) {
                currentPage--;
                updatePageDisplay();
                updatePaginationControls();
                updatePaginationInfo();
            }
        });

        // Next page button handler
        $('#nextPage').click(function() {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                updatePageDisplay();
                updatePaginationControls();
                updatePaginationInfo();
            }
        });

        // Initialize pagination on page load
        initPagination();

        // تهيئة القوائم المنسدلة في الشريط العلوي
        initializeNavbarDropdowns();
    });

    // تهيئة القوائم المنسدلة في الشريط العلوي
    function initializeNavbarDropdowns() {
        // Re-initialize all Bootstrap dropdowns
        const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
        const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });

        // Ensure dropdowns work properly
        $('.dropdown-toggle').off('click.bs.dropdown').on('click', function(e) {
            e.preventDefault();
            const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
            dropdown.toggle();
        });

        // Fix navbar dropdown items layout
        fixNavbarDropdownLayout();

        // Fix admin dropdown position
        fixAdminDropdownPosition();
    }

    // إصلاح تخطيط عناصر القائمة المنسدلة
    function fixNavbarDropdownLayout() {
        $('.navbar .dropdown-item').each(function() {
            const $item = $(this);
            const $icon = $item.find('i');
            const text = $item.text().trim();

            if ($icon.length > 0 && text) {
                // إعادة ترتيب المحتوى - الأيقونة أولاً ثم النص
                $item.html(`
                    <i class="${$icon.attr('class')}"></i>
                    <span class="dropdown-text">${text}</span>
                `);
            }
        });
    }

    // إصلاح موضع قائمة admin لمنع التمرير الأفقي
    function fixAdminDropdownPosition() {
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) {
            const dropdownMenu = userDropdown.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                // إضافة class خاص لقائمة المستخدم
                dropdownMenu.classList.add('dropdown-menu-end');

                // ضبط الموضع
                dropdownMenu.style.right = '0';
                dropdownMenu.style.left = 'auto';
                dropdownMenu.style.transform = 'none';
                dropdownMenu.style.minWidth = '160px';
                dropdownMenu.style.maxWidth = '180px';
            }
        }

        // منع التمرير الأفقي في الصفحة
        document.body.style.overflowX = 'hidden';
    }
</script>
{% endblock %}
