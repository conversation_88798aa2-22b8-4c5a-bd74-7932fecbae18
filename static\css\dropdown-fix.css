/* Bootstrap Dropdown Fixes */
.dropdown-menu {
    z-index: 1050 !important;
    border: none;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    background: white;
    min-width: 200px;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    background: none;
    color: #374151;
    font-weight: 500;
    text-decoration: none;
    display: block;
    width: 100%;
    clear: both;
    white-space: nowrap;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
}

.dropdown-item i {
    width: 20px;
    text-align: center;
    margin-left: 0.5rem;
}

/* Navbar Dropdown Specific */
.navbar .dropdown-menu {
    background: white;
    border: 1px solid rgba(0,0,0,0.1);
}

.navbar .dropdown-item {
    color: #374151;
    font-weight: 500;
}

.navbar .dropdown-item:hover {
    background: #f8f9fa;
    color: #0d6efd;
}

/* Force dropdown visibility */
.dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

/* Dropdown toggle button */
.dropdown-toggle::after {
    margin-right: 0.5rem;
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

.dropdown-toggle:empty::after {
    margin-right: 0;
}

/* RTL Support for dropdowns */
.dropdown-menu[data-bs-popper] {
    left: auto !important;
    right: 0 !important;
}

/* Dropdown animation */
.dropdown-menu {
    transition: all 0.3s ease;
    transform: translateY(-10px);
    opacity: 0;
}

.dropdown-menu.show {
    transform: translateY(0);
    opacity: 1;
}

/* Modern dropdown styling */
.modern-dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    z-index: 1050 !important;
    background: white;
}

.modern-dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    background: none;
    color: #374151;
    font-weight: 500;
}

.modern-dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modern-dropdown-item i {
    width: 20px;
    text-align: center;
    margin-left: 0.5rem;
}

/* Fix for Bootstrap 5 dropdown positioning */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.375rem;
}

/* Ensure dropdowns work on mobile */
@media (max-width: 768px) {
    .dropdown-menu {
        position: static !important;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }
    
    .navbar-nav .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }
}

/* Debug styles - remove in production */
.dropdown-toggle {
    cursor: pointer !important;
}

.dropdown-menu.show {
    background-color: white !important;
    border: 1px solid #dee2e6 !important;
}
