{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/settings.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* تطبيق خط Cairo على الصفحة */
    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    /* تصحيح حجم الخط في شريط التنقل */
    .nav-link {
        font-size: 1.1rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
    
    .navbar-brand {
        font-size: 1.2rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
</style>
{% endblock %}

{% block title %}{% trans "الإعدادات العامة" %} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% trans "الإعدادات العامة" %}</h1>
        <a href="{% url 'settings_app:index' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> {% trans "العودة" %}
        </a>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات الشركة" %}</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="row mb-4">
                    <div class="col-md-3 text-center">
                        {% if company_info.logo %}
                            <img src="{{ company_info.logo.url }}" alt="Company Logo" class="img-fluid mb-3" style="max-height: 150px;">
                        {% else %}
                            <div class="border rounded p-3 mb-3 text-muted">
                                <i class="fas fa-image fa-5x"></i>
                                <p class="mt-2">{% trans "لا يوجد شعار" %}</p>
                            </div>
                        {% endif %}
                        <div class="mb-3">
                            <label for="logo" class="form-label">{% trans "شعار الشركة" %}</label>
                            <input type="file" class="form-control" id="logo" name="logo">
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">{% trans "اسم الشركة" %}</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" value="{{ company_info.name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">{% trans "رقم الهاتف" %}</label>
                                <input type="text" class="form-control" id="phone" name="phone" value="{{ company_info.phone }}" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">{% trans "البريد الإلكتروني" %}</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ company_info.email }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="website" class="form-label">{% trans "الموقع الإلكتروني" %}</label>
                                <input type="text" class="form-control" id="website" name="website" value="{{ company_info.website }}" placeholder="{% trans "أدخل عنوان الموقع الإلكتروني (اختياري)" %}">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">{% trans "الرقم الضريبي" %}</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number" value="{{ company_info.tax_number }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="commercial_register" class="form-label">{% trans "رقم السجل التجاري" %}</label>
                                <input type="text" class="form-control" id="commercial_register" name="commercial_register" value="{{ company_info.commercial_register }}">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="address" class="form-label">{% trans "العنوان" %}</label>
                    <textarea class="form-control" id="address" name="address" rows="3" required>{{ company_info.address }}</textarea>
                </div>

                <div class="mb-3">
                    <label for="footer_text" class="form-label">{% trans "نص التذييل (يظهر في الفواتير)" %}</label>
                    <textarea class="form-control" id="footer_text" name="footer_text" rows="3">{{ company_info.footer_text }}</textarea>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-1"></i> {% trans "حفظ التغييرات" %}
                    </button>
                </div>
                <!-- مسافة إضافية لضمان عدم تداخل الشريط السفلي مع زر الحفظ -->
                <div style="height: 30px;"></div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
