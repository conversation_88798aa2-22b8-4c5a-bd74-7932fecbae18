// Main JavaScript for Auto Parts POS System

// إعداد CSRF token لجميع طلبات AJAX
$(document).ready(function() {
    // إعداد CSRF token لجميع طلبات AJAX
    var csrftoken = $('input[name="csrfmiddlewaretoken"]').val();
    
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("X-CSRFToken", csrftoken);
            }
        }
    });
});

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // تهيئة القوائم المنسدلة
    var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Current year for footer
    document.querySelectorAll('.current-year').forEach(function(el) {
        el.textContent = new Date().getFullYear();
    });

    // Print invoice functionality
    const printButtons = document.querySelectorAll('.print-button');
    if (printButtons) {
        printButtons.forEach(button => {
            button.addEventListener('click', function() {
                window.print();
            });
        });
    }

    // Search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const searchableItems = document.querySelectorAll('.searchable-item');

            searchableItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Mobile sidebar toggle
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });

        // Close sidebar when clicking outside
        document.addEventListener('click', function(event) {
            if (!sidebar.contains(event.target) && !sidebarToggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    if (forms) {
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }

    // Dynamic form fields (add/remove)
    const addFieldButton = document.getElementById('add-field');
    const removeFieldButtons = document.querySelectorAll('.remove-field');
    const fieldContainer = document.getElementById('dynamic-fields');

    if (addFieldButton && fieldContainer) {
        addFieldButton.addEventListener('click', function() {
            const fieldCount = fieldContainer.children.length;
            const newField = document.createElement('div');
            newField.className = 'row mb-3 dynamic-field';
            newField.innerHTML = `
                <div class="col-md-5">
                    <input type="text" class="form-control" name="item_name_${fieldCount}" placeholder="اسم العنصر" required>
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control" name="item_quantity_${fieldCount}" placeholder="الكمية" required>
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control" name="item_price_${fieldCount}" placeholder="السعر" required>
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger remove-field"><i class="fas fa-times"></i></button>
                </div>
            `;
            fieldContainer.appendChild(newField);

            // Add event listener to the new remove button
            newField.querySelector('.remove-field').addEventListener('click', function() {
                fieldContainer.removeChild(newField);
            });
        });
    }

    if (removeFieldButtons) {
        removeFieldButtons.forEach(button => {
            button.addEventListener('click', function() {
                const field = this.closest('.dynamic-field');
                field.parentNode.removeChild(field);
            });
        });
    }

    // Auto calculate totals in invoices
    function calculateTotals() {
        const quantityInputs = document.querySelectorAll('.item-quantity');
        const priceInputs = document.querySelectorAll('.item-price');
        const subtotalElements = document.querySelectorAll('.item-subtotal');

        let total = 0;

        for (let i = 0; i < quantityInputs.length; i++) {
            const quantity = parseFloat(quantityInputs[i].value) || 0;
            const price = parseFloat(priceInputs[i].value) || 0;
            const subtotal = quantity * price;

            if (subtotalElements[i]) {
                subtotalElements[i].textContent = subtotal.toFixed(2);
            }

            total += subtotal;
        }

        const totalElement = document.getElementById('total-amount');
        if (totalElement) {
            totalElement.textContent = total.toFixed(2);
        }

        const taxRateInput = document.getElementById('tax-rate');
        if (taxRateInput) {
            const taxRate = parseFloat(taxRateInput.value) || 0;
            const taxAmount = total * (taxRate / 100);

            const taxElement = document.getElementById('tax-amount');
            if (taxElement) {
                taxElement.textContent = taxAmount.toFixed(2);
            }

            const grandTotalElement = document.getElementById('grand-total');
            if (grandTotalElement) {
                grandTotalElement.textContent = (total + taxAmount).toFixed(2);
            }
        }
    }

    const quantityInputs = document.querySelectorAll('.item-quantity');
    const priceInputs = document.querySelectorAll('.item-price');
    const taxRateInput = document.getElementById('tax-rate');

    if (quantityInputs && priceInputs) {
        quantityInputs.forEach(input => {
            input.addEventListener('input', calculateTotals);
        });

        priceInputs.forEach(input => {
            input.addEventListener('input', calculateTotals);
        });

        if (taxRateInput) {
            taxRateInput.addEventListener('input', calculateTotals);
        }

        // Initial calculation
        calculateTotals();
    }

    // Product search in sales form
    const productSearchInput = document.getElementById('product-search');
    if (productSearchInput) {
        productSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            // Make AJAX request to search products
            fetch(`/inventory/search/?term=${searchTerm}`)
                .then(response => response.json())
                .then(data => {
                    const resultsContainer = document.getElementById('search-results');
                    resultsContainer.innerHTML = '';

                    data.forEach(product => {
                        const productElement = document.createElement('div');
                        productElement.className = 'search-result-item p-2 border-bottom';
                        productElement.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <span>${product.name}</span>
                                <button type="button" class="btn btn-sm btn-primary add-product-btn"
                                        data-id="${product.id}"
                                        data-name="${product.name}"
                                        data-price="${product.price}">
                                    إضافة
                                </button>
                            </div>
                        `;
                        resultsContainer.appendChild(productElement);

                        // Add event listener to the add button
                        productElement.querySelector('.add-product-btn').addEventListener('click', function() {
                            const productId = this.getAttribute('data-id');
                            const productName = this.getAttribute('data-name');
                            const productPrice = this.getAttribute('data-price');

                            addProductToInvoice(productId, productName, productPrice);
                            resultsContainer.innerHTML = '';
                            productSearchInput.value = '';
                        });
                    });
                })
                .catch(error => console.error('Error searching products:', error));
        });
    }

    // Function to add product to invoice
    function addProductToInvoice(id, name, price) {
        const invoiceItems = document.getElementById('invoice-items');
        if (invoiceItems) {
            const newRow = document.createElement('tr');
            const rowCount = invoiceItems.querySelectorAll('tr').length;

            newRow.innerHTML = `
                <td>
                    <input type="hidden" name="product_id_${rowCount}" value="${id}">
                    ${name}
                </td>
                <td>
                    <input type="number" class="form-control item-quantity" name="quantity_${rowCount}" value="1" min="1">
                </td>
                <td>
                    <input type="number" class="form-control item-price" name="price_${rowCount}" value="${price}" readonly>
                </td>
                <td class="item-subtotal">${price}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-item"><i class="fas fa-trash"></i></button>
                </td>
            `;

            invoiceItems.appendChild(newRow);

            // Add event listeners to new inputs
            newRow.querySelector('.item-quantity').addEventListener('input', calculateTotals);
            newRow.querySelector('.item-price').addEventListener('input', calculateTotals);

            // Add event listener to remove button
            newRow.querySelector('.remove-item').addEventListener('click', function() {
                invoiceItems.removeChild(newRow);
                calculateTotals();
            });

            // Update totals
            calculateTotals();
        }
    }

    // Date range picker initialization
    const dateRangePicker = document.getElementById('date-range');
    if (dateRangePicker && typeof daterangepicker !== 'undefined') {
        new daterangepicker(dateRangePicker, {
            opens: 'right',
            locale: {
                format: 'YYYY/MM/DD',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                fromLabel: 'من',
                toLabel: 'إلى',
                customRangeLabel: 'مخصص',
                daysOfWeek: ['أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 0
            }
        });
    }
});
