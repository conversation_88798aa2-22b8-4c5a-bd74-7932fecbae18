from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    path('', views.index, name='index'),
    path('sales/', views.sales_report_new, name='sales_report'),
    path('sales/chart-data/', views.sales_chart_data, name='sales_chart_data'),
    path('inventory/', views.inventory_report, name='inventory_report'),
    path('customers/', views.customers_report, name='customers_report'),
    path('customer/<int:customer_id>/', views.customer_details, name='customer_details'),
    path('financial/', views.financial_report, name='financial_report'),
    path('export/sales/', views.export_sales_report, name='export_sales_report'),
    path('export/inventory/', views.export_inventory_report, name='export_inventory_report'),
    path('export/customers/', views.export_customers_report, name='export_customers_report'),
    path('export/financial/', views.export_financial_report, name='export_financial_report'),
    path('compare-periods/', views.compare_periods, name='compare_periods'),
]
