{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "إدارة العملاء" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }

    .stats-card {
        transition: all 0.3s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .customer-row {
        transition: all 0.2s;
    }

    .customer-row:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    .badge-active {
        background-color: #28a745;
    }

    .badge-inactive {
        background-color: #dc3545;
    }

    .search-box {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .top-customers-card {
        height: 100%;
    }

    .action-buttons .btn {
        margin-right: 5px;
    }

    @media (max-width: 768px) {
        .action-buttons .btn {
            margin-bottom: 5px;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-users me-3"></i>
                        {% trans "إدارة العملاء" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "إدارة شاملة لقاعدة بيانات العملاء مع إحصائيات متقدمة وأدوات تحليل" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <a href="{% url 'customers:add_customer' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "عميل جديد" %}
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download me-1"></i>
                                    {% trans "تصدير" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'customers:export_customers' %}">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel text-success me-2"></i>Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                        <i class="fas fa-file-csv text-primary me-2"></i>CSV
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'customers:import_customers' %}">
                                        <i class="fas fa-file-import text-warning me-2"></i>{% trans "استيراد" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "العملاء" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid">

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-4">
        <div class="card border-right-primary shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            {% trans "إجمالي العملاء" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card border-right-success shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            {% trans "العملاء النشطين" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card border-right-danger shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            {% trans "العملاء غير النشطين" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ inactive_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card border-right-info shadow h-100 py-2 stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            {% trans "متوسط المشتريات" %}
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% if total_customers > 0 %}
                                {{ total_amount|default:0|floatformat:2 }} د.م
                            {% else %}
                                0.00 د.م
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- بحث وتصفية -->
    <div class="col-md-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "بحث وتصفية" %}</h6>
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse">
                    <i class="fas fa-filter me-1"></i> {% trans "عرض/إخفاء" %}
                </button>
            </div>
            <div class="card-body collapse show" id="searchCollapse">
                <form method="get" id="searchForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="search" class="form-label">{% trans "بحث" %}</label>
                            <input type="text" class="form-control" id="search" name="search" value="{{ search_form.search.value|default:'' }}" placeholder="{% trans 'اسم، رقم هاتف، أو بريد إلكتروني' %}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">{% trans "الفئة" %}</label>
                            {{ search_form.category }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="is_active" class="form-label">{% trans "الحالة" %}</label>
                            {{ search_form.is_active }}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="has_purchases" class="form-label">{% trans "المشتريات" %}</label>
                            {{ search_form.has_purchases }}
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="city" class="form-label">{% trans "المدينة" %}</label>
                            {{ search_form.city }}
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> {% trans "بحث" %}
                        </button>
                        <a href="{% url 'customers:index' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-redo me-1"></i> {% trans "إعادة تعيين" %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- العملاء الأكثر شراءً -->
    <div class="col-md-4 mb-4">
        <div class="card shadow top-customers-card">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{% trans "العملاء الأكثر شراءً" %}</h6>
            </div>
            <div class="card-body">
                {% if top_customers %}
                    <div class="list-group">
                        {% for customer in top_customers %}
                            <a href="{% url 'customers:view_customer' customer_id=customer.id %}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ customer.name }}</h6>
                                    <small>{{ customer.total_spent|floatformat:2 }} د.م</small>
                                </div>
                                <small class="text-muted">{{ customer.purchase_count }} {% trans "عملية شراء" %}</small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>{% trans "لا توجد مشتريات بعد" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- قائمة العملاء -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "قائمة العملاء" %}</h6>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown">
                <i class="fas fa-sort me-1"></i> {% trans "ترتيب حسب" %}
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                <li>
                    <a class="dropdown-item {% if sort_by == 'name' and sort_order == 'asc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=name&sort_order=asc">
                        <i class="fas fa-sort-alpha-down me-1"></i> {% trans "الاسم (تصاعدي)" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {% if sort_by == 'name' and sort_order == 'desc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=name&sort_order=desc">
                        <i class="fas fa-sort-alpha-up me-1"></i> {% trans "الاسم (تنازلي)" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {% if sort_by == 'created_at' and sort_order == 'desc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=created_at&sort_order=desc">
                        <i class="fas fa-calendar-alt me-1"></i> {% trans "تاريخ الإضافة (الأحدث)" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {% if sort_by == 'created_at' and sort_order == 'asc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=created_at&sort_order=asc">
                        <i class="fas fa-calendar-alt me-1"></i> {% trans "تاريخ الإضافة (الأقدم)" %}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item {% if sort_by == 'last_purchase' and sort_order == 'desc' %}active{% endif %}"
                       href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'sort_by' and key != 'sort_order' and key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}sort_by=last_purchase&sort_order=desc">
                        <i class="fas fa-shopping-cart me-1"></i> {% trans "آخر شراء (الأحدث)" %}
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive" id="customerTableContainer">
            {% include 'customers/partials/customer_list.html' %}
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
    $(document).ready(function() {
        // تهيئة DataTable
        var table = $('#customersTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "ordering": false, // تعطيل الترتيب لأننا نستخدم ترتيب الخادم
            "searching": false, // تعطيل البحث لأننا نستخدم نموذج البحث الخاص بنا
            "info": false, // إخفاء معلومات الصفحات لأننا نستخدم ترقيم الصفحات الخاص بنا
            "paging": false // تعطيل تقسيم الصفحات لأننا نستخدم ترقيم الصفحات الخاص بنا
        });

        // تحديث الجدول عند تغيير الفلتر
        $('#searchForm').on('submit', function(e) {
            e.preventDefault();

            var formData = $(this).serialize();
            var url = "{% url 'customers:index' %}?" + formData;

            // تحديث عنوان URL
            history.pushState(null, '', url);

            // تحديث الجدول باستخدام AJAX
            $.ajax({
                url: url,
                type: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(data) {
                    $('#customerTableContainer').html(data.html);
                }
            });
        });

        // حذف العميل
        $(document).on('click', '.delete-customer', function(e) {
            e.preventDefault();

            var customerId = $(this).data('id');
            var customerName = $(this).data('name');

            if (confirm("{% trans 'هل أنت متأكد من حذف العميل: ' %}" + customerName + "؟")) {
                $.ajax({
                    url: "{% url 'customers:index' %}" + "delete/" + customerId + "/",
                    type: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    success: function(data) {
                        alert(data.message);
                        location.reload();
                    },
                    error: function(xhr) {
                        var response = JSON.parse(xhr.responseText);
                        alert(response.message);
                    }
                });
            }
        });
    });

    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportToExcel() {
        // تصدير البيانات إلى Excel
        const table = document.querySelector('#customersTable');
        if (table) {
            const wb = XLSX.utils.table_to_book(table);
            XLSX.writeFile(wb, 'customers_data.xlsx');
        }
    }

    function exportToCSV() {
        // تصدير البيانات إلى CSV
        const table = document.querySelector('#customersTable');
        if (table) {
            const csv = [];
            const rows = table.querySelectorAll('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');

                for (let j = 0; j < cols.length; j++) {
                    row.push(cols[j].innerText);
                }

                csv.push(row.join(','));
            }

            const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = 'customers_data.csv';
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'dashboard:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });
</script>
{% endblock %}
