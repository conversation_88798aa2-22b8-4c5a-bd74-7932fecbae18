from django import template
from django.db.models import Sum
from django.utils.safestring import mark_safe
import re

register = template.Library()

@register.filter
def sum_payments(payments):
    """حساب مجموع الدفعات"""
    return payments.aggregate(Sum('amount'))['amount__sum'] or 0

@register.filter
def format_mixed_text(text):
    """
    تنسيق النصوص المختلطة (عربي + فرنسي/إنجليزي) لتحسين العرض في PDF
    """
    if not text:
        return ""

    # تحويل النص إلى string إذا لم يكن كذلك
    text = str(text)

    # تحسين عرض النصوص اللاتينية المضمنة في النص العربي
    # البحث عن النصوص اللاتينية وتطبيق التنسيق المناسب
    def replace_latin_text(match):
        latin_text = match.group(1)
        # تجاهل النصوص القصيرة جداً (أقل من 3 أحرف) أو التي تحتوي على أرقام فقط
        if len(latin_text.strip()) < 3 or latin_text.strip().isdigit():
            return latin_text

        # تطبيق تنسيق خاص للنصوص اللاتينية
        return f'<span class="latin-text" dir="ltr">{latin_text}</span>'

    # البحث عن النصوص اللاتينية (أحرف إنجليزية/فرنسية مع المسافات وعلامات الترقيم)
    text = re.sub(r'([a-zA-ZÀ-ÿ\s\.,;:!?\-\(\)]+)', replace_latin_text, text)

    # تحسين عرض الأرقام
    text = re.sub(r'(\d+)', r'<span class="number">\1</span>', text)

    # تحسين عرض علامات الترقيم المهمة
    text = re.sub(r'([.,;:!?])', r'<span class="symbol">\1</span>', text)

    # تحسين المسافات والأسطر الجديدة
    text = text.replace('\n', '<br>')
    text = re.sub(r'\s+', ' ', text)  # تقليل المسافات المتعددة

    return mark_safe(text)

@register.filter
def clean_mixed_text(text):
    """
    تنظيف النصوص المختلطة للعرض البسيط
    """
    if not text:
        return ""

    text = str(text)

    # إزالة المسافات الزائدة
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()

    return text
