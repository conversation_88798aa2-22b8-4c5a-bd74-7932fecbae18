/**
 * إضافة ميزة التمرير إلى القوائم المنسدلة
 * هذا الملف يحتوي على وظائف لإضافة ميزة التمرير إلى جميع القوائم المنسدلة في التطبيق
 */

// تهيئة القوائم المنسدلة المخصصة
function initCustomDropdowns() {
    // تطبيق الوظيفة على كل حقل select له الفئة form-select
    $('.form-select').each(function() {
        const select = $(this);
        const selectId = select.attr('id');
        
        // تجاهل الحقول التي تم تهيئتها بالفعل
        if (select.data('custom-dropdown-initialized')) {
            return;
        }
        
        // إضافة wrapper إذا لم يكن موجودًا
        if (!select.parent().hasClass('select-wrapper')) {
            select.wrap('<div class="select-wrapper position-relative"></div>');
        }
        
        // إنشاء معرف فريد للقائمة المنسدلة
        const dropdownId = selectId ? `${selectId}_dropdown` : `dropdown_${Math.random().toString(36).substr(2, 9)}`;
        
        // إضافة القائمة المنسدلة المخصصة
        select.parent().append(`<div id="${dropdownId}" class="custom-dropdown"></div>`);
        
        // تهيئة القائمة المنسدلة
        initSingleDropdown(select, $(`#${dropdownId}`));
        
        // تعليم الحقل كمهيأ
        select.data('custom-dropdown-initialized', true);
    });
}

// تهيئة قائمة منسدلة واحدة
function initSingleDropdown(select, dropdown) {
    // إنشاء قائمة العناصر
    let options = '';
    
    // نسخ العناصر من القائمة المنسدلة الأصلية
    select.find('option').each(function() {
        const value = $(this).val();
        const text = $(this).text();
        const selected = $(this).is(':selected') ? 'selected' : '';
        options += `<div class="option-item ${selected}" data-value="${value}">${text}</div>`;
    });
    
    dropdown.html(options);
    
    // عرض القائمة عند النقر على حقل الاختيار
    select.on('mousedown focus', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // إخفاء جميع القوائم المنسدلة الأخرى
        $('.custom-dropdown').hide();
        
        // إظهار القائمة المنسدلة
        dropdown.show();
        
        // إضافة الشريط الأزرق حول حقل الاختيار
        $('.select-wrapper').removeClass('focused');
        $(this).closest('.select-wrapper').addClass('focused');
        
        // تحديد العنصر المحدد حاليًا
        const selectedValue = select.val();
        dropdown.find('.option-item').removeClass('selected');
        dropdown.find(`.option-item[data-value="${selectedValue}"]`).addClass('selected');
        
        // تمرير القائمة إلى العنصر المحدد
        const selectedItem = dropdown.find('.option-item.selected');
        if (selectedItem.length) {
            dropdown.scrollTop(selectedItem.index() * selectedItem.outerHeight());
        }
        
        return false;
    });
    
    // تحديث القيمة عند اختيار عنصر
    dropdown.on('click', '.option-item', function() {
        const value = $(this).data('value');
        const text = $(this).text();
        
        select.val(value);
        dropdown.hide();
        
        // تحديث العنصر المحدد
        dropdown.find('.option-item').removeClass('selected');
        $(this).addClass('selected');
        
        // الحفاظ على الشريط الأزرق بعد اختيار العنصر
        $('.select-wrapper').removeClass('focused');
        select.closest('.select-wrapper').addClass('focused');
        
        // تشغيل حدث التغيير لتنفيذ أي وظائف مرتبطة بتغيير القيمة
        select.trigger('change');
    });
}

// إخفاء القوائم المنسدلة عند النقر خارجها
$(document).on('click mousedown', function(e) {
    if (!$(e.target).closest('.select-wrapper').length) {
        $('.custom-dropdown').hide();
        $('.select-wrapper').removeClass('focused');
    }
});

// تهيئة القوائم المنسدلة عند تحميل الصفحة
$(document).ready(function() {
    initCustomDropdowns();
    
    // إعادة تهيئة القوائم المنسدلة عند إضافة عناصر جديدة للصفحة
    $(document).on('DOMNodeInserted', function(e) {
        if ($(e.target).find('.form-select').length > 0) {
            setTimeout(function() {
                initCustomDropdowns();
            }, 100);
        }
    });
});
