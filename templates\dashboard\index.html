{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "لوحة التحكم" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<!-- DataTables CSS -->
<link href="{% static 'vendor/datatables/dataTables.bootstrap4.min.css' %}" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background-color: #f8f9fa;
        color: #0d6efd;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* General Design */
    .card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1) !important;
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    
    .card-header {
        background-color: transparent;
        border-bottom: 1px solid rgba(0,0,0,.05);
        padding: 1rem 1.25rem;
    }
    
    /* Dashboard Cards */
    .dashboard-card {
        border-radius: 10px;
        transition: all 0.3s;
        height: 100%;
        overflow: hidden;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1) !important;
    }
    
    .dashboard-card .card-icon {
        font-size: 1.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-bottom: 0.5rem;
        color: white;
    }
    
    /* Card Titles and Stats */
    .card-title {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #5a5c69;
    }
    
    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    /* Tables */
    .table {
        margin-bottom: 0;
    }
    
    .table thead th {
        border-top: 0;
        font-weight: 600;
        font-size: 0.85rem;
        color: #5a5c69;
        padding: 1rem;
    }
    
    .table td {
        padding: 0.75rem 1rem;
        vertical-align: middle;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.02);
    }
    
    /* Charts */
    .chart-container {
        position: relative;
        margin: auto;
    }
    
    /* View All Buttons */
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.25rem;
    }
    
    /* DataTables */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.25rem 0.5rem;
        margin-left: 2px;
        border-radius: 0.25rem;
    }
    
    .dataTables_info, .dataTables_length, .dataTables_filter {
        font-size: 0.85rem;
    }
    
    /* Badges */
    .badge {
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 600;
        border-radius: 0.25rem;
    }
    
    /* Progress Bars */
    .progress {
        height: 6px;
        border-radius: 3px;
        background-color: rgba(0,0,0,.05);
        margin-top: 0.5rem;
    }
    
    /* Avatar */
    .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Icons in Cards */
    .icon-circle {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 2.5rem;
        width: 2.5rem;
        border-radius: 50%;
    }
    
    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .stats-number {
            font-size: 1.25rem;
        }
        
        .dashboard-card .card-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-tachometer-alt me-3"></i>
                        {% trans "لوحة التحكم الرئيسية" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "نظرة شاملة على أداء المتجر والإحصائيات الحية مع تحليلات متقدمة" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <button type="button" class="btn btn-success btn-sm" onclick="window.print()">
                                <i class="fas fa-print me-1"></i>
                                {% trans "طباعة" %}
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download me-1"></i>
                                    {% trans "تصدير" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                                        <i class="fas fa-file-excel text-success me-2"></i>Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                        <i class="fas fa-file-csv text-primary me-2"></i>CSV
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item active text-white">
                                        <i class="fas fa-home me-1"></i>{% trans "لوحة التحكم" %}
                                    </li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-primary">{% trans "الإحصائيات السريعة" %}</h2>
        <div>
            <span class="badge bg-primary">{% now "l, j F Y" %}</span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Sales Today -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "مبيعات اليوم" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ currency.symbol }} {{ sales_today|floatformat:2 }}</div>
                            <div class="text-muted mt-2 small">{% now "l, j F" %}</div>
                        </div>
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-calendar-day stat-icon text-primary"></i>
                        </div>
                    </div>
                    <div class="progress mt-3" style="height: 5px;">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ sales_today_percent|default:0 }}%" aria-valuenow="{{ sales_today_percent|default:0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales This Week -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "مبيعات الأسبوع" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ currency.symbol }} {{ sales_week|floatformat:2 }}</div>
                            <div class="text-muted mt-2 small">الأسبوع الحالي</div>
                        </div>
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-calendar-week stat-icon text-success"></i>
                        </div>
                    </div>
                    <div class="progress mt-3" style="height: 5px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ sales_week_percent|default:0 }}%" aria-valuenow="{{ sales_week_percent|default:0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales This Month -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "مبيعات الشهر" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ currency.symbol }} {{ sales_month|floatformat:2 }}</div>
                            <div class="text-muted mt-2 small">{% now "F Y" %}</div>
                        </div>
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-calendar-alt stat-icon text-info"></i>
                        </div>
                    </div>
                    <div class="progress mt-3" style="height: 5px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ sales_month_percent|default:0 }}%" aria-valuenow="{{ sales_month_percent|default:0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Profit -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {% trans "أرباح الشهر" %}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ currency.symbol }} {{ monthly_profit|floatformat:2 }}</div>
                            <div class="text-muted mt-2 small">{% now "F Y" %}</div>
                        </div>
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-dollar-sign stat-icon text-warning"></i>
                        </div>
                    </div>
                    <div class="progress mt-3" style="height: 5px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{ profit_percent|default:0 }}%" aria-valuenow="{{ profit_percent|default:0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row -->
    <div class="row mb-4">
        <!-- Total Products Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow h-100 dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "إجمالي المنتجات" %}</div>
                            <div class="h4 mb-0 font-weight-bold">{{ total_products }}</div>
                        </div>
                        <div class="card-icon bg-primary bg-opacity-10">
                            <i class="fas fa-box text-primary"></i>
                        </div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="small mt-2 text-muted">
                        <i class="fas fa-info-circle me-1"></i> {% trans "إجمالي المنتجات في المخزون" %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Products Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow h-100 dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                {% trans "منتجات منخفضة المخزون" %}</div>
                            <div class="h4 mb-0 font-weight-bold">{{ low_stock_products }}</div>
                        </div>
                        <div class="card-icon bg-danger bg-opacity-10">
                            <i class="fas fa-exclamation-triangle text-danger"></i>
                        </div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-danger" role="progressbar" style="width: {% widthratio low_stock_products total_products 100 %}%" aria-valuenow="{% widthratio low_stock_products total_products 100 %}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="small mt-2 text-muted">
                        <i class="fas fa-exclamation-circle me-1 text-danger"></i> {% widthratio low_stock_products total_products 100 %}% {% trans "من إجمالي المنتجات" %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Customers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow h-100 dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "إجمالي العملاء" %}</div>
                            <div class="h4 mb-0 font-weight-bold">{{ total_customers }}</div>
                        </div>
                        <div class="card-icon bg-success bg-opacity-10">
                            <i class="fas fa-users text-success"></i>
                        </div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="small mt-2 text-muted">
                        <i class="fas fa-info-circle me-1"></i> {% trans "إجمالي العملاء المسجلين" %}
                    </div>
                </div>
            </div>
        </div>

        <!-- New Customers This Month Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card shadow h-100 dashboard-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {% trans "عملاء جدد هذا الشهر" %}</div>
                            <div class="h4 mb-0 font-weight-bold">{{ new_customers_month }}</div>
                        </div>
                        <div class="card-icon bg-info bg-opacity-10">
                            <i class="fas fa-user-plus text-info"></i>
                        </div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-info" role="progressbar" style="width: {% widthratio new_customers_month total_customers 100 %}%" aria-valuenow="{% widthratio new_customers_month total_customers 100 %}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="small mt-2 text-muted">
                        <i class="fas fa-arrow-up text-success me-1"></i> {% widthratio new_customers_month total_customers 100 %}% {% trans "من إجمالي العملاء" %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Sales -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shopping-cart me-2"></i>{% trans "أحدث المبيعات" %}
                    </h6>
                    <a href="{% url 'sales:index' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-external-link-alt me-1"></i>{% trans "عرض الكل" %}
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="recentSalesTable">
                            <thead>
                                <tr>
                                    <th class="border-0">{% trans "رقم الفاتورة" %}</th>
                                    <th class="border-0">{% trans "العميل" %}</th>
                                    <th class="border-0">{% trans "التاريخ" %}</th>
                                    <th class="border-0">{% trans "المبلغ" %}</th>
                                    <th class="border-0">{% trans "الحالة" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in recent_sales %}
                                <tr>
                                    <td>
                                        <a href="{% url 'sales:view_sale' sale.id %}" class="fw-bold text-primary">{{ sale.invoice_number }}</a>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-sm me-2 bg-primary bg-opacity-10 rounded-circle text-center" style="width: 32px; height: 32px; line-height: 32px;">
                                                <i class="fas fa-user text-primary"></i>
                                            </div>
                                            <span>{{ sale.customer.name }}</span>
                                        </div>
                                    </td>
                                    <td>{{ sale.date|date:"Y-m-d H:i" }}</td>
                                    <td class="fw-bold">{{ sale.total_amount|floatformat:2 }} {{ currency.symbol }}</td>
                                    <td>
                                        {% if sale.status == 'completed' %}
                                        <span class="badge bg-success">{% trans "مكتمل" %}</span>
                                        {% elif sale.status == 'pending' %}
                                        <span class="badge bg-warning text-dark">{% trans "معلق" %}</span>
                                        {% else %}
                                        <span class="badge bg-danger">{% trans "ملغي" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-info-circle me-2"></i>{% trans "لا توجد مبيعات حديثة" %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Products -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>{% trans "منتجات منخفضة المخزون" %}
                    </h6>
                    <a href="{% url 'inventory:index' %}" class="btn btn-sm btn-danger">
                        <i class="fas fa-external-link-alt me-1"></i>{% trans "عرض الكل" %}
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="lowStockTable">
                            <thead>
                                <tr>
                                    <th class="border-0">{% trans "الكود" %}</th>
                                    <th class="border-0">{% trans "المنتج" %}</th>
                                    <th class="border-0">{% trans "الكمية الحالية" %}</th>
                                    <th class="border-0">{% trans "الحد الأدنى" %}</th>
                                    <th class="border-0">{% trans "الإجراء" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_items %}
                                <tr>
                                    <td class="fw-bold">{{ product.code }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-sm me-2 bg-secondary bg-opacity-10 rounded-circle text-center" style="width: 32px; height: 32px; line-height: 32px;">
                                                <i class="fas fa-box text-secondary"></i>
                                            </div>
                                            <span>{{ product.name }}</span>
                                        </div>
                                    </td>
                                    <td class="{% if product.quantity == 0 %}text-danger fw-bold{% endif %}">
                                        {{ product.quantity }}
                                    </td>
                                    <td>{{ product.min_quantity }}</td>
                                    <td>
                                        <a href="{% url 'inventory:edit_product' product.id %}" class="btn btn-sm btn-primary rounded-circle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-check-circle me-2"></i>{% trans "لا توجد منتجات منخفضة المخزون" %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Third Row -->
    <div class="row">
        <!-- Top Selling Products -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "المنتجات الأكثر مبيعًا" %}
                    </h6>
                </div>
                <div class="card-body">
                    {% if top_products %}
                    <div class="chart-container" style="position: relative; height:250px;">
                        <canvas id="topProductsChart"></canvas>
                    </div>
                    {% else %}
                    <p class="text-center">{% trans "لا توجد بيانات كافية لعرض المنتجات الأكثر مبيعًا" %}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Product Movements -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-exchange-alt me-2"></i>{% trans "حركات المنتجات الأخيرة" %}</h6>
                    <a href="{% url 'inventory:index' %}" class="btn btn-sm btn-info">
                        <i class="fas fa-external-link-alt me-1"></i>{% trans "عرض الكل" %}
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-hover mb-0" id="recentMovementsTable">
                            <thead>
                                <tr>
                                    <th class="border-0">{% trans "المنتج" %}</th>
                                    <th class="border-0">{% trans "النوع" %}</th>
                                    <th class="border-0">{% trans "الكمية" %}</th>
                                    <th class="border-0">{% trans "التاريخ" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in recent_movements %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-sm me-2 bg-secondary bg-opacity-10 rounded-circle text-center" style="width: 32px; height: 32px; line-height: 32px;">
                                                <i class="fas fa-box text-secondary"></i>
                                            </div>
                                            <span>{{ movement.product.name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        {% if movement.movement_type == 'in' %}
                                        <span class="badge bg-success">{% trans "وارد" %}</span>
                                        {% elif movement.movement_type == 'out' %}
                                        <span class="badge bg-danger">{% trans "صادر" %}</span>
                                        {% else %}
                                        <span class="badge bg-info">{% trans "تعديل" %}</span>
                                        {% endif %}
                                    </td>
                                    <td class="fw-bold">{{ movement.quantity }}</td>
                                    <td>{{ movement.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-info-circle me-2"></i>{% trans "لا توجد حركات مخزون حديثة" %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Top Products Chart
        {% if top_products %}
        var ctx = document.getElementById('topProductsChart').getContext('2d');
        var topProductsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [
                    {% for product in top_products %}
                    "{{ product.product__name }}",
                    {% endfor %}
                ],
                datasets: [{
                    label: '{% trans "الكمية المباعة" %}',
                    data: [
                        {% for product in top_products %}
                        {{ product.total_quantity }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(78, 115, 223, 0.7)',
                        'rgba(28, 200, 138, 0.7)',
                        'rgba(54, 185, 204, 0.7)',
                        'rgba(246, 194, 62, 0.7)',
                        'rgba(231, 74, 59, 0.7)'
                    ],
                    borderColor: [
                        'rgba(78, 115, 223, 1)',
                        'rgba(28, 200, 138, 1)',
                        'rgba(54, 185, 204, 1)',
                        'rgba(246, 194, 62, 1)',
                        'rgba(231, 74, 59, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        {% endif %}

        // تهيئة جداول DataTables
        // جدول أحدث المبيعات
        $('#recentSalesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20, -1], [5, 10, 15, 20, "الكل"]],
            "dom": 'lfrtip',
            "ordering": false,
            "searching": false,
            "paging": true
        });

        // جدول منتجات منخفضة المخزون
        $('#lowStockTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20, -1], [5, 10, 15, 20, "الكل"]],
            "dom": 'lfrtip',
            "ordering": false,
            "searching": false,
            "paging": true
        });

        // جدول حركات المخزون الأخيرة
        $('#recentMovementsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل"
            },
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20, -1], [5, 10, 15, 20, "الكل"]],
            "dom": 'lfrtip',
            "ordering": false,
            "searching": false,
            "paging": true
        });
    });

    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportToPDF() {
        window.print();
    }

    function exportToExcel() {
        // تصدير البيانات إلى Excel
        const table = document.querySelector('#recentOrdersTable');
        if (table) {
            const wb = XLSX.utils.table_to_book(table);
            XLSX.writeFile(wb, 'dashboard_data.xlsx');
        }
    }

    function exportToCSV() {
        // تصدير البيانات إلى CSV
        const table = document.querySelector('#recentOrdersTable');
        if (table) {
            const csv = [];
            const rows = table.querySelectorAll('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');

                for (let j = 0; j < cols.length; j++) {
                    row.push(cols[j].innerText);
                }

                csv.push(row.join(','));
            }

            const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = 'dashboard_data.csv';
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'dashboard:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    function initializeNavbarDropdowns() {
        // Re-initialize all Bootstrap dropdowns
        const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
        const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });

        // Ensure dropdowns work properly
        $('.dropdown-toggle').off('click.bs.dropdown').on('click', function(e) {
            e.preventDefault();
            const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
            dropdown.toggle();
        });

        // Fix navbar dropdown items layout
        fixNavbarDropdownLayout();

        // Fix admin dropdown position
        fixAdminDropdownPosition();
    }

    // إصلاح تخطيط عناصر القائمة المنسدلة
    function fixNavbarDropdownLayout() {
        $('.navbar .dropdown-item').each(function() {
            const $item = $(this);
            const $icon = $item.find('i');
            const text = $item.text().trim();

            if ($icon.length > 0 && text) {
                // إعادة ترتيب المحتوى - الأيقونة أولاً ثم النص
                $item.html(`
                    <i class="${$icon.attr('class')}"></i>
                    <span class="dropdown-text">${text}</span>
                `);
            }
        });
    }

    // إصلاح موضع قائمة admin لمنع التمرير الأفقي
    function fixAdminDropdownPosition() {
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) {
            const dropdownMenu = userDropdown.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                // إضافة class خاص لقائمة المستخدم
                dropdownMenu.classList.add('dropdown-menu-end');

                // ضبط الموضع
                dropdownMenu.style.right = '0';
                dropdownMenu.style.left = 'auto';
                dropdownMenu.style.transform = 'none';
                dropdownMenu.style.minWidth = '160px';
                dropdownMenu.style.maxWidth = '180px';
            }
        }

        // منع التمرير الأفقي في الصفحة
        document.body.style.overflowX = 'hidden';
    }

    // تهيئة القوائم المنسدلة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        initializeNavbarDropdowns();
    });
</script>
{% endblock %}
