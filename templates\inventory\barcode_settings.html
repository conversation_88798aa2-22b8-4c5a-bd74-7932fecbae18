{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.css" rel="stylesheet">
<script src="{% static 'js/dropdown_fix.js' %}"></script>
{% endblock %}
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes - مطابق لصفحة لوحة التحكم */
    body .navbar .dropdown-menu {
        max-height: none !important;
        overflow: visible !important;
        border: 1px solid rgba(0,0,0,.15) !important;
        border-radius: 0.375rem !important;
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15) !important;
        background-color: white !important;
        min-width: 10rem !important;
        margin-top: 0.5rem !important;
    }

    body .navbar .dropdown-item {
        padding: 0.5rem 1rem !important;
        white-space: nowrap !important;
        color: #374151 !important;
        background-color: transparent !important;
        font-weight: normal !important;
    }

    body .navbar .dropdown-item:hover,
    body .navbar .dropdown-item:focus {
        background-color: #f8f9fa !important;
        color: #0d6efd !important;
        text-decoration: none !important;
    }

    body .navbar .dropdown-item i {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    .settings-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    .settings-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 15px 20px;
    }
    .settings-card .card-body {
        padding: 20px;
    }
    .form-group label {
        font-weight: 600;
    }
    .help-text {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 5px;
    }
    .preview-container {
        border: 1px dashed #ced4da;
        padding: 15px;
        text-align: center;
        margin-top: 10px;
        border-radius: 5px;
    }
    .barcode-preview {
        max-width: 100%;
        height: auto;
    }
</style>
{% endblock %}

<script>
    // إصلاح شامل للنوافذ المنسدلة
    (function() {
        // تأكد من تحميل Bootstrap بالكامل
        if (typeof bootstrap !== 'undefined') {
            // انتظر حتى يتم تحميل جميع العناصر
            setTimeout(function() {
                // إعادة تهيئة جميع النوافذ المنسدلة
                const dropdowns = document.querySelectorAll('.dropdown-toggle');
                dropdowns.forEach(function(dropdown) {
                    // تأكد من وجود data-bs-toggle
                    dropdown.setAttribute('data-bs-toggle', 'dropdown');
                    
                    // أعد تهيئة النافذة المنسدلة
                    const instance = bootstrap.Dropdown.getInstance(dropdown);
                    if (instance) {
                        instance.dispose();
                    }
                    new bootstrap.Dropdown(dropdown);
                });
                
                // أضف معالج النقر المخصص
                dropdowns.forEach(function(dropdown) {
                    dropdown.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // أولاً، أغلق جميع القوائم المنسدلة المفتوحة الأخرى
                        document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                            if (menu.parentElement.querySelector('.dropdown-toggle') !== dropdown) {
                                menu.classList.remove('show');
                            }
                        });
                        
                        // ثم افتح أو أغلق القائمة المنسدلة الحالية
                        const instance = bootstrap.Dropdown.getInstance(dropdown) || new bootstrap.Dropdown(dropdown);
                        instance.toggle();
                    });
                });
                
                // تأكد من إغلاق القوائم المنسدلة عند النقر خارجها
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.dropdown')) {
                        document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                            menu.classList.remove('show');
                        });
                    }
                });
            }, 1000);
        }
    })();
    
    // تأكد من تحميل جميع المكتبات اللازمة قبل تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // إصلاح مشكلة النوافذ المنسدلة في جميع الصفحات
        setTimeout(function() {
            // تطبيق الأنماط المخصصة مباشرة على النوافذ المنسدلة
            const style = document.createElement('style');
            style.innerHTML = `
                .navbar .dropdown-menu {
                    max-height: none !important;
                    overflow: visible !important;
                    border: 1px solid rgba(0,0,0,.15) !important;
                    border-radius: 0.375rem !important;
                    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15) !important;
                    background-color: white !important;
                    min-width: 10rem !important;
                    margin-top: 0.5rem !important;
                    position: absolute !important;
                    top: 100% !important;
                    right: 0 !important;
                    float: left !important;
                    z-index: 1000 !important;
                    display: none !important;
                    min-width: 160px !important;
                    font-size: 0.875rem !important;
                    text-align: right !important;
                    list-style: none !important;
                    background-clip: padding-box !important;
                    border: 1px solid rgba(0,0,0,.15) !important;
                    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15) !important;
                }
                
                .navbar .dropdown-menu.show {
                    display: block !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    transform: translateY(0) !important;
                }
                
                .navbar .dropdown-item {
                    padding: 0.5rem 1rem !important;
                    white-space: nowrap !important;
                    color: #374151 !important;
                    background-color: transparent !important;
                    font-weight: normal !important;
                }
                
                .navbar .dropdown-item:hover,
                .navbar .dropdown-item:focus {
                    background-color: #f8f9fa !important;
                    color: #0d6efd !important;
                    text-decoration: none !important;
                }
                
                .navbar .dropdown-item i {
                    margin-left: 0 !important;
                    margin-right: 0.5rem !important;
                }
            `;
            document.head.appendChild(style);
            
            // تهيئة جميع القوائم المنسدلة
            const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
            
            // إصلاح مشاكل النقر على القوائم المنسدلة
            document.querySelectorAll('.dropdown-toggle').forEach(function(element) {
                element.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
                    dropdown.toggle();
                });
            });

            // إصلاح تخطيط عناصر القائمة المنسدلة
            document.querySelectorAll('.navbar .dropdown-item').forEach(function(item) {
                const icon = item.querySelector('i');
                const text = item.textContent.trim();

                if (icon && text) {
                    item.innerHTML = `
                        <i class="${icon.getAttribute('class')}"></i>
                        <span class="dropdown-text">${text}</span>
                    `;
                }
            });
            document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                menu.style.zIndex = '1050';
                menu.style.display = 'block';
                menu.style.opacity = '1';
                menu.style.visibility = 'visible';
                menu.style.transform = 'translateY(0)';
            });

            // منع التمرير الأفقي في الصفحة
            document.body.style.overflowX = 'hidden';

            // تأكد من تحميل وظائف navbar-fix
            if (typeof fixNavbarDropdownLayout === 'function') {
                fixNavbarDropdownLayout();
            }

            if (typeof fixAdminDropdownPosition === 'function') {
                fixAdminDropdownPosition();
            }
            
            // إضافة معالج إضافي للنوافذ المنسدلة
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                        menu.classList.remove('show');
                    });
                }
            });
        }, 1000);
        
        // تأكد من عمل النوافذ المنسدلة بشكل صحيح بعد تحميل الصفحة بالكامل
        window.addEventListener('load', function() {
            setTimeout(function() {
                document.querySelectorAll('.dropdown-toggle').forEach(function(element) {
                    const dropdown = bootstrap.Dropdown.getInstance(element);
                    if (dropdown) {
                        // إعادة تهيئة النافذة المنسدلة
                        dropdown.dispose();
                        new bootstrap.Dropdown(element);
                    }
                });
            }, 500);
        }, 1000);
    });
</script>

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-sliders-h me-3"></i>
                        {% trans "إعدادات الباركود" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "تخصيص وإعداد جميع خيارات الباركود والملصقات والطباعة حسب احتياجاتك" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <button type="button" class="btn btn-success btn-sm" onclick="saveSettings()">
                                <i class="fas fa-save me-1"></i>
                                {% trans "حفظ الإعدادات" %}
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "أدوات" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'inventory:barcode:barcode_types' %}">
                                        <i class="fas fa-tags text-warning me-2"></i>{% trans "إدارة أنواع الباركود" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:barcode:generate_barcode' %}">
                                        <i class="fas fa-plus text-success me-2"></i>{% trans "إنشاء باركود جديد" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="resetToDefaults()">
                                        <i class="fas fa-undo text-secondary me-2"></i>{% trans "إعادة تعيين للافتراضي" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportSettings()">
                                        <i class="fas fa-file-export text-primary me-2"></i>{% trans "تصدير الإعدادات" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="importSettings()">
                                        <i class="fas fa-file-import text-info me-2"></i>{% trans "استيراد إعدادات" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:barcode:scan_barcode' %}">
                                        <i class="fas fa-camera text-primary me-2"></i>{% trans "مسح باركود" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'inventory:barcode:barcode_logs' %}">
                                        <i class="fas fa-history text-secondary me-2"></i>{% trans "سجلات الباركود" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'inventory:index' %}">
                                            <i class="fas fa-warehouse me-1"></i>{% trans "المخزون" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'inventory:barcode:barcode_dashboard' %}">
                                            <i class="fas fa-qrcode me-1"></i>{% trans "لوحة تحكم الباركود" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "الإعدادات" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid">

    <div class="row">
        <div class="col-lg-8">
            <form method="post" action="{% url 'inventory:barcode:barcode_settings' %}">
                {% csrf_token %}

                <!-- إعدادات الباركود الافتراضية -->
                <div class="card settings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "الإعدادات الافتراضية" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="default_barcode_type">{% trans "نوع الباركود الافتراضي" %}</label>
                                    <select class="form-control" id="default_barcode_type" name="default_barcode_type">
                                        <option value="">{% trans "اختر نوع الباركود" %}</option>
                                        {% for barcode_type in barcode_types %}
                                            <option value="{{ barcode_type.id }}" {% if settings.default_barcode_type_id == barcode_type.id %}selected{% endif %}>
                                                {{ barcode_type.name }} ({{ barcode_type.code }})
                                            </option>
                                        {% endfor %}
                                    </select>
                                    <div class="help-text">{% trans "نوع الباركود الذي سيتم استخدامه افتراضيًا عند إنشاء باركود جديد" %}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="d-block">{% trans "خيارات العرض الافتراضية" %}</label>
                                    <div class="custom-control custom-switch mb-2">
                                        <input type="checkbox" class="custom-control-input" id="default_include_price" name="default_include_price" {% if settings.default_include_price %}checked{% endif %}>
                                        <label class="custom-control-label" for="default_include_price">{% trans "تضمين السعر في الباركود" %}</label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="default_include_name" name="default_include_name" {% if settings.default_include_name %}checked{% endif %}>
                                        <label class="custom-control-label" for="default_include_name">{% trans "تضمين اسم المنتج في الباركود" %}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات أبعاد الملصق -->
                <div class="card settings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "أبعاد الملصق" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="label_width">{% trans "عرض الملصق (مم)" %}</label>
                                    <input type="number" class="form-control" id="label_width" name="label_width" value="{{ settings.label_width }}" min="10" max="200">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="label_height">{% trans "ارتفاع الملصق (مم)" %}</label>
                                    <input type="number" class="form-control" id="label_height" name="label_height" value="{{ settings.label_height }}" min="10" max="200">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="labels_per_row">{% trans "عدد الملصقات في الصف" %}</label>
                                    <input type="number" class="form-control" id="labels_per_row" name="labels_per_row" value="{{ settings.labels_per_row }}" min="1" max="10">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="labels_per_column">{% trans "عدد الملصقات في العمود" %}</label>
                                    <input type="number" class="form-control" id="labels_per_column" name="labels_per_column" value="{{ settings.labels_per_column }}" min="1" max="20">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الهوامش -->
                <div class="card settings-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">{% trans "الهوامش" %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="margin_top">{% trans "الهامش العلوي (مم)" %}</label>
                                    <input type="number" class="form-control" id="margin_top" name="margin_top" value="{{ settings.margin_top }}" min="0" max="50">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="margin_right">{% trans "الهامش الأيمن (مم)" %}</label>
                                    <input type="number" class="form-control" id="margin_right" name="margin_right" value="{{ settings.margin_right }}" min="0" max="50">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="margin_bottom">{% trans "الهامش السفلي (مم)" %}</label>
                                    <input type="number" class="form-control" id="margin_bottom" name="margin_bottom" value="{{ settings.margin_bottom }}" min="0" max="50">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="margin_left">{% trans "الهامش الأيسر (مم)" %}</label>
                                    <input type="number" class="form-control" id="margin_left" name="margin_left" value="{{ settings.margin_left }}" min="0" max="50">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-end mt-3 mb-5">
                    <button type="submit" class="btn btn-primary">{% trans "حفظ الإعدادات" %}</button>
                </div>
            </form>
        </div>

        <div class="col-lg-4">
            <!-- معاينة الباركود -->
            <div class="card settings-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "معاينة الباركود" %}</h5>
                </div>
                <div class="card-body">
                    <div class="preview-container">
                        <div id="barcode-preview-container">
                            <p>{% trans "سيظهر هنا معاينة للباركود بناءً على الإعدادات المحددة" %}</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="button" id="generate-preview" class="btn btn-info btn-sm">{% trans "توليد معاينة" %}</button>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card settings-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "روابط سريعة" %}</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{% url 'inventory:barcode:barcode_types' %}" class="list-group-item list-group-item-action">
                            <i class="mdi mdi-tag-multiple me-1"></i> {% trans "إدارة أنواع الباركود" %}
                        </a>
                        <a href="{% url 'inventory:barcode:scan_barcode' %}" class="list-group-item list-group-item-action">
                            <i class="mdi mdi-barcode-scan me-1"></i> {% trans "مسح الباركود" %}
                        </a>
                        <a href="{% url 'inventory:barcode:barcode_logs' %}" class="list-group-item list-group-item-action">
                            <i class="mdi mdi-history me-1"></i> {% trans "سجلات الباركود" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function saveSettings() {
        // تشغيل حفظ النموذج
        const form = document.querySelector('form[method="post"]');
        if (form) {
            form.submit();
        } else {
            toastr.warning("{% trans 'لم يتم العثور على نموذج الإعدادات' %}");
        }
    }

    function resetToDefaults() {
        if (confirm("{% trans 'هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟' %}")) {
            // إعادة تعيين القيم الافتراضية
            document.getElementById('default_barcode_type').selectedIndex = 0;
            document.getElementById('default_include_price').checked = true;
            document.getElementById('default_include_name').checked = true;
            document.getElementById('label_width').value = 50;
            document.getElementById('label_height').value = 30;
            document.getElementById('labels_per_row').value = 3;
            document.getElementById('labels_per_column').value = 8;
            document.getElementById('margin_top').value = 10;
            document.getElementById('margin_right').value = 10;
            document.getElementById('margin_bottom').value = 10;
            document.getElementById('margin_left').value = 10;

            toastr.success("{% trans 'تم إعادة تعيين الإعدادات للقيم الافتراضية' %}");
        }
    }

    function exportSettings() {
        // تصدير الإعدادات الحالية
        const settings = {
            default_barcode_type: document.getElementById('default_barcode_type').value,
            default_include_price: document.getElementById('default_include_price').checked,
            default_include_name: document.getElementById('default_include_name').checked,
            label_width: document.getElementById('label_width').value,
            label_height: document.getElementById('label_height').value,
            labels_per_row: document.getElementById('labels_per_row').value,
            labels_per_column: document.getElementById('labels_per_column').value,
            margin_top: document.getElementById('margin_top').value,
            margin_right: document.getElementById('margin_right').value,
            margin_bottom: document.getElementById('margin_bottom').value,
            margin_left: document.getElementById('margin_left').value,
            export_date: new Date().toISOString()
        };

        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'barcode_settings_' + new Date().toISOString().split('T')[0] + '.json';
        link.click();
        URL.revokeObjectURL(url);

        toastr.success("{% trans 'تم تصدير الإعدادات بنجاح' %}");
    }

    function importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const settings = JSON.parse(e.target.result);

                        // تطبيق الإعدادات المستوردة
                        if (settings.default_barcode_type) document.getElementById('default_barcode_type').value = settings.default_barcode_type;
                        if (settings.default_include_price !== undefined) document.getElementById('default_include_price').checked = settings.default_include_price;
                        if (settings.default_include_name !== undefined) document.getElementById('default_include_name').checked = settings.default_include_name;
                        if (settings.label_width) document.getElementById('label_width').value = settings.label_width;
                        if (settings.label_height) document.getElementById('label_height').value = settings.label_height;
                        if (settings.labels_per_row) document.getElementById('labels_per_row').value = settings.labels_per_row;
                        if (settings.labels_per_column) document.getElementById('labels_per_column').value = settings.labels_per_column;
                        if (settings.margin_top) document.getElementById('margin_top').value = settings.margin_top;
                        if (settings.margin_right) document.getElementById('margin_right').value = settings.margin_right;
                        if (settings.margin_bottom) document.getElementById('margin_bottom').value = settings.margin_bottom;
                        if (settings.margin_left) document.getElementById('margin_left').value = settings.margin_left;

                        toastr.success("{% trans 'تم استيراد الإعدادات بنجاح' %}");
                    } catch (error) {
                        toastr.error("{% trans 'خطأ في قراءة ملف الإعدادات' %}");
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'inventory:barcode:barcode_dashboard' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    $(document).ready(function() {
        // توليد معاينة الباركود
        $("#generate-preview").click(function() {
            const barcodeType = $("#default_barcode_type").val();
            if (!barcodeType) {
                toastr.warning("{% trans 'يرجى اختيار نوع الباركود أولاً' %}");
                return;
            }

            // إظهار مؤشر التحميل
            $("#barcode-preview-container").html('<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');

            // إرسال طلب لتوليد باركود للمعاينة
            // سنستخدم نوع الباركود المحدد من القائمة المنسدلة كـ type
            // وسنستخدم بيانات افتراضية كـ data
            const selectedBarcodeTypeOption = $("#default_barcode_type option:selected");
            const barcodeTypeCode = selectedBarcodeTypeOption.text().split('(')[1].split(')')[0].trim(); // استخلاص رمز النوع مثل Code128
            const barcodeData = '12345PREVIEW'; // بيانات افتراضية للمعاينة

            $.ajax({
                url: `{% url 'inventory:barcode:generate_barcode' %}?type=${barcodeTypeCode}&data=${barcodeData}`,
                type: "GET", // تغيير نوع الطلب إلى GET
                // لا نحتاج إلى إرسال بيانات إضافية مع GET
                success: function(response, status, xhr) {
                    // التحقق من أن الاستجابة هي صورة
                    if (xhr.getResponseHeader('content-type').indexOf('image') !== -1) {
                        // تحويل البيانات الثنائية للصورة إلى base64
                        const reader = new FileReader();
                        reader.onloadend = function() {
                            const base64data = reader.result;
                            const previewHtml = `
                                <div class="text-center">
                                    <img src="${base64data}" alt="Barcode Preview" class="barcode-preview">
                                    <div class="mt-2">
                                        <strong>${barcodeData} (${barcodeTypeCode})</strong>
                                    </div>
                                    <div class="mt-1 small">
                                        {% trans "معاينة فقط - قد يختلف الشكل النهائي" %}
                                    </div>
                                </div>
                            `;
                            $("#barcode-preview-container").html(previewHtml);
                        }
                        reader.readAsDataURL(new Blob([response])); // قراءة الاستجابة كـ Blob
                    } else {
                        // إذا لم تكن صورة، قد يكون هناك خطأ نصي
                        toastr.error(response || "{% trans 'حدث خطأ غير متوقع أثناء تحميل المعاينة' %}");
                        $("#barcode-preview-container").html(`<p class="text-danger">${response || "{% trans 'حدث خطأ غير متوقع' %}"}</p>`);
                    }
                },
                error: function(xhr) {
                    let errorMsg = "{% trans 'حدث خطأ أثناء توليد المعاينة' %}";
                    if (xhr.responseText) {
                        errorMsg = xhr.responseText;
                    }
                    toastr.error(errorMsg);
                    $("#barcode-preview-container").html(`<p class="text-danger">${errorMsg}</p>`);
                },
                // ضروري لطلبات GET التي تتوقع بيانات ثنائية (مثل الصور)
                xhrFields: {
                    responseType: 'blob'
                }
            });
        });

        // تحديث معاينة الأبعاد عند تغيير القيم
        $("#label_width, #label_height, #labels_per_row, #labels_per_column").change(function() {
            updateDimensionsPreview();
        });

        function updateDimensionsPreview() {
            const width = $("#label_width").val();
            const height = $("#label_height").val();
            const perRow = $("#labels_per_row").val();
            const perColumn = $("#labels_per_column").val();

            // يمكن إضافة رسم توضيحي هنا لعرض الأبعاد
        }

        // تهيئة القوائم المنسدلة في الشريط العلوي
        initializeNavbarDropdowns();
    });
</script>

<script>
    // تهيئة القوائم المنسدلة في الشريط العلوي
    function initializeNavbarDropdowns() {
        // Re-initialize all Bootstrap dropdowns
        const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
        const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });

        // Ensure dropdowns work properly
        $('.dropdown-toggle').off('click.bs.dropdown').on('click', function(e) {
            e.preventDefault();
            const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
            dropdown.toggle();
        });

        // Fix navbar dropdown items layout
        fixNavbarDropdownLayout();

        // Fix admin dropdown position
        fixAdminDropdownPosition();
    }

    // إصلاح تخطيط عناصر القائمة المنسدلة
    function fixNavbarDropdownLayout() {
        $('.navbar .dropdown-item').each(function() {
            const $item = $(this);
            const $icon = $item.find('i');
            const text = $item.text().trim();

            if ($icon.length > 0 && text) {
                // إعادة ترتيب المحتوى - الأيقونة أولاً ثم النص
                $item.html(`
                    <i class="${$icon.attr('class')}"></i>
                    <span class="dropdown-text">${text}</span>
                `);
            }
        });
    }

    // إصلاح موضع قائمة admin لمنع التمرير الأفقي
    function fixAdminDropdownPosition() {
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) {
            const dropdownMenu = userDropdown.nextElementSibling;
            if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                // إضافة class خاص لقائمة المستخدم
                dropdownMenu.classList.add('dropdown-menu-end');

                // ضبط الموضع
                dropdownMenu.style.right = '0';
                dropdownMenu.style.left = 'auto';
                dropdownMenu.style.transform = 'none';
                dropdownMenu.style.minWidth = '160px';
                dropdownMenu.style.maxWidth = '180px';
            }
        }

        // منع التمرير الأفقي في الصفحة
        document.body.style.overflowX = 'hidden';
    }

    // كود إضافي لضمان عمل القوائم المنسدلة بشكل صحيح
    $(document).ready(function() {
        // تأكد من تحميل جميع المكتبات اللازمة
        setTimeout(function() {
            // إعادة تهيئة جميع القوائم المنسدلة
            const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
            dropdownElementList.forEach(function(dropdownToggleEl) {
                const dropdown = bootstrap.Dropdown.getInstance(dropdownToggleEl);
                if (!dropdown) {
                    new bootstrap.Dropdown(dropdownToggleEl);
                }
            });

            // إصلاح مشاكل النقر على القوائم المنسدلة
            $('.dropdown-toggle').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
                dropdown.toggle();
            });

            // إصلاح تخطيط عناصر القائمة المنسدلة
            $('.navbar .dropdown-item').each(function() {
                const $item = $(this);
                const $icon = $item.find('i');
                const text = $item.text().trim();

                if ($icon.length > 0 && text) {
                    $item.html(`
                        <i class="${$icon.attr('class')}"></i>
                        <span class="dropdown-text">${text}</span>
                    `);
                }
            });

            // تأكد من ظهور القوائم المنسدلة بشكل صحيح
            $('.dropdown-menu').css({
                'z-index': '1050',
                'display': 'block',
                'opacity': '1',
                'visibility': 'visible',
                'transform': 'translateY(0)'
            });

            // إصلاح مشكلة القوائم المنسدلة في صفحة إعدادات الباركود
            fixNavbarDropdownLayout();
            fixAdminDropdownPosition();
        }, 1000);
    });

    // إضافة كود إضافي في نهاية الصفحة لضمان عمل القوائم المنسدلة
    $(window).on('load', function() {
        setTimeout(function() {
            // إعادة تهيئة جميع القوائم المنسدلة
            const dropdowns = document.querySelectorAll('.dropdown-toggle');
            dropdowns.forEach(function(dropdown) {
                const instance = bootstrap.Dropdown.getInstance(dropdown);
                if (instance) {
                    instance.dispose();
                    new bootstrap.Dropdown(dropdown);
                }
            });

            // إصلاح مشاكل النقر على القوائم المنسدلة
            document.querySelectorAll('.dropdown-toggle').forEach(function(element) {
                element.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this);
                    dropdown.toggle();
                });
            });

            // تأكد من ظهور القوائم المنسدلة بشكل صحيح
            document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
                menu.style.zIndex = '1050';
                menu.style.display = 'block';
                menu.style.opacity = '1';
                menu.style.visibility = 'visible';
                menu.style.transform = 'translateY(0)';
            });
        }, 1500);
    });
</script>
{% endblock %}