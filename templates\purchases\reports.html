{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "تقارير المشتريات" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    .report-card {
        transition: all 0.3s;
        height: 100%;
    }
    
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .report-icon {
        font-size: 3rem;
        opacity: 0.7;
        margin-bottom: 1rem;
    }
    
    /* تطبيق خط Cairo على الصفحة */
    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    /* تصحيح حجم الخط في شريط التنقل */
    .nav-link {
        font-size: 1.1rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
    
    .navbar-brand {
        font-size: 1.2rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">{% trans "تقارير المشتريات" %}</h1>
    <div>
        <a href="{% url 'purchases:index' %}" class="btn btn-primary">
            <i class="fas fa-arrow-right me-1"></i> {% trans "العودة إلى لوحة التحكم" %}
        </a>
    </div>
</div>

{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}
{% endif %}

<!-- Reports Cards -->
<div class="row">
    {% for report in report_types %}
    <div class="col-md-4 mb-4">
        <div class="card report-card text-center">
            <div class="card-body">
                {% if report.id == 'suppliers' %}
                <i class="fas fa-users report-icon text-primary"></i>
                {% elif report.id == 'purchases' %}
                <i class="fas fa-shopping-cart report-icon text-success"></i>
                {% elif report.id == 'payments' %}
                <i class="fas fa-money-bill-wave report-icon text-warning"></i>
                {% endif %}
                
                <h5 class="card-title">{{ report.name }}</h5>
                <p class="card-text text-muted">
                    {% if report.id == 'suppliers' %}
                    {% trans "عرض تقرير مفصل عن الموردين وإجمالي المشتريات لكل مورد." %}
                    {% elif report.id == 'purchases' %}
                    {% trans "عرض تقرير مفصل عن طلبات الشراء وحالتها وإجمالي المبالغ." %}
                    {% elif report.id == 'payments' %}
                    {% trans "عرض تقرير مفصل عن المدفوعات وطرق الدفع والمبالغ." %}
                    {% endif %}
                </p>
                <a href="{% url 'purchases:'|add:report.id|add:'_report' %}" class="btn btn-primary mt-3">
                    <i class="fas fa-chart-bar me-1"></i> {% trans "عرض التقرير" %}
                </a>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100">
                    <a href="{% url 'purchases:export_report' report_type=report.id %}" class="btn btn-sm btn-success">
                        <i class="fas fa-file-csv me-1"></i> {% trans "تصدير CSV" %}
                    </a>
                    <a href="#" class="btn btn-sm btn-info print-report" data-report="{{ report.id }}">
                        <i class="fas fa-print me-1"></i> {% trans "طباعة" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Report Description -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{% trans "معلومات عن التقارير" %}</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">{% trans "تقرير الموردين" %}</h5>
                    </div>
                    <div class="card-body">
                        <p>{% trans "يوفر هذا التقرير معلومات مفصلة عن:" %}</p>
                        <ul>
                            <li>{% trans "قائمة الموردين وبياناتهم" %}</li>
                            <li>{% trans "إجمالي المشتريات لكل مورد" %}</li>
                            <li>{% trans "عدد طلبات الشراء لكل مورد" %}</li>
                            <li>{% trans "المبالغ المدفوعة وغير المدفوعة" %}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">{% trans "تقرير المشتريات" %}</h5>
                    </div>
                    <div class="card-body">
                        <p>{% trans "يوفر هذا التقرير معلومات مفصلة عن:" %}</p>
                        <ul>
                            <li>{% trans "قائمة طلبات الشراء وتفاصيلها" %}</li>
                            <li>{% trans "حالة كل طلب (معلق، تم الاستلام، ملغي)" %}</li>
                            <li>{% trans "حالة الدفع لكل طلب" %}</li>
                            <li>{% trans "إجمالي المبالغ والإحصائيات" %}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">{% trans "تقرير المدفوعات" %}</h5>
                    </div>
                    <div class="card-body">
                        <p>{% trans "يوفر هذا التقرير معلومات مفصلة عن:" %}</p>
                        <ul>
                            <li>{% trans "قائمة المدفوعات وتفاصيلها" %}</li>
                            <li>{% trans "طرق الدفع المستخدمة" %}</li>
                            <li>{% trans "تواريخ الدفع" %}</li>
                            <li>{% trans "إجمالي المبالغ المدفوعة" %}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Print report
        $('.print-report').click(function(e) {
            e.preventDefault();
            var reportType = $(this).data('report');
            var url = "{% url 'purchases:index' %}".replace('index', reportType + '_report');
            
            // Open report in new window and print
            var printWindow = window.open(url, '_blank');
            printWindow.addEventListener('load', function() {
                printWindow.print();
            });
        });
    });
</script>
{% endblock %}
