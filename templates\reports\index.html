{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "التقارير" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --info-color: #0891b2;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --dark-color: #1e293b;
        --light-color: #f8fafc;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
        --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        --gradient-info: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        direction: rtl;
        text-align: right;
        line-height: 1.6;
        color: var(--dark-color);
        min-height: 100vh;
    }

    .main-container {
        background: transparent;
        min-height: 100vh;
        padding: 0;
    }

    /* Header Section */
    .page-header {
        background: var(--gradient-primary);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: var(--shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 1;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .header-actions {
        position: relative;
        z-index: 1;
    }

    .header-actions .btn {
        border-radius: 10px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: none;
    }

    .header-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .breadcrumb-nav {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 0.75rem 1rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .breadcrumb-nav a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .breadcrumb-nav a:hover {
        color: white;
    }

    /* زر العودة للصفحة السابقة */
    .btn-back {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
        margin-right: 10px;
    }

    .btn-back:hover {
        background: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-back i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .btn-back:hover i {
        transform: translateX(-2px);
    }

    /* تحسين breadcrumb للعمل مع زر العودة */
    .breadcrumb-nav nav {
        flex-grow: 1;
    }

    .breadcrumb {
        margin-bottom: 0;
        background: transparent;
        padding: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: rgba(255,255,255,0.6);
        margin: 0 0.5rem;
    }

    /* Dropdown Fixes */
    .dropdown-menu {
        z-index: 1050 !important;
        border: none;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        padding: 0.5rem 0;
        margin-top: 0.5rem;
        background: white;
        min-width: 200px;
    }

    .dropdown-item {
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        background: none;
        color: #374151;
        font-weight: 500;
        text-decoration: none;
        display: block;
        width: 100%;
        clear: both;
        white-space: nowrap;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
    }

    .dropdown-item i {
        width: 20px;
        text-align: center;
        margin-left: 0.5rem;
    }

    /* Force dropdown visibility */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .breadcrumb-nav {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .header-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .page-header {
            padding: 1.5rem 0;
        }
    }
    /* Enhanced Dashboard Cards */
    .stats-card {
        background: linear-gradient(135deg, #fff 0%, #f8f9fc 100%);
        border-radius: 20px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-light) 100%);
    }

    .stats-card-primary {
        --card-color: #4e73df;
        --card-color-light: #6f8ef7;
    }

    .stats-card-success {
        --card-color: #1cc88a;
        --card-color-light: #36d9a3;
    }

    .stats-card-info {
        --card-color: #36b9cc;
        --card-color-light: #6ecdd7;
    }

    .stats-card-warning {
        --card-color: #f6c23e;
        --card-color-light: #f8d866;
    }

    .stats-card-body {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: linear-gradient(135deg, var(--card-color) 0%, var(--card-color-light) 100%);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .stats-content {
        flex: 1;
        margin-right: 1rem;
    }

    .stats-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stats-subtitle {
        font-size: 0.75rem;
        color: #95a5a6;
        font-weight: 500;
    }

    .section-title {
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(90deg, #4e73df, #6f8ef7);
        border-radius: 2px;
    }

    .page-header {
        background: linear-gradient(135deg, #4e73df 0%, #6f8ef7 100%);
        color: white;
        padding: 30px 0;
        border-radius: 0 0 15px 15px;
        margin-bottom: 30px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4e73df 0%, #6f8ef7 100%);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #3a5bc7 0%, #5a7fe6 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .report-card {
        height: 100%;
        transition: all 0.3s ease;
    }

    .report-card .card-body {
        padding: 1.5rem;
    }

    .report-card .btn {
        border-radius: 25px;
        font-weight: 500;
        padding: 0.5rem 1.5rem;
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">
                        <i class="fas fa-chart-bar me-3"></i>
                        {% trans "إدارة التقارير" %}
                    </h1>
                    <p class="page-subtitle">
                        {% trans "مراقبة أداء عملك وتحليل البيانات المهمة مع تقارير شاملة ومتقدمة" %}
                    </p>
                </div>
                <div class="col-lg-4">
                    <div class="header-actions text-end">
                        <div class="d-flex gap-2 mb-3 justify-content-end">
                            <button type="button" class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>
                                {% trans "تحديث" %}
                            </button>
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#saveReportModal">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "تقرير جديد" %}
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-info btn-sm dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>
                                    {% trans "إدارة" %}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'reports:sales_report' %}">
                                        <i class="fas fa-shopping-cart text-primary me-2"></i>{% trans "تقرير المبيعات" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:inventory_report' %}">
                                        <i class="fas fa-boxes text-warning me-2"></i>{% trans "تقرير المخزون" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:customers_report' %}">
                                        <i class="fas fa-users text-info me-2"></i>{% trans "تقرير العملاء" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:financial_report' %}">
                                        <i class="fas fa-money-bill-wave text-success me-2"></i>{% trans "التقرير المالي" %}
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportAllReports()">
                                        <i class="fas fa-file-excel text-success me-2"></i>{% trans "تصدير جميع التقارير" %}
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#scheduleReportModal">
                                        <i class="fas fa-calendar-plus text-primary me-2"></i>{% trans "جدولة تقرير" %}
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="breadcrumb-nav d-flex align-items-center justify-content-between">
                            <nav aria-label="breadcrumb" class="flex-grow-1">
                                <ol class="breadcrumb mb-0">
                                    <li class="breadcrumb-item">
                                        <a href="{% url 'dashboard:index' %}">
                                            <i class="fas fa-home me-1"></i>{% trans "الرئيسية" %}
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white">{% trans "التقارير" %}</li>
                                </ol>
                            </nav>
                            <!-- زر العودة للصفحة السابقة -->
                            <button type="button" class="btn-back" onclick="goBack()" title="العودة للصفحة السابقة">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid px-4">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Main Reports Section -->
    <div class="row mb-5">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-primary h-100 report-card">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "تقرير المبيعات" %}</div>
                        <div class="stats-number">{% trans "تحليل المبيعات" %}</div>
                        <div class="stats-subtitle">{% trans "تتبع أداء المبيعات" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:sales_report' %}" class="btn btn-primary btn-block">
                        <i class="fas fa-chart-line me-2"></i>{% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-success h-100 report-card">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "تقرير المخزون" %}</div>
                        <div class="stats-number">{% trans "حالة المخزون" %}</div>
                        <div class="stats-subtitle">{% trans "إدارة المخزون الفعالة" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:inventory_report' %}" class="btn btn-success btn-block">
                        <i class="fas fa-boxes me-2"></i>{% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-info h-100 report-card">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "تقرير العملاء" %}</div>
                        <div class="stats-number">{% trans "تحليل العملاء" %}</div>
                        <div class="stats-subtitle">{% trans "فهم سلوك العملاء" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:customers_report' %}" class="btn btn-info btn-block">
                        <i class="fas fa-users me-2"></i>{% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-warning h-100 report-card">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "التقرير المالي" %}</div>
                        <div class="stats-number">{% trans "الأرباح والمصروفات" %}</div>
                        <div class="stats-subtitle">{% trans "التحليل المالي" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:financial_report' %}" class="btn btn-warning btn-block">
                        <i class="fas fa-money-bill-wave me-2"></i>{% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Saved Reports Section -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <div class="stats-card">
                <h3 class="section-title">
                    <i class="fas fa-file-alt me-2 text-primary"></i>{% trans "التقارير المحفوظة" %}
                </h3>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="text-muted small">{% trans "جميع التقارير التي قمت بحفظها" %}</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#saveReportModal">
                        <i class="fas fa-plus-circle me-1"></i> {% trans "حفظ تقرير جديد" %}
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover" id="savedReportsTable">
                        <thead>
                            <tr>
                                <th>{% trans "اسم التقرير" %}</th>
                                <th>{% trans "النوع" %}</th>
                                <th>{% trans "تاريخ الإنشاء" %}</th>
                                <th>{% trans "عام" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in saved_reports %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        <span>{{ report.name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ report.get_report_type_display }}</span>
                                </td>
                                <td>{{ report.created_at|date:"Y-m-d" }}</td>
                                <td>
                                    {% if report.is_public %}
                                    <span class="badge bg-success">{% trans "نعم" %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% trans "لا" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-info view-report me-1" data-id="{{ report.id }}" title="{% trans 'عرض' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if report.created_by == request.user %}
                                    <a href="#" class="btn btn-sm btn-warning edit-report me-1" data-id="{{ report.id }}" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-danger delete-report" data-id="{{ report.id }}" data-name="{{ report.name }}" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-3">
                                    <i class="fas fa-inbox fa-3x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">{% trans "لا توجد تقارير محفوظة" %}</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="stats-card">
                <h3 class="section-title">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>{% trans "التقارير المجدولة" %}
                </h3>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="text-muted small">{% trans "التقارير التي يتم إرسالها تلقائيًا" %}</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleReportModal">
                        <i class="fas fa-calendar-plus me-1"></i> {% trans "جدولة تقرير" %}
                    </button>
                </div>
                <div class="list-group">
                    {% for schedule in scheduled_reports %}
                    <div class="list-group-item list-group-item-action border-0">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ schedule.saved_report.name }}</h5>
                            <small class="text-muted">{{ schedule.get_frequency_display }}</small>
                        </div>
                        <p class="mb-1 text-muted">{{ schedule.saved_report.get_report_type_display }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small>
                                <i class="fas fa-paper-plane me-1 text-primary"></i>
                                {% trans "آخر إرسال:" %} 
                                {% if schedule.last_sent %}
                                    <span class="text-success">{{ schedule.last_sent|date:"Y-m-d H:i" }}</span>
                                {% else %}
                                    <span class="text-muted">{% trans "لم يتم الإرسال بعد" %}</span>
                                {% endif %}
                            </small>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-info edit-schedule me-1" data-id="{{ schedule.id }}">
                                    <i class="fas fa-edit"></i> {% trans "تعديل" %}
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-schedule" data-id="{{ schedule.id }}" data-name="{{ schedule.saved_report.name }}">
                                    <i class="fas fa-trash"></i> {% trans "حذف" %}
                                </button>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted mb-0">{% trans "لا توجد تقارير مجدولة" %}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Report Modal -->
<div class="modal fade" id="saveReportModal" tabindex="-1" aria-labelledby="saveReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title" id="saveReportModalLabel">
                    <i class="fas fa-save me-2"></i>{% trans "حفظ تقرير جديد" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="saveReportForm" method="post" action="#">
                    {% csrf_token %}
                    <div class="mb-4">
                        <label for="report_name" class="form-label">
                            <i class="fas fa-tag me-2 text-primary"></i>{% trans "اسم التقرير" %}
                        </label>
                        <input type="text" class="form-control" id="report_name" name="report_name" required placeholder="{% trans 'أدخل اسمًا فريدًا للتقرير' %}">
                    </div>
                    <div class="mb-4">
                        <label for="report_type" class="form-label">
                            <i class="fas fa-chart-pie me-2 text-primary"></i>{% trans "نوع التقرير" %}
                        </label>
                        <select class="form-select" id="report_type" name="report_type" required>
                            <option value="sales">{% trans "تقرير المبيعات" %}</option>
                            <option value="inventory">{% trans "تقرير المخزون" %}</option>
                            <option value="customers">{% trans "تقرير العملاء" %}</option>
                            <option value="financial">{% trans "التقرير المالي" %}</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="report_description" class="form-label">
                            <i class="fas fa-align-left me-2 text-primary"></i>{% trans "الوصف" %}
                        </label>
                        <textarea class="form-control" id="report_description" name="report_description" rows="3" placeholder="{% trans 'أضف وصفًا مختصرًا للتقرير' %}"></textarea>
                    </div>
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="is_public" name="is_public">
                        <label class="form-check-label" for="is_public">
                            <i class="fas fa-globe me-2 text-primary"></i>{% trans "جعل التقرير عامًا (متاح لجميع المستخدمين)" %}
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>{% trans "إلغاء" %}
                </button>
                <button type="button" class="btn btn-primary" id="saveReportBtn">
                    <i class="fas fa-save me-2"></i>{% trans "حفظ التقرير" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Report Modal -->
<div class="modal fade" id="scheduleReportModal" tabindex="-1" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title" id="scheduleReportModalLabel">
                    <i class="fas fa-calendar-check me-2"></i>{% trans "جدولة تقرير" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleReportForm" method="post" action="#">
                    {% csrf_token %}
                    <div class="mb-4">
                        <label for="saved_report" class="form-label">
                            <i class="fas fa-file-alt me-2 text-primary"></i>{% trans "التقرير" %}
                        </label>
                        <select class="form-select" id="saved_report" name="saved_report" required>
                            {% for report in saved_reports %}
                            <option value="{{ report.id }}">{{ report.name }} ({{ report.get_report_type_display }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="frequency" class="form-label">
                            <i class="fas fa-sync me-2 text-primary"></i>{% trans "التكرار" %}
                        </label>
                        <select class="form-select" id="frequency" name="frequency" required>
                            <option value="daily">{% trans "يومي" %}</option>
                            <option value="weekly">{% trans "أسبوعي" %}</option>
                            <option value="monthly">{% trans "شهري" %}</option>
                            <option value="quarterly">{% trans "ربع سنوي" %}</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="recipients" class="form-label">
                            <i class="fas fa-envelope me-2 text-primary"></i>{% trans "المستلمون" %}
                        </label>
                        <input type="text" class="form-control" id="recipients" name="recipients" required placeholder="{% trans "بريد إلكتروني مفصول بفواصل" %}">
                    </div>
                    <div class="mb-4">
                        <label for="subject" class="form-label">
                            <i class="fas fa-heading me-2 text-primary"></i>{% trans "الموضوع" %}
                        </label>
                        <input type="text" class="form-control" id="subject" name="subject" required placeholder="{% trans "موضوع التقرير" %}">
                    </div>
                    <div class="mb-4">
                        <label for="message" class="form-label">
                            <i class="fas fa-comment-dots me-2 text-primary"></i>{% trans "الرسالة" %}
                        </label>
                        <textarea class="form-control" id="message" name="message" rows="3" placeholder="{% trans "أضف رسالة اختيارية" %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>{% trans "إلغاء" %}
                </button>
                <button type="button" class="btn btn-primary" id="scheduleReportBtn">
                    <i class="fas fa-calendar-check me-2"></i>{% trans "جدولة التقرير" %}
                </button>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // وظائف الشريط العلوي
    function refreshData() {
        location.reload();
    }

    function exportAllReports() {
        // تصدير جميع التقارير
        const reportTypes = ['sales', 'inventory', 'customers', 'financial'];
        const wb = XLSX.utils.book_new();

        reportTypes.forEach(type => {
            // إنشاء ورقة عمل لكل نوع تقرير
            const ws = XLSX.utils.aoa_to_sheet([
                [`تقرير ${type}`, 'البيانات', 'التاريخ'],
                ['مثال على البيانات', '100', new Date().toLocaleDateString('ar-SA')]
            ]);
            XLSX.utils.book_append_sheet(wb, ws, `تقرير_${type}`);
        });

        XLSX.writeFile(wb, 'all_reports.xlsx');
    }

    function goBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = "{% url 'dashboard:index' %}";
        }
    }

    // تهيئة القوائم المنسدلة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة القوائم المنسدلة في الشريط العلوي
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        const dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable with modern styling
        $('#savedReportsTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                "search": "بحث:",
                "paginate": {
                    "first": "الأول",
                    "last": "الأخير",
                    "next": "التالي",
                    "previous": "السابق"
                },
                "emptyTable": "لا توجد بيانات متاحة في الجدول",
                "zeroRecords": "لم يتم العثور على أي سجلات مطابقة للبحث"
            },
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip',
            "responsive": true,
            "order": [[2, 'desc']], // Sort by creation date by default
            "initComplete": function() {
                // Add custom styling to DataTable
                $(this).addClass('table-hover');
                $('.dataTables_wrapper').addClass('mt-3');
            }
        });

        // Save Report Button with animation
        const saveReportBtn = document.getElementById('saveReportBtn');
        if (saveReportBtn) {
            saveReportBtn.addEventListener('click', function() {
                const form = document.getElementById('saveReportForm');
                const submitBtn = this;

                // Add loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{% trans "جاري الحفظ" %}';

                // Submit form
                form.submit();

                // Reset button after a delay (in case form submission is slow)
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>{% trans "حفظ التقرير" %}';
                }, 2000);
            });
        }

        // Schedule Report Button with animation
        const scheduleReportBtn = document.getElementById('scheduleReportBtn');
        if (scheduleReportBtn) {
            scheduleReportBtn.addEventListener('click', function() {
                const form = document.getElementById('scheduleReportForm');
                const submitBtn = this;

                // Add loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{% trans "جاري الجدولة" %}';

                // Submit form
                form.submit();

                // Reset button after a delay
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-calendar-check me-2"></i>{% trans "جدولة التقرير" %>';
                }, 2000);
            });
        }

        // Add delete confirmation
        document.querySelectorAll('.delete-report').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const reportId = this.getAttribute('data-id');
                const reportName = this.getAttribute('data-name');

                if (confirm(`هل أنت متأكد من حذف تقرير "${reportName}"؟`)) {
                    // Implement delete functionality here
                    window.location.href = `/reports/delete/${reportId}/`;
                }
            });
        });

        // Add delete confirmation for scheduled reports
        document.querySelectorAll('.delete-schedule').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const scheduleId = this.getAttribute('data-id');
                const scheduleName = this.getAttribute('data-name');

                if (confirm(`هل أنت متأكد من إلغاء جدولة تقرير "${scheduleName}"؟`)) {
                    // Implement delete functionality here
                    window.location.href = `/reports/schedule/delete/${scheduleId}/`;
                }
            });
        });

        // Add fade-in animation to cards
        const cards = document.querySelectorAll('.report-card, .stats-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>

<!-- Custom CSS for animations and styling -->
<style>
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease forwards;
    }

    .gradient-primary {
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    }

    .btn-primary {
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a0db0 0%, #1d65e5 100%);
    }

    .modal-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .modal-footer {
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    .form-control:focus, .form-select:focus {
        border-color: #6a11cb;
        box-shadow: 0 0 0 0.2rem rgba(106, 17, 203, 0.25);
    }
</style>
{% endblock %}
