{% extends 'modern_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "التقارير" %} | {{ block.super }}{% endblock %}

{% block page_title %}{% trans "مركز التقارير المتقدم" %}{% endblock %}
{% block page_subtitle %}{% trans "تحليلات شاملة وتقارير تفاعلية لجميع جوانب العمل" %}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active text-white">{% trans "التقارير" %}</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Dashboard Cards */
    .stats-card {
        background: linear-gradient(135deg, #fff 0%, #f8f9fc 100%);
        border-radius: 20px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-light) 100%);
    }

    .stats-card-primary {
        --card-color: #4e73df;
        --card-color-light: #6f8ef7;
    }

    .stats-card-success {
        --card-color: #1cc88a;
        --card-color-light: #36d9a3;
    }

    .stats-card-info {
        --card-color: #36b9cc;
        --card-color-light: #6ecdd7;
    }

    .stats-card-warning {
        --card-color: #f6c23e;
        --card-color-light: #f8d866;
    }

    .stats-card-body {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: linear-gradient(135deg, var(--card-color) 0%, var(--card-color-light) 100%);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .stats-content {
        flex: 1;
        margin-right: 1rem;
    }

    .stats-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stats-subtitle {
        font-size: 0.75rem;
        color: #95a5a6;
        font-weight: 500;
    }

    .section-title {
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(90deg, #4e73df, #6f8ef7);
        border-radius: 2px;
    }

    .page-header {
        background: linear-gradient(135deg, #4e73df 0%, #6f8ef7 100%);
        color: white;
        padding: 30px 0;
        border-radius: 0 0 15px 15px;
        margin-bottom: 30px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4e73df 0%, #6f8ef7 100%);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #3a5bc7 0%, #5a7fe6 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .report-card {
        height: 100%;
        transition: all 0.3s ease;
    }

    .report-card .card-body {
        padding: 1.5rem;
    }

    .report-card .btn {
        border-radius: 25px;
        font-weight: 500;
        padding: 0.5rem 1.5rem;
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">{% trans "التقارير" %}</h1>
                <p class="mb-0 opacity-75">{% trans "مراقبة أداء عملك وتحليل البيانات المهمة" %}</p>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <i class="fas fa-chart-line fa-2x"></i>
                </span>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid px-4">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Main Reports Section -->
    <div class="row mb-5">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-primary h-100 report-card">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "تقرير المبيعات" %}</div>
                        <div class="stats-number">{% trans "تحليل المبيعات" %}</div>
                        <div class="stats-subtitle">{% trans "تتبع أداء المبيعات" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:sales_report' %}" class="btn btn-primary btn-block">
                        <i class="fas fa-chart-line me-2"></i>{% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-success h-100 report-card">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "تقرير المخزون" %}</div>
                        <div class="stats-number">{% trans "حالة المخزون" %}</div>
                        <div class="stats-subtitle">{% trans "إدارة المخزون الفعالة" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:inventory_report' %}" class="btn btn-success btn-block">
                        <i class="fas fa-boxes me-2"></i>{% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-info h-100 report-card">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "تقرير العملاء" %}</div>
                        <div class="stats-number">{% trans "تحليل العملاء" %}</div>
                        <div class="stats-subtitle">{% trans "فهم سلوك العملاء" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:customers_report' %}" class="btn btn-info btn-block">
                        <i class="fas fa-users me-2"></i>{% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-warning h-100 report-card">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-title">{% trans "التقرير المالي" %}</div>
                        <div class="stats-number">{% trans "الأرباح والمصروفات" %}</div>
                        <div class="stats-subtitle">{% trans "التحليل المالي" %}</div>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{% url 'reports:financial_report' %}" class="btn btn-warning btn-block">
                        <i class="fas fa-money-bill-wave me-2"></i>{% trans "عرض التقرير" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Saved Reports Section -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <div class="stats-card">
                <h3 class="section-title">
                    <i class="fas fa-file-alt me-2 text-primary"></i>{% trans "التقارير المحفوظة" %}
                </h3>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="text-muted small">{% trans "جميع التقارير التي قمت بحفظها" %}</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#saveReportModal">
                        <i class="fas fa-plus-circle me-1"></i> {% trans "حفظ تقرير جديد" %}
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover" id="savedReportsTable">
                        <thead>
                            <tr>
                                <th>{% trans "اسم التقرير" %}</th>
                                <th>{% trans "النوع" %}</th>
                                <th>{% trans "تاريخ الإنشاء" %}</th>
                                <th>{% trans "عام" %}</th>
                                <th>{% trans "الإجراءات" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in saved_reports %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        <span>{{ report.name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ report.get_report_type_display }}</span>
                                </td>
                                <td>{{ report.created_at|date:"Y-m-d" }}</td>
                                <td>
                                    {% if report.is_public %}
                                    <span class="badge bg-success">{% trans "نعم" %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% trans "لا" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-info view-report me-1" data-id="{{ report.id }}" title="{% trans 'عرض' %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if report.created_by == request.user %}
                                    <a href="#" class="btn btn-sm btn-warning edit-report me-1" data-id="{{ report.id }}" title="{% trans 'تعديل' %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-danger delete-report" data-id="{{ report.id }}" data-name="{{ report.name }}" title="{% trans 'حذف' %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-3">
                                    <i class="fas fa-inbox fa-3x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">{% trans "لا توجد تقارير محفوظة" %}</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="stats-card">
                <h3 class="section-title">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>{% trans "التقارير المجدولة" %}
                </h3>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="text-muted small">{% trans "التقارير التي يتم إرسالها تلقائيًا" %}</span>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleReportModal">
                        <i class="fas fa-calendar-plus me-1"></i> {% trans "جدولة تقرير" %}
                    </button>
                </div>
                <div class="list-group">
                    {% for schedule in scheduled_reports %}
                    <div class="list-group-item list-group-item-action border-0">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ schedule.saved_report.name }}</h5>
                            <small class="text-muted">{{ schedule.get_frequency_display }}</small>
                        </div>
                        <p class="mb-1 text-muted">{{ schedule.saved_report.get_report_type_display }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small>
                                <i class="fas fa-paper-plane me-1 text-primary"></i>
                                {% trans "آخر إرسال:" %} 
                                {% if schedule.last_sent %}
                                    <span class="text-success">{{ schedule.last_sent|date:"Y-m-d H:i" }}</span>
                                {% else %}
                                    <span class="text-muted">{% trans "لم يتم الإرسال بعد" %}</span>
                                {% endif %}
                            </small>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-info edit-schedule me-1" data-id="{{ schedule.id }}">
                                    <i class="fas fa-edit"></i> {% trans "تعديل" %}
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-schedule" data-id="{{ schedule.id }}" data-name="{{ schedule.saved_report.name }}">
                                    <i class="fas fa-trash"></i> {% trans "حذف" %}
                                </button>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted mb-0">{% trans "لا توجد تقارير مجدولة" %}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Report Modal -->
<div class="modal fade" id="saveReportModal" tabindex="-1" aria-labelledby="saveReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title" id="saveReportModalLabel">
                    <i class="fas fa-save me-2"></i>{% trans "حفظ تقرير جديد" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="saveReportForm" method="post" action="#">
                    {% csrf_token %}
                    <div class="mb-4">
                        <label for="report_name" class="form-label">
                            <i class="fas fa-tag me-2 text-primary"></i>{% trans "اسم التقرير" %}
                        </label>
                        <input type="text" class="form-control" id="report_name" name="report_name" required placeholder="{% trans 'أدخل اسمًا فريدًا للتقرير' %}">
                    </div>
                    <div class="mb-4">
                        <label for="report_type" class="form-label">
                            <i class="fas fa-chart-pie me-2 text-primary"></i>{% trans "نوع التقرير" %}
                        </label>
                        <select class="form-select" id="report_type" name="report_type" required>
                            <option value="sales">{% trans "تقرير المبيعات" %}</option>
                            <option value="inventory">{% trans "تقرير المخزون" %}</option>
                            <option value="customers">{% trans "تقرير العملاء" %}</option>
                            <option value="financial">{% trans "التقرير المالي" %}</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="report_description" class="form-label">
                            <i class="fas fa-align-left me-2 text-primary"></i>{% trans "الوصف" %}
                        </label>
                        <textarea class="form-control" id="report_description" name="report_description" rows="3" placeholder="{% trans 'أضف وصفًا مختصرًا للتقرير' %}"></textarea>
                    </div>
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="is_public" name="is_public">
                        <label class="form-check-label" for="is_public">
                            <i class="fas fa-globe me-2 text-primary"></i>{% trans "جعل التقرير عامًا (متاح لجميع المستخدمين)" %}
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>{% trans "إلغاء" %}
                </button>
                <button type="button" class="btn btn-primary" id="saveReportBtn">
                    <i class="fas fa-save me-2"></i>{% trans "حفظ التقرير" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Report Modal -->
<div class="modal fade" id="scheduleReportModal" tabindex="-1" aria-labelledby="scheduleReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title" id="scheduleReportModalLabel">
                    <i class="fas fa-calendar-check me-2"></i>{% trans "جدولة تقرير" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleReportForm" method="post" action="#">
                    {% csrf_token %}
                    <div class="mb-4">
                        <label for="saved_report" class="form-label">
                            <i class="fas fa-file-alt me-2 text-primary"></i>{% trans "التقرير" %}
                        </label>
                        <select class="form-select" id="saved_report" name="saved_report" required>
                            {% for report in saved_reports %}
                            <option value="{{ report.id }}">{{ report.name }} ({{ report.get_report_type_display }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="frequency" class="form-label">
                            <i class="fas fa-sync me-2 text-primary"></i>{% trans "التكرار" %}
                        </label>
                        <select class="form-select" id="frequency" name="frequency" required>
                            <option value="daily">{% trans "يومي" %}</option>
                            <option value="weekly">{% trans "أسبوعي" %}</option>
                            <option value="monthly">{% trans "شهري" %}</option>
                            <option value="quarterly">{% trans "ربع سنوي" %}</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="recipients" class="form-label">
                            <i class="fas fa-envelope me-2 text-primary"></i>{% trans "المستلمون" %}
                        </label>
                        <input type="text" class="form-control" id="recipients" name="recipients" required placeholder="{% trans "بريد إلكتروني مفصول بفواصل" %}">
                    </div>
                    <div class="mb-4">
                        <label for="subject" class="form-label">
                            <i class="fas fa-heading me-2 text-primary"></i>{% trans "الموضوع" %}
                        </label>
                        <input type="text" class="form-control" id="subject" name="subject" required placeholder="{% trans "موضوع التقرير" %}">
                    </div>
                    <div class="mb-4">
                        <label for="message" class="form-label">
                            <i class="fas fa-comment-dots me-2 text-primary"></i>{% trans "الرسالة" %}
                        </label>
                        <textarea class="form-control" id="message" name="message" rows="3" placeholder="{% trans "أضف رسالة اختيارية" %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>{% trans "إلغاء" %}
                </button>
                <button type="button" class="btn btn-primary" id="scheduleReportBtn">
                    <i class="fas fa-calendar-check me-2"></i>{% trans "جدولة التقرير" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable with modern styling
        $('#savedReportsTable').DataTable({
            "language": {
                "url": "{% static 'js/dataTables.arabic.json' %}",
                "lengthMenu": "إظهار _MENU_ من العناصر",
                "info": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
                "search": "بحث:",
                "paginate": {
                    "first": "الأول",
                    "last": "الأخير",
                    "next": "التالي",
                    "previous": "السابق"
                },
                "emptyTable": "لا توجد بيانات متاحة في الجدول",
                "zeroRecords": "لم يتم العثور على أي سجلات مطابقة للبحث"
            },
            "pageLength": 10,
            "lengthMenu": [[5, 10, 15, 20, 25, 50, -1], [5, 10, 15, 20, 25, 50, "الكل"]],
            "dom": 'lfrtip',
            "responsive": true,
            "order": [[2, 'desc']], // Sort by creation date by default
            "initComplete": function() {
                // Add custom styling to DataTable
                $(this).addClass('table-hover');
                $('.dataTables_wrapper').addClass('mt-3');
            }
        });

        // Save Report Button with animation
        const saveReportBtn = document.getElementById('saveReportBtn');
        if (saveReportBtn) {
            saveReportBtn.addEventListener('click', function() {
                const form = document.getElementById('saveReportForm');
                const submitBtn = this;

                // Add loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{% trans "جاري الحفظ" %}';

                // Submit form
                form.submit();

                // Reset button after a delay (in case form submission is slow)
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>{% trans "حفظ التقرير" %}';
                }, 2000);
            });
        }

        // Schedule Report Button with animation
        const scheduleReportBtn = document.getElementById('scheduleReportBtn');
        if (scheduleReportBtn) {
            scheduleReportBtn.addEventListener('click', function() {
                const form = document.getElementById('scheduleReportForm');
                const submitBtn = this;

                // Add loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{% trans "جاري الجدولة" %}';

                // Submit form
                form.submit();

                // Reset button after a delay
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-calendar-check me-2"></i>{% trans "جدولة التقرير" %>';
                }, 2000);
            });
        }

        // Add delete confirmation
        document.querySelectorAll('.delete-report').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const reportId = this.getAttribute('data-id');
                const reportName = this.getAttribute('data-name');

                if (confirm(`هل أنت متأكد من حذف تقرير "${reportName}"؟`)) {
                    // Implement delete functionality here
                    window.location.href = `/reports/delete/${reportId}/`;
                }
            });
        });

        // Add delete confirmation for scheduled reports
        document.querySelectorAll('.delete-schedule').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const scheduleId = this.getAttribute('data-id');
                const scheduleName = this.getAttribute('data-name');

                if (confirm(`هل أنت متأكد من إلغاء جدولة تقرير "${scheduleName}"؟`)) {
                    // Implement delete functionality here
                    window.location.href = `/reports/schedule/delete/${scheduleId}/`;
                }
            });
        });

        // Add fade-in animation to cards
        const cards = document.querySelectorAll('.report-card, .stats-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>

<!-- Custom CSS for animations and styling -->
<style>
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease forwards;
    }

    .gradient-primary {
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    }

    .btn-primary {
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a0db0 0%, #1d65e5 100%);
    }

    .modal-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .modal-footer {
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    .form-control:focus, .form-select:focus {
        border-color: #6a11cb;
        box-shadow: 0 0 0 0.2rem rgba(106, 17, 203, 0.25);
    }
</style>
{% endblock %}
