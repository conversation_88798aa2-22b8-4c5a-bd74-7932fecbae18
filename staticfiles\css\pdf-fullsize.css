/* CSS خاص لجعل PDF يملأ الصفحة بالكامل */

/* إعادة تعيين شاملة */
* {
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* إعدادات HTML و Body */
html {
    width: 210mm !important;
    height: 297mm !important;
    margin: 0 !important;
    padding: 0 !important;
    font-size: 16px !important;
}

body {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    font-family: 'Tajawal', 'Tahoma', 'Arial Unicode MS', sans-serif !important;
    font-size: 14pt !important;
    line-height: 1.6 !important;
    color: #000 !important;
    background: white !important;
    direction: rtl !important;
    text-align: right !important;
}

/* حاوي الفاتورة الرئيسي */
.invoice-container {
    width: 100% !important;
    max-width: none !important;
    min-height: 100% !important;
    height: auto !important;
    margin: 0 !important;
    padding: 20mm !important;
    background: white !important;
    box-sizing: border-box !important;
    overflow: visible !important;
}

/* تحسين العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Tajawal', 'Tahoma', sans-serif !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 15px !important;
    line-height: 1.3 !important;
}

h1 { font-size: 28pt !important; }
h2 { font-size: 24pt !important; }
h3 { font-size: 20pt !important; }
h4 { font-size: 18pt !important; }
h5 { font-size: 16pt !important; }
h6 { font-size: 14pt !important; }

/* تحسين الفقرات */
p {
    font-size: 14pt !important;
    line-height: 1.6 !important;
    margin-bottom: 12px !important;
    color: #000 !important;
}

/* تحسين الجداول */
table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin-bottom: 25px !important;
    font-size: 13pt !important;
    border: 2px solid #000 !important;
}

th, td {
    padding: 12px 8px !important;
    border: 1px solid #000 !important;
    text-align: center !important;
    vertical-align: middle !important;
    font-size: 13pt !important;
    line-height: 1.4 !important;
}

th {
    background-color: #f0f0f0 !important;
    font-weight: bold !important;
    color: #000 !important;
}

/* تحسين الصفوف والأعمدة */
.row {
    width: 100% !important;
    margin-bottom: 20px !important;
    display: table !important;
    clear: both !important;
}

.col-md-6 {
    width: 50% !important;
    float: right !important;
    padding: 0 15px !important;
    display: table-cell !important;
    vertical-align: top !important;
}

.col-md-4 {
    width: 33.333% !important;
    float: right !important;
    padding: 0 10px !important;
    display: table-cell !important;
    vertical-align: top !important;
}

.col-md-12 {
    width: 100% !important;
    padding: 0 15px !important;
    display: block !important;
}

/* تحسين رأس الفاتورة */
.invoice-header {
    border-bottom: 3px solid #000 !important;
    padding-bottom: 25px !important;
    margin-bottom: 30px !important;
    page-break-after: avoid !important;
}

.invoice-title {
    font-size: 28pt !important;
    font-weight: bold !important;
    color: #000 !important;
    margin-bottom: 15px !important;
    text-align: center !important;
}

/* تحسين قوائم التعريف */
dl {
    margin-bottom: 20px !important;
}

dt {
    font-weight: bold !important;
    font-size: 14pt !important;
    margin-bottom: 8px !important;
    color: #000 !important;
    display: inline-block !important;
    width: 40% !important;
    vertical-align: top !important;
}

dd {
    font-size: 14pt !important;
    margin-bottom: 12px !important;
    color: #000 !important;
    display: inline-block !important;
    width: 58% !important;
    vertical-align: top !important;
    padding-right: 10px !important;
}

/* تحسين الأرقام */
.number, .arabic-number {
    font-family: 'Tajawal', 'Tahoma', monospace !important;
    font-weight: 600 !important;
    font-size: 14pt !important;
    direction: ltr !important;
    display: inline-block !important;
}

/* تحسين الشارات */
.badge {
    display: inline-block !important;
    padding: 8px 12px !important;
    font-size: 12pt !important;
    font-weight: bold !important;
    border-radius: 4px !important;
    color: white !important;
    background-color: #007bff !important;
}

/* تحسين معلومات الدفع */
.payment-info {
    background-color: #f8f9fa !important;
    border: 2px solid #007bff !important;
    padding: 20px !important;
    margin-bottom: 25px !important;
    border-radius: 8px !important;
}

.payment-status.paid {
    background-color: #28a745 !important;
    color: white !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
}

.payment-status.partial {
    background-color: #ffc107 !important;
    color: #000 !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
}

.payment-status.unpaid {
    background-color: #dc3545 !important;
    color: white !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
}

/* تحسين ملخص الفاتورة */
.invoice-summary {
    background-color: #f8f9fa !important;
    border: 2px solid #007bff !important;
    padding: 25px !important;
    margin-bottom: 30px !important;
    border-radius: 8px !important;
}

/* تحسين منطقة التوقيع */
.signature-area {
    margin-top: 40px !important;
    page-break-inside: avoid !important;
    display: table !important;
    width: 100% !important;
}

.signature-box {
    display: table-cell !important;
    width: 33.333% !important;
    text-align: center !important;
    padding: 0 20px !important;
    vertical-align: top !important;
}

.signature-line {
    border-top: 2px solid #000 !important;
    margin-top: 60px !important;
    margin-bottom: 10px !important;
}

/* تحسين الشروط والأحكام */
.terms-conditions {
    font-size: 11pt !important;
    margin-top: 30px !important;
    padding: 20px !important;
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 6px !important;
}

.terms-conditions ol {
    padding-right: 25px !important;
    margin: 0 !important;
}

.terms-conditions li {
    margin-bottom: 8px !important;
    line-height: 1.5 !important;
}

/* تحسين QR Code */
.qr-code {
    text-align: center !important;
    margin: 25px auto !important;
}

.qr-code img {
    max-width: 120px !important;
    max-height: 120px !important;
    border: 1px solid #ddd !important;
    padding: 5px !important;
}

/* إعدادات الطباعة */
@page {
    size: A4 !important;
    margin: 15mm !important;
}

@media print {
    html, body {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .invoice-container {
        width: 100% !important;
        height: 100% !important;
        padding: 15mm !important;
        margin: 0 !important;
    }
    
    .page-break {
        page-break-before: always !important;
    }
    
    .no-page-break {
        page-break-inside: avoid !important;
    }
}

/* إخفاء العناصر غير المطلوبة */
.no-print {
    display: none !important;
}

/* تحسين الألوان للطباعة */
.text-primary { color: #000 !important; }
.text-success { color: #000 !important; }
.text-danger { color: #000 !important; }
.text-warning { color: #000 !important; }
.text-info { color: #000 !important; }
