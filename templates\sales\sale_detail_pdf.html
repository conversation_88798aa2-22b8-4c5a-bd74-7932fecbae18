<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل البيع - {{ sale.invoice_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap');
        
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 24px;
        }
        
        .company-info {
            margin-bottom: 20px;
        }
        
        .info-section {
            margin-bottom: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .info-section h3 {
            color: #007bff;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: center;
        }
        
        .items-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .items-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .summary-table {
            width: 50%;
            margin-left: auto;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .summary-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
        }
        
        .summary-table .label {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 60%;
        }
        
        .summary-table .value {
            text-align: center;
            width: 40%;
        }
        
        .total-row {
            background-color: #007bff !important;
            color: white !important;
            font-weight: bold;
        }
        
        .payments-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .payments-table th,
        .payments-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: center;
        }
        
        .payments-table th {
            background-color: #28a745;
            color: white;
            font-weight: bold;
        }
        
        .notes-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .notes-section h4 {
            color: #856404;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        {% if company_info %}
            <h1>{{ company_info.name }}</h1>
            {% if company_info.address %}
                <p>{{ company_info.address }}</p>
            {% endif %}
            {% if company_info.phone %}
                <p>هاتف: {{ company_info.phone }}</p>
            {% endif %}
        {% else %}
            <h1>تفاصيل البيع</h1>
        {% endif %}
    </div>

    <!-- Sale Information -->
    <div class="info-section">
        <h3>معلومات البيع</h3>
        <div class="info-row">
            <span class="info-label">رقم الفاتورة:</span>
            <span class="info-value">{{ sale.invoice_number }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">التاريخ:</span>
            <span class="info-value">{{ sale.date|date:"Y-m-d H:i" }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">الموظف:</span>
            <span class="info-value">{{ sale.employee.get_full_name }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">الحالة:</span>
            <span class="info-value">{% if sale.is_paid %}مدفوع{% else %}غير مدفوع{% endif %}</span>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="info-section">
        <h3>معلومات العميل</h3>
        <div class="info-row">
            <span class="info-label">الاسم:</span>
            <span class="info-value">{{ sale.customer.name }}</span>
        </div>
        {% if sale.customer.phone %}
        <div class="info-row">
            <span class="info-label">الهاتف:</span>
            <span class="info-value">{{ sale.customer.phone }}</span>
        </div>
        {% endif %}
        {% if sale.customer.email %}
        <div class="info-row">
            <span class="info-label">البريد الإلكتروني:</span>
            <span class="info-value">{{ sale.customer.email }}</span>
        </div>
        {% endif %}
        {% if sale.customer.address %}
        <div class="info-row">
            <span class="info-label">العنوان:</span>
            <span class="info-value">{{ sale.customer.address }}</span>
        </div>
        {% endif %}
    </div>

    <!-- Sale Items -->
    <div class="info-section">
        <h3>عناصر البيع</h3>
        <table class="items-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ item.product.name }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.price|floatformat:2 }} درهم</td>
                    <td>{{ item.total|floatformat:2 }} درهم</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Financial Summary -->
    <div class="info-section">
        <h3>الملخص المالي</h3>
        <table class="summary-table">
            <tr>
                <td class="label">المبلغ الفرعي:</td>
                <td class="value">{{ sale.subtotal|floatformat:2 }} درهم</td>
            </tr>
            {% if sale.tax_amount %}
            <tr>
                <td class="label">الضريبة:</td>
                <td class="value">{{ sale.tax_amount|floatformat:2 }} درهم</td>
            </tr>
            {% endif %}
            <tr class="total-row">
                <td class="label">الإجمالي:</td>
                <td class="value">{{ sale.total_amount|floatformat:2 }} درهم</td>
            </tr>
            <tr>
                <td class="label">المدفوع:</td>
                <td class="value">{{ total_paid|floatformat:2 }} درهم</td>
            </tr>
            <tr>
                <td class="label">المتبقي:</td>
                <td class="value">{{ remaining_amount|floatformat:2 }} درهم</td>
            </tr>
        </table>
    </div>

    <!-- Payments -->
    {% if payments %}
    <div class="info-section">
        <h3>المدفوعات</h3>
        <table class="payments-table">
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>المبلغ</th>
                    <th>طريقة الدفع</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                {% for payment in payments %}
                <tr>
                    <td>{{ payment.date|date:"Y-m-d" }}</td>
                    <td>{{ payment.amount|floatformat:2 }} درهم</td>
                    <td>{{ payment.get_payment_method_display|default:"-" }}</td>
                    <td>{{ payment.notes|default:"-" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- Notes -->
    {% if sale.notes %}
    <div class="notes-section">
        <h4>ملاحظات</h4>
        <p>{{ sale.notes }}</p>
    </div>
    {% endif %}

    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير في {{ "now"|date:"Y-m-d H:i" }}</p>
    </div>
</body>
</html>
