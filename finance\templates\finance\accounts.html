{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "الحسابات" %}{% endblock %}

{% block extra_css %}
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* تطبيق خط Cairo على الصفحة */
    body {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    /* تصحيح حجم الخط في شريط التنقل */
    .nav-link {
        font-size: 1.1rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
    
    .navbar-brand {
        font-size: 1.2rem !important;
        font-family: 'Cairo', sans-serif !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "الحسابات" %}</h5>
            <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                <i class="fas fa-plus"></i> {% trans "إضافة حساب جديد" %}
            </button>
        </div>
        <div class="card-body">
            <div class="row">
                {% for account in accounts %}
                <div class="col-md-4 mb-4">
                    <div class="card h-100 {% if account.current_balance < 0 %}border-danger{% elif account.current_balance > 0 %}border-success{% endif %}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ account.name }}</h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton{{ account.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton{{ account.id }}">
                                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#editAccountModal{{ account.id }}"><i class="fas fa-edit"></i> {% trans "تعديل" %}</a></li>
                                    {% if not account.is_active %}
                                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#activateAccountModal{{ account.id }}"><i class="fas fa-check"></i> {% trans "تفعيل" %}</a></li>
                                    {% else %}
                                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#deactivateAccountModal{{ account.id }}"><i class="fas fa-ban"></i> {% trans "تعطيل" %}</a></li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                <strong>{% trans "النوع:" %}</strong> {{ account.account_type }}
                            </p>
                            <p class="card-text">
                                <strong>{% trans "الرصيد الحالي:" %}</strong>
                                <span class="{% if account.current_balance < 0 %}text-danger{% elif account.current_balance > 0 %}text-success{% endif %}">
                                    {{ account.current_balance }} {% trans "د.م" %}
                                </span>
                            </p>
                            <p class="card-text">
                                <strong>{% trans "الحالة:" %}</strong>
                                {% if account.is_active %}
                                <span class="badge bg-success">{% trans "نشط" %}</span>
                                {% else %}
                                <span class="badge bg-danger">{% trans "غير نشط" %}</span>
                                {% endif %}
                            </p>
                            <p class="card-text">
                                <small class="text-muted">{% trans "تاريخ الإنشاء:" %} {{ account.created_at|date }}</small>
                            </p>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info">
                        {% trans "لا توجد حسابات مضافة بعد. قم بإضافة حساب جديد للبدء." %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Modal: Add Account -->
<div class="modal fade" id="addAccountModal" tabindex="-1" aria-labelledby="addAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAccountModalLabel">{% trans "إضافة حساب جديد" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'finance:add_account' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="name">{% trans "اسم الحساب" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="account_type">{% trans "نوع الحساب" %} <span class="text-danger">*</span></label>
                        <select class="form-control" id="account_type" name="account_type" required>
                            <option value="">{% trans "اختر نوع الحساب" %}</option>
                            <option value="cash">{% trans "نقدي" %}</option>
                            <option value="bank">{% trans "بنكي" %}</option>
                            <option value="credit_card">{% trans "بطاقة ائتمان" %}</option>
                            <option value="other">{% trans "أخرى" %}</option>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="initial_balance">{% trans "الرصيد الافتتاحي" %}</label>
                        <div class="input-group">
                            <input type="number" step="0.01" class="form-control" id="initial_balance" name="initial_balance" value="0">
                            <div class="input-group-append">
                                <span class="input-group-text">{% trans "د.م" %}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="description">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Account Modals -->
{% for account in accounts %}
<div class="modal fade" id="editAccountModal{{ account.id }}" tabindex="-1" aria-labelledby="editAccountModalLabel{{ account.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAccountModalLabel{{ account.id }}">{% trans "تعديل الحساب" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'finance:edit_account' account.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="name{{ account.id }}">{% trans "اسم الحساب" %} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name{{ account.id }}" name="name" value="{{ account.name }}" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="account_type{{ account.id }}">{% trans "نوع الحساب" %} <span class="text-danger">*</span></label>
                        <select class="form-control" id="account_type{{ account.id }}" name="account_type" required>
                            <option value="cash" {% if account.account_type == 'cash' %}selected{% endif %}>{% trans "نقدي" %}</option>
                            <option value="bank" {% if account.account_type == 'bank' %}selected{% endif %}>{% trans "بنكي" %}</option>
                            <option value="credit_card" {% if account.account_type == 'credit_card' %}selected{% endif %}>{% trans "بطاقة ائتمان" %}</option>
                            <option value="other" {% if account.account_type == 'other' %}selected{% endif %}>{% trans "أخرى" %}</option>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="description{{ account.id }}">{% trans "الوصف" %}</label>
                        <textarea class="form-control" id="description{{ account.id }}" name="description" rows="3">{{ account.description }}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-primary">{% trans "حفظ التغييرات" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Deactivate Account Modal -->
<div class="modal fade" id="deactivateAccountModal{{ account.id }}" tabindex="-1" aria-labelledby="deactivateAccountModalLabel{{ account.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deactivateAccountModalLabel{{ account.id }}">{% trans "تعطيل الحساب" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'finance:deactivate_account' account.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <p>{% trans "هل أنت متأكد من تعطيل هذا الحساب؟" %}</p>
                    <p>{% trans "لن يظهر الحساب في القوائم النشطة، ولكن ستظل البيانات موجودة." %}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-danger">{% trans "تعطيل" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Activate Account Modal -->
<div class="modal fade" id="activateAccountModal{{ account.id }}" tabindex="-1" aria-labelledby="activateAccountModalLabel{{ account.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="activateAccountModalLabel{{ account.id }}">{% trans "تفعيل الحساب" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'finance:activate_account' account.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <p>{% trans "هل أنت متأكد من تفعيل هذا الحساب؟" %}</p>
                    <p>{% trans "سيظهر الحساب في القوائم النشطة ويمكن استخدامه في المعاملات." %}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "إلغاء" %}</button>
                    <button type="submit" class="btn btn-success">{% trans "تفعيل" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}
