from django.urls import path
from . import views

app_name = 'sales'

urlpatterns = [
    path('', views.index, name='index'),
    path('new/', views.new_sale, name='new_sale'),
    path('view/<int:sale_id>/', views.view_sale, name='view_sale'),
    path('edit/<int:sale_id>/', views.edit_sale, name='edit_sale'),
    path('delete/<int:sale_id>/', views.delete_sale, name='delete_sale'),
    path('invoice/<int:sale_id>/', views.invoice, name='invoice'),
    path('invoice/<int:sale_id>/pdf/', views.generate_invoice_pdf, name='invoice_pdf'),
    path('invoice/<int:sale_id>/pdf-fullsize/', views.generate_invoice_pdf_fullsize, name='invoice_pdf_fullsize'),
    path('invoice/<int:sale_id>/print-fullsize/', views.invoice_print_fullsize, name='invoice_print_fullsize'),
    path('email/<int:sale_id>/', views.email_invoice, name='email_invoice'),
    path('add_payment/<int:sale_id>/', views.add_payment, name='add_payment'),
    path('mark_as_paid/<int:sale_id>/', views.mark_as_paid, name='mark_as_paid'),
    path('sale_detail/<int:sale_id>/', views.sale_detail, name='sale_detail'),
    path('print_invoice/<int:sale_id>/', views.print_invoice, name='print_invoice'),
    path('check_stock/', views.check_stock, name='check_stock'),
    path('get_promotions/', views.get_promotions, name='get_promotions'),
    path('alerts/', views.alerts, name='alerts'),
    path('export/excel/', views.export_sales_excel, name='export_sales_excel'),
    path('export/pdf/', views.export_sales_pdf, name='export_sales_pdf'),
    # تصدير تفاصيل البيع الفردي
    path('export/sale/<int:sale_id>/pdf/', views.export_sale_detail_pdf, name='export_sale_pdf'),
    path('export/sale/<int:sale_id>/excel/', views.export_sale_detail_excel, name='export_sale_excel'),
    # اختبار النصوص المختلطة
    path('test-mixed-text/', views.test_mixed_text, name='test_mixed_text'),
    # تشخيص المدفوعات
    path('debug-payments/<int:sale_id>/', views.debug_payments, name='debug_payments'),
    path('create-test-payment/<int:sale_id>/', views.create_test_payment, name='create_test_payment'),
]
