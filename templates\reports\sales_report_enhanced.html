{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "تقرير المبيعات المتقدم" %} | {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/daterangepicker.css' %}">
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* RTL Support Enhanced */
    body {
        direction: rtl;
        text-align: right;
        font-family: '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
    }

    .container-fluid {
        direction: rtl;
    }

    /* Enhanced Stats Cards */
    .stats-card {
        background: linear-gradient(135deg, #fff 0%, #f8f9fc 100%);
        border-radius: 15px;
        border: none;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
        height: 100%;
        border-right: 4px solid transparent;
        min-height: 140px;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(0,0,0,0.15);
    }

    .stats-card-primary { border-right-color: #4e73df; }
    .stats-card-success { border-right-color: #1cc88a; }
    .stats-card-warning { border-right-color: #f6c23e; }
    .stats-card-info { border-right-color: #36b9cc; }

    .stats-card-body {
        padding: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .stats-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1rem;
    }

    .stats-text {
        flex: 1;
        min-width: 0;
    }

    .stats-title {
        font-size: 0.85rem;
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 0.5rem;
        line-height: 1.2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .stats-number {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2e59d9;
        margin-bottom: 0.25rem;
        line-height: 1.1;
        direction: ltr;
        text-align: right;
    }

    .stats-subtitle {
        font-size: 0.75rem;
        color: #858796;
        line-height: 1.3;
        margin-top: 0.25rem;
    }

    .stats-icon {
        font-size: 2.2rem;
        color: rgba(78, 115, 223, 0.15);
        flex-shrink: 0;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(78, 115, 223, 0.05);
        border-radius: 50%;
    }

    .stats-card-primary .stats-icon {
        color: rgba(78, 115, 223, 0.3);
        background: rgba(78, 115, 223, 0.1);
    }

    .stats-card-success .stats-icon {
        color: rgba(28, 200, 138, 0.3);
        background: rgba(28, 200, 138, 0.1);
    }

    .stats-card-warning .stats-icon {
        color: rgba(246, 194, 62, 0.3);
        background: rgba(246, 194, 62, 0.1);
    }

    .stats-card-info .stats-icon {
        color: rgba(54, 185, 204, 0.3);
        background: rgba(54, 185, 204, 0.1);
    }

    /* Enhanced Filter Section */
    .filter-section {
        background: linear-gradient(135deg, #f8f9fc 0%, #fff 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        border: 1px solid #e3e6f0;
    }

    .search-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        border: 1px solid #e3e6f0;
    }

    /* Enhanced Table Styles */
    .table-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        border: 1px solid #e3e6f0;
        margin-bottom: 2rem;
    }

    .table thead th {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-align: center;
        padding: 1rem 0.75rem;
        font-size: 0.85rem;
    }

    .table tbody td {
        padding: 0.75rem;
        vertical-align: middle;
        border-color: #e3e6f0;
    }

    .table tbody tr:hover {
        background-color: #f8f9fc;
    }

    /* Export Buttons */
    .export-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        border: 1px solid #e3e6f0;
    }

    .btn-export {
        margin-left: 0.5rem;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* Chart Container */
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        border: 1px solid #e3e6f0;
    }

    /* Form Enhancements */
    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #d1d3e2;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .btn {
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stats-card-body { padding: 1rem; }
        .stats-number { font-size: 1.5rem; }
        .stats-icon { font-size: 1.8rem; width: 40px; height: 40px; }
        .stats-title { font-size: 0.8rem; }
        .stats-subtitle { font-size: 0.7rem; }
        .filter-section { padding: 1rem; }
        .table-container { padding: 1rem; }
        .stats-content { gap: 0.5rem; }
    }

    @media (max-width: 576px) {
        .stats-card { min-height: 120px; }
        .stats-card-body { padding: 0.75rem; }
        .stats-number { font-size: 1.3rem; }
        .stats-icon { font-size: 1.5rem; width: 35px; height: 35px; }
        .stats-title { font-size: 0.75rem; }
        .stats-subtitle { font-size: 0.65rem; }
    }

    /* Loading States */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Alert Enhancements */
    .alert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    /* Quick Stats */
    .quick-stat {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        backdrop-filter: blur(10px);
    }

    .quick-stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .quick-stat-label {
        font-size: 0.85rem;
        opacity: 0.8;
    }

    /* No Data State */
    .no-data {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .no-data i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* Status Badges */
    .status-paid {
        background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-unpaid {
        background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-partial {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Enhanced Page Header -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-chart-line me-3"></i>{% trans "تقرير المبيعات المتقدم" %}
                    </h1>
                    <p class="page-subtitle">{% trans "تحليل شامل لأداء المبيعات مع إحصائيات متقدمة وتصدير متعدد التنسيقات" %}</p>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <div class="col-6">
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ sales.count }}</div>
                                <div class="quick-stat-label">{% trans "إجمالي المبيعات" %}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="quick-stat">
                                <div class="quick-stat-number">{{ total_revenue|floatformat:0 }}</div>
                                <div class="quick-stat-label">{% trans "إجمالي الإيرادات" %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export and Actions Section -->
    <div class="export-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2 text-primary"></i>{% trans "تصدير وإجراءات التقرير" %}
                </h5>
                <p class="text-muted mb-0">{% trans "قم بتصدير البيانات أو تطبيق إجراءات متقدمة على التقرير" %}</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-danger btn-export" id="exportPdf">
                        <i class="fas fa-file-pdf me-1"></i>PDF
                    </button>
                    <button type="button" class="btn btn-success btn-export" id="exportExcel">
                        <i class="fas fa-file-excel me-1"></i>Excel
                    </button>
                    <button type="button" class="btn btn-info btn-export" id="exportCsv">
                        <i class="fas fa-file-csv me-1"></i>CSV
                    </button>
                    <button type="button" class="btn btn-secondary btn-export" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>{% trans "طباعة" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters Section -->
    <div class="collapse" id="filterSection">
        <div class="filter-section">
            <form id="filterForm" method="GET">
                <div class="row mb-3">
                    <div class="col-12">
                        <h5 class="mb-3">
                            <i class="fas fa-filter me-2 text-primary"></i>{% trans "فلاتر متقدمة للبحث" %}
                        </h5>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="daterange" class="form-label">
                            <i class="fas fa-calendar me-1"></i>{% trans "نطاق التاريخ" %}
                        </label>
                        <input type="text" class="form-control" id="daterange" name="daterange"
                               value="{{ start_date|date:'Y-m-d' }} - {{ end_date|date:'Y-m-d' }}">
                        <input type="hidden" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                        <input type="hidden" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="category" class="form-label">
                            <i class="fas fa-tags me-1"></i>{% trans "فئة المنتج" %}
                        </label>
                        <select class="form-select" id="category" name="category">
                            <option value="">{% trans "جميع الفئات" %}</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if selected_category == category.id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="customer" class="form-label">
                            <i class="fas fa-user me-1"></i>{% trans "العميل" %}
                        </label>
                        <select class="form-select" id="customer" name="customer">
                            <option value="">{% trans "جميع العملاء" %}</option>
                            {% for customer in all_customers %}
                            <option value="{{ customer.id }}" {% if selected_customer == customer.id %}selected{% endif %}>
                                {{ customer.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="payment_status" class="form-label">
                            <i class="fas fa-credit-card me-1"></i>{% trans "حالة الدفع" %}
                        </label>
                        <select class="form-select" id="payment_status" name="payment_status">
                            <option value="">{% trans "جميع الحالات" %}</option>
                            <option value="paid" {% if selected_payment_status == 'paid' %}selected{% endif %}>
                                ✓ {% trans "مدفوع" %}
                            </option>
                            <option value="unpaid" {% if selected_payment_status == 'unpaid' %}selected{% endif %}>
                                ✗ {% trans "غير مدفوع" %}
                            </option>
                            <option value="partial" {% if selected_payment_status == 'partial' %}selected{% endif %}>
                                ◐ {% trans "مدفوع جزئياً" %}
                            </option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="payment_method" class="form-label">
                            <i class="fas fa-money-bill me-1"></i>{% trans "طريقة الدفع" %}
                        </label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="">{% trans "جميع طرق الدفع" %}</option>
                            <option value="cash" {% if selected_payment_method == 'cash' %}selected{% endif %}>
                                💵 {% trans "نقدي" %}
                            </option>
                            <option value="card" {% if selected_payment_method == 'card' %}selected{% endif %}>
                                💳 {% trans "بطاقة ائتمان" %}
                            </option>
                            <option value="transfer" {% if selected_payment_method == 'transfer' %}selected{% endif %}>
                                🏦 {% trans "تحويل بنكي" %}
                            </option>
                            <option value="check" {% if selected_payment_method == 'check' %}selected{% endif %}>
                                📄 {% trans "شيك" %}
                            </option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="min_amount" class="form-label">
                            <i class="fas fa-sort-numeric-up me-1"></i>{% trans "الحد الأدنى للمبلغ" %}
                        </label>
                        <input type="number" class="form-control" id="min_amount" name="min_amount"
                               value="{{ min_amount }}" placeholder="0" min="0" step="0.01">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="max_amount" class="form-label">
                            <i class="fas fa-sort-numeric-down me-1"></i>{% trans "الحد الأعلى للمبلغ" %}
                        </label>
                        <input type="number" class="form-control" id="max_amount" name="max_amount"
                               value="{{ max_amount }}" placeholder="10000" min="0" step="0.01">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="sort_by" class="form-label">
                            <i class="fas fa-sort me-1"></i>{% trans "ترتيب حسب" %}
                        </label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="date" {% if selected_sort == 'date' %}selected{% endif %}>{% trans "التاريخ" %}</option>
                            <option value="amount" {% if selected_sort == 'amount' %}selected{% endif %}>{% trans "المبلغ" %}</option>
                            <option value="customer" {% if selected_sort == 'customer' %}selected{% endif %}>{% trans "العميل" %}</option>
                            <option value="invoice_number" {% if selected_sort == 'invoice_number' %}selected{% endif %}>{% trans "رقم الفاتورة" %}</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>{% trans "تطبيق الفلاتر" %}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                    <i class="fas fa-undo me-1"></i>{% trans "إعادة تعيين" %}
                                </button>
                                <button type="button" class="btn btn-info" data-bs-toggle="collapse" data-bs-target="#filterSection">
                                    <i class="fas fa-times me-1"></i>{% trans "إخفاء الفلاتر" %}
                                </button>
                            </div>
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "عدد النتائج" %}: <span id="resultsCount">{{ sales.count }}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Search Section -->
    <div class="search-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="globalSearch"
                           placeholder="{% trans 'البحث السريع في المبيعات (رقم الفاتورة، العميل، المنتجات...)' %}">
                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-info" type="button" data-bs-toggle="collapse"
                        data-bs-target="#filterSection" aria-expanded="false">
                    <i class="fas fa-filter me-1"></i>{% trans "فلاتر متقدمة" %}
                </button>
                <button class="btn btn-success" type="button" id="refreshData">
                    <i class="fas fa-sync-alt me-1"></i>{% trans "تحديث" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Sales Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-primary h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-text">
                            <div class="stats-title">{% trans "إجمالي المبيعات" %}</div>
                            <div class="stats-number">{{ total_sales|floatformat:0 }}</div>
                            <div class="stats-subtitle">
                                {% if sales_growth > 0 %}
                                    <span class="text-success"><i class="fas fa-arrow-up"></i> {{ sales_growth|floatformat:1 }}%</span>
                                {% elif sales_growth < 0 %}
                                    <span class="text-danger"><i class="fas fa-arrow-down"></i> {{ sales_growth|floatformat:1 }}%</span>
                                {% else %}
                                    <span class="text-muted"><i class="fas fa-equals"></i> 0%</span>
                                {% endif %}
                                {% trans "مقارنة بالفترة السابقة" %}
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-success h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-text">
                            <div class="stats-title">{% trans "إجمالي الإيرادات" %}</div>
                            <div class="stats-number">{{ total_revenue|floatformat:0 }} د.م</div>
                            <div class="stats-subtitle">
                                {% if revenue_growth > 0 %}
                                    <span class="text-success"><i class="fas fa-arrow-up"></i> {{ revenue_growth|floatformat:1 }}%</span>
                                {% elif revenue_growth < 0 %}
                                    <span class="text-danger"><i class="fas fa-arrow-down"></i> {{ revenue_growth|floatformat:1 }}%</span>
                                {% else %}
                                    <span class="text-muted"><i class="fas fa-equals"></i> 0%</span>
                                {% endif %}
                                {% trans "مقارنة بالفترة السابقة" %}
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-info h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-text">
                            <div class="stats-title">{% trans "متوسط قيمة البيع" %}</div>
                            <div class="stats-number">{{ average_sale_amount|floatformat:0 }} د.م</div>
                            <div class="stats-subtitle">
                                <span class="text-info">{% trans "لكل عملية بيع" %}</span>
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card stats-card-warning h-100">
                <div class="stats-card-body">
                    <div class="stats-content">
                        <div class="stats-text">
                            <div class="stats-title">{% trans "عدد العملاء" %}</div>
                            <div class="stats-number">{{ unique_customers_count }}</div>
                            <div class="stats-subtitle">
                                <span class="text-warning">{% trans "عميل فريد" %}</span>
                            </div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Data Table -->
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-table me-2 text-primary"></i>{% trans "تفاصيل المبيعات" %}
            </h5>
            <div class="d-flex align-items-center">
                <small class="text-muted me-3">
                    {% trans "عرض" %} {{ sales.count }} {% trans "من إجمالي" %} {{ total_sales_count }} {% trans "مبيعة" %}
                </small>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="checkbox" class="btn-check" id="showPaid" checked>
                    <label class="btn btn-outline-success" for="showPaid">{% trans "مدفوع" %}</label>

                    <input type="checkbox" class="btn-check" id="showUnpaid" checked>
                    <label class="btn btn-outline-danger" for="showUnpaid">{% trans "غير مدفوع" %}</label>

                    <input type="checkbox" class="btn-check" id="showPartial" checked>
                    <label class="btn btn-outline-warning" for="showPartial">{% trans "جزئي" %}</label>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover" id="salesTable">
                <thead>
                    <tr>
                        <th>{% trans "رقم الفاتورة" %}</th>
                        <th>{% trans "التاريخ" %}</th>
                        <th>{% trans "العميل" %}</th>
                        <th>{% trans "إجمالي المبلغ" %}</th>
                        <th>{% trans "حالة الدفع" %}</th>
                        <th>{% trans "طريقة الدفع" %}</th>
                        <th>{% trans "عدد الأصناف" %}</th>
                        <th>{% trans "الإجراءات" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in sales %}
                    <tr class="sale-row" data-payment-status="{{ sale.payment_status }}">
                        <td>
                            <strong class="text-primary">{{ sale.invoice_number }}</strong>
                            <br><small class="text-muted">#{{ sale.id }}</small>
                        </td>
                        <td>
                            <div>{{ sale.date|date:"Y-m-d" }}</div>
                            <small class="text-muted">{{ sale.date|date:"H:i" }}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <i class="fas fa-user-circle text-primary fa-2x"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">{{ sale.customer.name }}</div>
                                    <small class="text-muted">{{ sale.customer.phone|default:"" }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="fw-bold text-success">{{ sale.total_amount|floatformat:2 }} د.م</div>
                            {% if sale.discount_amount > 0 %}
                                <small class="text-muted">{% trans "خصم" %}: {{ sale.discount_amount|floatformat:2 }} د.م</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if sale.payment_status == 'paid' %}
                                <span class="status-paid">✓ {% trans "مدفوع" %}</span>
                            {% elif sale.payment_status == 'unpaid' %}
                                <span class="status-unpaid">✗ {% trans "غير مدفوع" %}</span>
                            {% else %}
                                <span class="status-partial">◐ {% trans "جزئي" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if sale.payment_method == 'cash' %}
                                <i class="fas fa-money-bill-wave text-success me-1"></i>{% trans "نقدي" %}
                            {% elif sale.payment_method == 'card' %}
                                <i class="fas fa-credit-card text-primary me-1"></i>{% trans "بطاقة" %}
                            {% elif sale.payment_method == 'transfer' %}
                                <i class="fas fa-university text-info me-1"></i>{% trans "تحويل" %}
                            {% elif sale.payment_method == 'check' %}
                                <i class="fas fa-file-invoice text-warning me-1"></i>{% trans "شيك" %}
                            {% else %}
                                <i class="fas fa-question-circle text-muted me-1"></i>{% trans "غير محدد" %}
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ sale.items.count }}</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'sales:sale_detail' sale.id %}" class="btn btn-outline-primary" title="{% trans 'عرض التفاصيل' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'sales:print_invoice' sale.id %}" class="btn btn-outline-success" title="{% trans 'طباعة الفاتورة' %}" target="_blank">
                                    <i class="fas fa-print"></i>
                                </a>
                                {% if sale.payment_status != 'paid' %}
                                    <button class="btn btn-outline-warning" title="{% trans 'تسجيل دفعة' %}" data-bs-toggle="modal" data-bs-target="#paymentModal" data-sale-id="{{ sale.id }}">
                                        <i class="fas fa-dollar-sign"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8">
                            <div class="no-data">
                                <i class="fas fa-inbox"></i>
                                <h5>{% trans "لا توجد مبيعات" %}</h5>
                                <p>{% trans "لم يتم العثور على مبيعات تطابق معايير البحث المحددة" %}</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
<script src="{% static 'js/moment.min.js' %}"></script>
<script src="{% static 'js/daterangepicker.min.js' %}"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable with enhanced features
    var salesTable = $('#salesTable').DataTable({
        "language": {
            "url": "{% static 'js/dataTables.arabic.json' %}",
            "search": "{% trans 'البحث:' %}",
            "lengthMenu": "{% trans 'عرض' %} _MENU_ {% trans 'عنصر' %}",
            "info": "{% trans 'عرض' %} _START_ {% trans 'إلى' %} _END_ {% trans 'من أصل' %} _TOTAL_ {% trans 'عنصر' %}",
            "paginate": {
                "first": "{% trans 'الأول' %}",
                "last": "{% trans 'الأخير' %}",
                "next": "{% trans 'التالي' %}",
                "previous": "{% trans 'السابق' %}"
            },
            "emptyTable": "{% trans 'لا توجد بيانات متاحة في الجدول' %}",
            "zeroRecords": "{% trans 'لم يتم العثور على نتائج مطابقة' %}"
        },
        "order": [[1, "desc"]],
        "responsive": true,
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "{% trans 'الكل' %}"]],
        "dom": '<"d-flex justify-content-between align-items-center mb-3"<"d-flex align-items-center"l><"d-flex"f>>t<"d-flex justify-content-between align-items-center mt-3"<"d-flex align-items-center"i><"d-flex"p>>',
        "columnDefs": [
            { "orderable": false, "targets": [7] }, // Actions column
            { "className": "text-center", "targets": [4, 5, 6, 7] }
        ]
    });

    // Global Search Enhancement
    $('#globalSearch').on('keyup', function() {
        salesTable.search(this.value).draw();
        updateResultsCount();
    });

    // Clear Search
    $('#clearSearch').on('click', function() {
        $('#globalSearch').val('');
        salesTable.search('').draw();
        updateResultsCount();
    });

    // Payment Status Filter
    $('#showPaid, #showUnpaid, #showPartial').on('change', function() {
        var showPaid = $('#showPaid').is(':checked');
        var showUnpaid = $('#showUnpaid').is(':checked');
        var showPartial = $('#showPartial').is(':checked');

        salesTable.rows().every(function() {
            var row = this.node();
            var status = $(row).data('payment-status');
            var show = false;

            if (status === 'paid' && showPaid) show = true;
            if (status === 'unpaid' && showUnpaid) show = true;
            if (status === 'partial' && showPartial) show = true;

            if (show) {
                $(row).show();
            } else {
                $(row).hide();
            }
        });

        salesTable.draw();
        updateResultsCount();
    });

    // Date Range Picker
    $('#daterange').daterangepicker({
        locale: {
            format: 'YYYY-MM-DD',
            separator: ' - ',
            applyLabel: '{% trans "تطبيق" %}',
            cancelLabel: '{% trans "إلغاء" %}',
            fromLabel: '{% trans "من" %}',
            toLabel: '{% trans "إلى" %}',
            customRangeLabel: '{% trans "نطاق مخصص" %}',
            weekLabel: 'W',
            daysOfWeek: ['أح', 'إث', 'ث', 'أر', 'خ', 'ج', 'س'],
            monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            firstDay: 6
        },
        ranges: {
            '{% trans "اليوم" %}': [moment(), moment()],
            '{% trans "أمس" %}': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            '{% trans "آخر 7 أيام" %}': [moment().subtract(6, 'days'), moment()],
            '{% trans "آخر 30 يوم" %}': [moment().subtract(29, 'days'), moment()],
            '{% trans "هذا الشهر" %}': [moment().startOf('month'), moment().endOf('month')],
            '{% trans "الشهر الماضي" %}': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    }, function(start, end, label) {
        $('#start_date').val(start.format('YYYY-MM-DD'));
        $('#end_date').val(end.format('YYYY-MM-DD'));
    });

    // Export Functions
    $('#exportPdf').on('click', function() {
        exportReport('pdf');
    });

    $('#exportExcel').on('click', function() {
        exportReport('excel');
    });

    $('#exportCsv').on('click', function() {
        exportReport('csv');
    });

    function exportReport(format) {
        var $btn = $('#export' + format.charAt(0).toUpperCase() + format.slice(1));
        var originalText = $btn.html();

        // Show loading state
        $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>{% trans "جاري التصدير..." %}');
        $btn.prop('disabled', true);

        // Get current filters
        var params = new URLSearchParams();
        params.append('format', format);
        params.append('start_date', $('#start_date').val());
        params.append('end_date', $('#end_date').val());

        if ($('#category').val()) params.append('category', $('#category').val());
        if ($('#customer').val()) params.append('customer', $('#customer').val());
        if ($('#payment_status').val()) params.append('payment_status', $('#payment_status').val());
        if ($('#payment_method').val()) params.append('payment_method', $('#payment_method').val());
        if ($('#min_amount').val()) params.append('min_amount', $('#min_amount').val());
        if ($('#max_amount').val()) params.append('max_amount', $('#max_amount').val());
        if ($('#sort_by').val()) params.append('sort_by', $('#sort_by').val());
        if ($('#globalSearch').val()) params.append('search', $('#globalSearch').val());

        // Create download link
        var exportUrl = '{% url "reports:export_sales_report" %}?' + params.toString();

        // Create temporary link and click it
        var link = document.createElement('a');
        link.href = exportUrl;
        link.download = 'sales_report_' + new Date().toISOString().split('T')[0] + '.' + format;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button state
        setTimeout(function() {
            $btn.html(originalText);
            $btn.prop('disabled', false);

            // Show success message
            showAlert('success', '{% trans "تم تصدير التقرير بنجاح" %}');
        }, 2000);
    }

    // Refresh Data
    $('#refreshData').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.html();

        $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>{% trans "جاري التحديث..." %}');
        $btn.prop('disabled', true);

        // Reload page with current filters
        setTimeout(function() {
            location.reload();
        }, 1000);
    });

    // Reset Filters
    window.resetFilters = function() {
        $('#filterForm')[0].reset();
        $('#daterange').val('');
        $('#globalSearch').val('');
        salesTable.search('').draw();

        // Reset checkboxes
        $('#showPaid, #showUnpaid, #showPartial').prop('checked', true);

        showAlert('info', '{% trans "تم إعادة تعيين جميع الفلاتر" %}');
    };

    // Update Results Count
    function updateResultsCount() {
        var info = salesTable.page.info();
        $('#resultsCount').text(info.recordsDisplay);
    }

    // Show Alert Function
    function showAlert(type, message) {
        var alertClass = type === 'success' ? 'alert-success' :
                        type === 'error' ? 'alert-danger' :
                        type === 'warning' ? 'alert-warning' : 'alert-info';

        var alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' :
                                  type === 'error' ? 'exclamation-triangle' :
                                  type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        $('body').append(alertHtml);

        // Auto remove after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Form submission with loading state
    $('#filterForm').on('submit', function() {
        var $submitBtn = $(this).find('button[type="submit"]');
        var originalText = $submitBtn.html();

        $submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>{% trans "جاري التطبيق..." %}');
        $submitBtn.prop('disabled', true);

        return true;
    });

    // Initialize page
    updateResultsCount();

    // Auto-refresh every 5 minutes (optional)
    // setInterval(function() {
    //     if (confirm('{% trans "هل تريد تحديث البيانات؟" %}')) {
    //         location.reload();
    //     }
    // }, 300000); // 5 minutes
});
</script>
{% endblock %}
