<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #{{ sale.invoice_number }} - ملونة</title>
    {% load static %}
    {% load sales_extras %}
    
    <style>
        /* إعدادات أساسية للطباعة الملونة */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        
        html {
            font-size: 16px;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        body {
            font-family: 'Tajawal', 'Tahoma', 'Arial Unicode MS', sans-serif;
            font-size: 14pt;
            line-height: 1.6;
            color: #000;
            background: white;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20mm;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 0;
        }
        
        /* العناوين بألوان قوية */
        h1 { 
            font-size: 28pt; 
            font-weight: bold; 
            margin-bottom: 15px; 
            text-align: center;
            color: #0d47a1 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        h2 { 
            font-size: 24pt; 
            font-weight: bold; 
            margin-bottom: 12px; 
            text-align: center;
            color: #1565c0 !important;
        }
        h3, h4, h5 { 
            color: #1976d2 !important;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        /* الجداول بألوان قوية - استخدام الحدود بدلاً من الخلفيات */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            font-size: 13pt;
            border: 6px solid #0d47a1 !important;
        }

        th, td {
            padding: 12px 8px;
            border: 3px solid #1565c0 !important;
            text-align: center;
            vertical-align: middle;
            font-size: 13pt;
        }

        th {
            /* استخدام الحدود والنصوص بدلاً من الخلفيات */
            border: 4px solid #0d47a1 !important;
            color: #0d47a1 !important;
            font-weight: 900 !important;
            font-size: 14pt !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            /* إضافة نمط مميز بدون خلفية */
            position: relative !important;
        }

        /* إضافة نمط للخلايا */
        th::before {
            content: "▓▓▓" !important;
            color: #0d47a1 !important;
            font-size: 8pt !important;
            position: absolute !important;
            top: 2px !important;
            left: 2px !important;
        }
        
        /* صفوف الجدول بألوان متناوبة */
        tbody tr:nth-child(even) {
            background-color: #e3f2fd !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        tbody tr:nth-child(odd) {
            background-color: #f8f9fa !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        /* رأس الفاتورة بحدود قوية بدلاً من الخلفيات */
        .invoice-header {
            border: 8px solid #0d47a1 !important;
            color: #0d47a1 !important;
            padding: 30px !important;
            margin-bottom: 30px;
            text-align: center;
            border-radius: 15px !important;
            /* إضافة نمط مميز بدون خلفية */
            position: relative !important;
        }

        .invoice-header::before {
            content: "████████████████████████████████████████" !important;
            color: #0d47a1 !important;
            font-size: 6pt !important;
            position: absolute !important;
            top: 5px !important;
            left: 5px !important;
            right: 5px !important;
            opacity: 0.3 !important;
        }

        .invoice-header h1,
        .invoice-header h2,
        .invoice-header p {
            color: #0d47a1 !important;
            font-weight: 900 !important;
            text-transform: uppercase !important;
            letter-spacing: 2px !important;
        }
        
        /* المجموع الكلي بحدود قوية */
        .total-amount {
            font-size: 20pt !important;
            font-weight: 900 !important;
            color: #d32f2f !important;
            border: 6px solid #d32f2f !important;
            padding: 15px 20px !important;
            border-radius: 10px !important;
            text-transform: uppercase !important;
            letter-spacing: 3px !important;
            text-align: center !important;
            position: relative !important;
        }

        .total-amount::before {
            content: "▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓" !important;
            color: #d32f2f !important;
            font-size: 8pt !important;
            position: absolute !important;
            top: 3px !important;
            left: 3px !important;
            right: 3px !important;
            opacity: 0.3 !important;
        }
        
        /* معلومات الدفع بحدود قوية */
        .payment-info {
            border: 5px solid #4caf50 !important;
            color: #2e7d32 !important;
            padding: 20px !important;
            margin-bottom: 25px;
            border-radius: 10px !important;
            position: relative !important;
        }

        .payment-info::before {
            content: "▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓" !important;
            color: #4caf50 !important;
            font-size: 8pt !important;
            position: absolute !important;
            top: 5px !important;
            left: 5px !important;
            right: 5px !important;
            opacity: 0.2 !important;
        }

        .payment-info h5,
        .payment-info dt,
        .payment-info dd {
            color: #2e7d32 !important;
            font-weight: 900 !important;
            text-transform: uppercase !important;
        }
        
        /* ملخص الفاتورة بحدود قوية */
        .invoice-summary {
            border: 5px solid #ff9800 !important;
            color: #e65100 !important;
            padding: 25px !important;
            margin-bottom: 30px;
            border-radius: 10px !important;
            position: relative !important;
        }

        .invoice-summary::before {
            content: "▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓" !important;
            color: #ff9800 !important;
            font-size: 8pt !important;
            position: absolute !important;
            top: 5px !important;
            left: 5px !important;
            right: 5px !important;
            opacity: 0.2 !important;
        }
        
        .invoice-summary span,
        .invoice-summary strong {
            color: white !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        /* الصفوف */
        .row {
            width: 100%;
            margin-bottom: 20px;
            display: table;
            clear: both;
        }
        
        .col-md-6 {
            width: 50%;
            float: right;
            padding: 0 15px;
            display: table-cell;
            vertical-align: top;
        }
        
        /* قوائم التعريف */
        dt {
            font-weight: bold;
            font-size: 14pt;
            margin-bottom: 8px;
            display: inline-block;
            width: 40%;
            vertical-align: top;
            color: #1976d2 !important;
        }
        
        dd {
            font-size: 14pt;
            margin-bottom: 12px;
            display: inline-block;
            width: 58%;
            vertical-align: top;
            padding-right: 10px;
        }
        
        /* الأرقام */
        .arabic-number {
            font-family: 'Tajawal', 'Tahoma', monospace;
            font-weight: 600;
            direction: ltr;
            display: inline-block;
        }
        
        /* أزرار التحكم */
        .print-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .print-controls button {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12pt;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .btn-print {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .btn-close {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        /* إعدادات الطباعة */
        @page {
            size: A4;
            margin: 15mm;
        }
        
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            body {
                padding: 0;
                margin: 0;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .print-controls {
                display: none !important;
            }

            .invoice-container {
                max-width: none;
                width: 100%;
            }

            /* تأكيد الحدود في الطباعة */
            table, th, td {
                border: 3px solid #000 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            th {
                border: 4px solid #000 !important;
                font-weight: 900 !important;
                color: #000 !important;
            }

            .invoice-header,
            .payment-info,
            .invoice-summary,
            .total-amount {
                border: 5px solid #000 !important;
                color: #000 !important;
                font-weight: 900 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <!-- أزرار التحكم -->
    <div class="print-controls">
        <button class="btn-print" onclick="window.print()">🖨️ طباعة ملونة</button>
        <button class="btn-close" onclick="window.close()">❌ إغلاق</button>
    </div>

    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            {% if company_info %}
                <h1>{{ company_info.name }}</h1>
                {% if company_info.address %}
                    <p>{{ company_info.address }}</p>
                {% endif %}
                {% if company_info.phone %}
                    <p>هاتف: {{ company_info.phone }}</p>
                {% endif %}
            {% else %}
                <h1>فاتورة مبيعات</h1>
            {% endif %}
            <h2>فاتورة #{{ sale.invoice_number }}</h2>
            <p><strong>التاريخ:</strong> {{ sale.date|date:"Y-m-d H:i" }}</p>
        </div>

        <!-- معلومات البيع والعميل -->
        <div class="row">
            <div class="col-md-6">
                <h5>معلومات العميل</h5>
                <dl>
                    <dt>الاسم:</dt>
                    <dd>{{ sale.customer.name }}</dd>
                    {% if sale.customer.phone %}
                    <dt>الهاتف:</dt>
                    <dd class="arabic-number">{{ sale.customer.phone }}</dd>
                    {% endif %}
                    {% if sale.customer.email %}
                    <dt>البريد:</dt>
                    <dd>{{ sale.customer.email }}</dd>
                    {% endif %}
                </dl>
            </div>
            <div class="col-md-6">
                <h5>معلومات البيع</h5>
                <dl>
                    <dt>الموظف:</dt>
                    <dd>{{ sale.employee.get_full_name }}</dd>
                    <dt>طريقة الدفع:</dt>
                    <dd>{{ sale.get_payment_method_display }}</dd>
                    <dt>الحالة:</dt>
                    <dd>{{ sale.get_status_display }}</dd>
                </dl>
            </div>
        </div>

        <!-- عناصر البيع -->
        <h4>المنتجات</h4>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.product.name }}</td>
                    <td class="arabic-number">{{ item.quantity }}</td>
                    <td class="arabic-number">{{ item.unit_price|floatformat:2 }} د.م</td>
                    <td class="arabic-number">{{ item.subtotal|floatformat:2 }} د.م</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- الملخص المالي -->
        <div class="row">
            <div class="col-md-6">
                <div class="payment-info">
                    <h5>معلومات الدفع</h5>
                    <dl>
                        <dt>المجموع المدفوع:</dt>
                        <dd class="arabic-number">{{ total_paid|floatformat:2 }} د.م</dd>
                        <dt>المبلغ المتبقي:</dt>
                        <dd class="arabic-number">{{ remaining_amount|floatformat:2 }} د.م</dd>
                    </dl>
                </div>
            </div>
            <div class="col-md-6">
                <div class="invoice-summary">
                    <div style="display: table; width: 100%; margin-bottom: 10px;">
                        <span style="display: table-cell; width: 70%; font-weight: 600; text-align: right;">المبلغ الفرعي:</span>
                        <span style="display: table-cell; width: 30%; text-align: left; font-weight: 700;" class="arabic-number">{{ sale.subtotal|floatformat:2 }} د.م</span>
                    </div>
                    {% if sale.tax_amount %}
                    <div style="display: table; width: 100%; margin-bottom: 10px;">
                        <span style="display: table-cell; width: 70%; font-weight: 600; text-align: right;">الضريبة:</span>
                        <span style="display: table-cell; width: 30%; text-align: left; font-weight: 700;" class="arabic-number">{{ sale.tax_amount|floatformat:2 }} د.م</span>
                    </div>
                    {% endif %}
                    {% if sale.discount %}
                    <div style="display: table; width: 100%; margin-bottom: 10px;">
                        <span style="display: table-cell; width: 70%; font-weight: 600; text-align: right;">الخصم:</span>
                        <span style="display: table-cell; width: 30%; text-align: left; font-weight: 700;" class="arabic-number">{{ sale.discount|floatformat:2 }} د.م</span>
                    </div>
                    {% endif %}
                    <hr style="border-color: white; margin: 15px 0;">
                    <div style="display: table; width: 100%;">
                        <strong style="display: table-cell; width: 70%; text-align: right;">المجموع الكلي:</strong>
                        <strong class="total-amount arabic-number" style="display: table-cell; width: 30%; text-align: left;">{{ sale.total_amount|floatformat:2 }} د.م</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- ذيل الفاتورة -->
        <div style="text-align: center; margin-top: 30px; font-size: 12pt; color: #1976d2; border-top: 3px solid #1976d2; padding-top: 15px; font-weight: bold;">
            <p>شكراً لتعاملكم معنا</p>
            <p><small>تم إنشاء هذه الفاتورة في {{ "now"|date:"Y-m-d H:i" }}</small></p>
        </div>
    </div>

    <script>
        // فتح نافذة الطباعة تلقائياً مع ضمان الألوان
        window.onload = function() {
            // تطبيق إعدادات طباعة الألوان على جميع العناصر
            var elements = document.querySelectorAll('*');
            elements.forEach(function(element) {
                element.style.webkitPrintColorAdjust = 'exact';
                element.style.printColorAdjust = 'exact';
                element.style.colorAdjust = 'exact';
            });
            
            // إضافة CSS إضافي لضمان الألوان والحدود
            var style = document.createElement('style');
            style.innerHTML = `
                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }

                    table, th, td {
                        border: 3px solid #000 !important;
                        border-collapse: collapse !important;
                    }

                    th {
                        border: 4px solid #000 !important;
                        font-weight: 900 !important;
                        background: #f0f0f0 !important;
                        color: #000 !important;
                    }

                    .invoice-header,
                    .payment-info,
                    .invoice-summary,
                    .total-amount {
                        border: 5px solid #000 !important;
                        color: #000 !important;
                        font-weight: 900 !important;
                        background: #f8f8f8 !important;
                    }
                }
            `;
            document.head.appendChild(style);

            // إضافة حدود قوية لجميع الجداول
            var tables = document.querySelectorAll('table');
            tables.forEach(function(table) {
                table.style.border = '4px solid #000';
                table.style.borderCollapse = 'collapse';

                var cells = table.querySelectorAll('th, td');
                cells.forEach(function(cell) {
                    cell.style.border = '2px solid #000';
                    if (cell.tagName === 'TH') {
                        cell.style.border = '3px solid #000';
                        cell.style.fontWeight = '900';
                        cell.style.backgroundColor = '#f0f0f0';
                    }
                });
            });
            
            // فتح نافذة الطباعة
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
