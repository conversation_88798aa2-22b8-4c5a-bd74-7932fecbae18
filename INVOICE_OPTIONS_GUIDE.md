# دليل خيارات الفاتورة المحسنة

## المشكلة التي تم حلها
كانت هناك مشاكل في عرض المعلومات عند اختيار أحجام ورق مختلفة، خاصة الإيصال الحراري (80 مم) الذي كان يظهر معلومات ناقصة.

## الحلول المطبقة

### 1. تحسين CSS للأحجام المختلفة
- **A4**: الحجم الافتراضي مع عرض كامل للمعلومات
- **A5**: حجم مضغوط مع تنسيق محسن
- **إيصال حراري (80مم)**: تنسيق خاص للطابعات الحرارية

### 2. نظام إخفاء/إظهار ذكي
- **فئات CSS ديناميكية**: تطبق حسب الإعدادات المختارة
- **دعم جميع الأحجام**: كل حجم ورق يدعم جميع الخيارات
- **حفظ الإعدادات**: تحفظ في localStorage للاستخدام المستقبلي

## خيارات حجم الورق

### 📄 A4 (الافتراضي)
- **الأبعاد**: 210 × 297 مم
- **الاستخدام**: الطباعة العادية والرسمية
- **المميزات**: 
  - عرض كامل لجميع المعلومات
  - تنسيق احترافي
  - مناسب للأرشفة

### 📄 A5 (مضغوط)
- **الأبعاد**: 148 × 210 مم  
- **الاستخدام**: توفير الورق والطباعة السريعة
- **المميزات**:
  - خط أصغر (11pt)
  - مسافات مضغوطة
  - جميع المعلومات متاحة

### 🧾 إيصال حراري (80مم)
- **الأبعاد**: 80 مم عرض × طول تلقائي
- **الاستخدام**: الطابعات الحرارية ونقاط البيع
- **المميزات**:
  - تنسيق عمودي
  - خط صغير (9-10pt)
  - تحسين للطباعة السريعة

## خيارات المحتوى

### 🖼️ عرض الشعار
- **الوصف**: إظهار/إخفاء شعار الشركة
- **A4**: شعار كامل الحجم
- **A5**: شعار متوسط (60px)
- **حراري**: شعار صغير (40px) أو مخفي

### 👤 معلومات العميل
- **الوصف**: بيانات العميل (الاسم، الهاتف، العنوان)
- **جميع الأحجام**: متاح مع تنسيق مناسب لكل حجم

### 🏢 معلومات الشركة  
- **الوصف**: بيانات الشركة (الاسم، الهاتف، العنوان، الرقم الضريبي)
- **جميع الأحجام**: متاح مع تنسيق مناسب

### ✍️ منطقة التوقيع
- **الوصف**: مساحات للتوقيعات والأختام
- **A4**: ثلاث مناطق توقيع
- **A5**: مناطق توقيع مضغوطة  
- **حراري**: منطقة توقيع واحدة أو مخفية

### 📋 الشروط والأحكام
- **الوصف**: قائمة بشروط البيع
- **A4**: نص كامل (9pt)
- **A5**: نص مضغوط (7pt)
- **حراري**: نص صغير جداً (7pt) أو مخفي

### 📱 رمز الاستجابة السريعة (QR)
- **الوصف**: رمز QR للتحقق من الفاتورة
- **A4**: حجم كامل (150×150px)
- **A5**: حجم متوسط (100×100px)
- **حراري**: حجم صغير (50×50px) أو مخفي

### 💳 معلومات الدفع
- **الوصف**: طريقة الدفع وحالة السداد
- **جميع الأحجام**: متاح مع تنسيق مناسب

## كيفية الاستخدام

### 1. اختيار حجم الورق
```
1. اضغط على زر "طباعة" ▼
2. اختر من قسم "حجم الورق":
   - A4 (للطباعة العادية)
   - A5 (للتوفير)  
   - إيصال حراري (للطابعات الحرارية)
```

### 2. تخصيص المحتوى
```
1. في نفس القائمة، اختر من "محتوى الفاتورة":
   ☑️ عرض الشعار
   ☑️ معلومات العميل  
   ☑️ معلومات الشركة
   ☑️ منطقة التوقيع
   ☑️ الشروط والأحكام
   ☑️ رمز QR
   ☑️ معلومات الدفع
```

### 3. حفظ الإعدادات
- **تلقائي**: تحفظ الإعدادات تلقائياً في المتصفح
- **إعادة تعيين**: زر "إعادة تعيين الإعدادات" لاستعادة الافتراضي

## أمثلة الاستخدام

### 📋 فاتورة رسمية (A4)
```
✅ جميع الخيارات مفعلة
✅ حجم A4
✅ مناسبة للأرشفة والمراسلات الرسمية
```

### 🏪 فاتورة سريعة (A5)  
```
✅ الشعار + معلومات العميل + معلومات الدفع
❌ التوقيع + الشروط + QR
✅ حجم A5
✅ مناسبة للبيع السريع
```

### 🧾 إيصال نقطة البيع (حراري)
```
✅ معلومات العميل + معلومات الدفع
❌ الشعار + التوقيع + الشروط + QR  
✅ حجم 80مم
✅ مناسب للطابعات الحرارية
```

## نصائح للاستخدام الأمثل

### 🎯 للطباعة العادية
- استخدم **A4** مع جميع الخيارات
- مناسب للفواتير الرسمية والأرشفة

### ⚡ للطباعة السريعة  
- استخدم **A5** مع الخيارات الأساسية
- يوفر الورق والوقت

### 🏪 لنقاط البيع
- استخدم **الإيصال الحراري** مع الحد الأدنى من المعلومات
- مناسب للطابعات الحرارية

### 💾 حفظ الإعدادات
- الإعدادات تحفظ تلقائياً لكل متصفح
- استخدم "إعادة تعيين" عند الحاجة للإعدادات الافتراضية

## استكشاف الأخطاء

### ❌ المعلومات ناقصة في الإيصال الحراري
**الحل**: تأكد من تفعيل الخيارات المطلوبة في قائمة "محتوى الفاتورة"

### ❌ الخط صغير جداً
**الحل**: غير حجم الورق إلى A5 أو A4

### ❌ الإعدادات لا تحفظ
**الحل**: تأكد من تفعيل localStorage في المتصفح

### ❌ التنسيق غير صحيح عند الطباعة
**الحل**: استخدم "معاينة الطباعة" قبل الطباعة الفعلية

## التحديثات المطبقة

### ✅ إصلاحات CSS
- تحسين عرض الإيصال الحراري
- دعم أفضل لـ A5
- نظام فئات ديناميكي للإخفاء/الإظهار

### ✅ تحسينات JavaScript  
- معالجة أفضل للإعدادات
- حفظ تلقائي للخيارات
- تطبيق فوري للتغييرات

### ✅ دعم جميع الخيارات
- كل حجم ورق يدعم جميع خيارات المحتوى
- تنسيق مناسب لكل حجم
- إخفاء ذكي للعناصر غير المناسبة
