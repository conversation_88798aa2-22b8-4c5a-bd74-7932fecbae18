// تهيئة القائمة العلوية
document.addEventListener('DOMContentLoaded', function() {
    // انتظار تحميل Bootstrap بالكامل
    setTimeout(function() {
        initializeNavbarDropdowns();
    }, 100);
});

// تهيئة القوائم المنسدلة في الشريط العلوي
function initializeNavbarDropdowns() {
    // البحث عن جميع القوائم المنسدلة
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle[data-bs-toggle="dropdown"]');

    dropdownToggles.forEach(function(toggle) {
        // إزالة أي مستمعات سابقة
        toggle.removeEventListener('click', handleDropdownClick);

        // إضافة مستمع جديد
        toggle.addEventListener('click', handleDropdownClick);

        // تهيئة Bootstrap dropdown إذا لم يكن موجوداً
        if (!bootstrap.Dropdown.getInstance(toggle)) {
            new bootstrap.Dropdown(toggle);
        }
    });
}

// معالج النقر على القوائم المنسدلة
function handleDropdownClick(event) {
    event.preventDefault();
    event.stopPropagation();

    const toggle = event.currentTarget;
    const dropdownInstance = bootstrap.Dropdown.getInstance(toggle);

    if (dropdownInstance) {
        dropdownInstance.toggle();
    } else {
        // إنشاء instance جديد إذا لم يكن موجوداً
        const newDropdown = new bootstrap.Dropdown(toggle);
        newDropdown.toggle();
    }
}

// إغلاق القوائم المنسدلة عند النقر خارجها
document.addEventListener('click', function(event) {
    const dropdownMenus = document.querySelectorAll('.dropdown-menu.show');
    dropdownMenus.forEach(function(menu) {
        const toggle = menu.previousElementSibling;
        if (toggle && !toggle.contains(event.target) && !menu.contains(event.target)) {
            const dropdownInstance = bootstrap.Dropdown.getInstance(toggle);
            if (dropdownInstance) {
                dropdownInstance.hide();
            }
        }
    });
});

// إعادة تهيئة القوائم المنسدلة عند الحاجة
window.reinitializeDropdowns = function() {
    initializeNavbarDropdowns();
};
